# Don't check auto-generated stuff into git
coverage
build
node_modules
stats.json
.pnp
.pnp.js

# misc
.DS_Store
npm-debug.log*

# yarn
yarn-debug.log*
yarn-error.log*
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# env
.env
.env.development.local
.env.test.local
.env.production.local

# boilerplate internals
generated-cra-app
.cra-template-rb
template
.eslintcache
