export const convertString = (str: string) => {
  if (str?.length < 4) {
    return str; // Tr<PERSON> về chuỗi không thay đổi nếu độ dài chuỗi ít hơn 4 ký tự
  }

  const prefix = str?.substring(0, 3); // L<PERSON>y phần đầu của chuỗi, bao gồm 3 ký tự đầu
  const suffix = str?.substring(3).replace(/./g, '*'); // Thay thế các ký tự từ vị trí thứ 4 trở đi bằng '*'

  return prefix + suffix; // Kết hợp phần đầu và phần cuối đã thay đổi
};
