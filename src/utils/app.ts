/* eslint-disable array-callback-return */
export const filterArrayByProperty = (array: any[], propertyName: string) => {
  const properties = propertyName.split(' ');
  const result: any = [];
  properties.map((property: string) => {
    array.map((element: any) => {
      const foundIndex = result.findIndex(
        (e: any) => e[property] === element[property],
      );

      if (foundIndex < 0) {
        result.push(element);
      }
    });
  });

  return result;
};

export const readAsDataURL = (file: any) => {
  return new Promise((resolve, reject) => {
    const fr = new FileReader();
    fr.onerror = reject;
    fr.onload = function () {
      resolve(fr.result);
    };

    fr.readAsDataURL(file);
  });
};
