import AWS, { AWSError, S3 } from 'aws-sdk';
import get from 'lodash/get';
// import stream from 'stream';

class S3Client {
  client: AWS.S3;

  bucketName: string;

  static instance: S3Client;

  constructor() {
    this.client = new AWS.S3({
      accessKeyId: process.env.REACT_APP_AWS_S3_ACCESS_KEY || '',
      secretAccessKey: process.env.REACT_APP_AWS_S3_SECRET_KEY || '',
    });

    this.bucketName = process.env.REACT_APP_AWS_S3_BUCKET_NAME || '';
  }

  static getInstance() {
    if (!S3Client.instance) {
      S3Client.instance = new S3Client();
    }
    return S3Client.instance;
  }

  async uploadFile(key: string, body: any) {
    try {
      const response = await this.client
        .upload({
          Key: key,
          Bucket: this.bucketName,
          Body: body,
          ContentType: body.type,
        })
        .promise();
      return response;

      //const link = get(response, 'Location');
    } catch (error) {
      throw new Error(get(error, 'message'));
    }
  }

  getHeadObject(key: string) {
    const params = {
      Key: key,
      Bucket: this.bucketName,
    };

    return new Promise((reslove, reject) => {
      this.client.headObject(
        params,
        (err: AWSError, headers: S3.HeadObjectOutput) => {
          if (err) {
            return reject(err);
          }

          return reslove(headers);
        },
      );
    });
  }

  getFile(key: string) {
    return this.client
      .getObject({
        Key: key,
        Bucket: this.bucketName,
      })
      .promise();
  }

  getFileStream(key: string) {
    return this.client
      .getObject({
        Key: key,
        Bucket: this.bucketName,
      })
      .createReadStream();
  }

  deleteFile(key: string) {
    return this.client
      .deleteObject({
        Key: key,
        Bucket: this.bucketName,
      })
      .promise();
  }

  createReadS3Stream(Key: string) {
    return this.client
      .getObject({
        Bucket: this.bucketName,
        Key,
      })
      .createReadStream();
  }

  // write S3 file
  // createWriteS3Stream(Key: string) {
  //   const writeStream = new stream.PassThrough();
  //   const uploadPromise = this.client
  //     .upload({
  //       Bucket: this.bucketName,
  //       Key,
  //       Body: writeStream,
  //     })
  //     .promise();
  //   return { writeStream, uploadPromise };
  // }
}

export default S3Client.getInstance();
