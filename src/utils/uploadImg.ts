import axios from 'axios';

const IMG_URL = process.env.REACT_APP_IMG_URL;

export const uploadFiles = async (files: any[], path: string, group: any) => {
  // const token = localStorage.getItem('TOKEN');
  const formData = new FormData();
  if (group) formData.append('group', group);
  if (path) formData.append('path', path);
  files.forEach(file => formData.append('file', file));

  try {
    const response = await axios.post(`${IMG_URL}/file/img`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        // Authorization: `Bearer ${token}`,
      },
    });
    console.log('upload response: ', response.data.data.url);
    return response.data.data.url;
  } catch (error) {
    console.error('Error uploading file:', error);
  }
};
