import { createGlobalStyle } from 'styled-components';

export const GlobalStyle = createGlobalStyle`
  html,
  body {
    height: 100%;
    width: 100%;
    font-size: 16px;
    font-family: "Lexend Deca", sans-serif;
  }

  /* body {
    overflow-x: hidden;
  }

  body::-webkit-scrollbar {
  width: 0.4rem;
  height: 0.5rem;
  display: none;
  }

  body:hover::-webkit-scrollbar {
  display: initial;
  } */

  /* body::-webkit-scrollbar-thumb {
  background-color: rgb(82 82 82);
  border-radius: 1rem;
  } */
  

  #root {
    min-height: 100%;
    min-width: 100%;
    font-family: "Lexend Deca", sans-serif;
  }

  a {
    line-height: 1.5em;
    font-size: inherit;
    font-family: "Lexend Deca", sans-serif;
  }
  p,
  label {
    line-height: 1.5em;
    font-size: inherit;
    font-family: "Lexend Deca", sans-serif;
  }

  input, select {
    font-family: inherit;
    font-size: inherit;
  }


  .zoom {
    transform: 'scale(2)';
    transition: 'opacity .2s ease-in-out,transform 1.2s ease-in-out';
  }
  .zoom:hover {
    transform: 'scale(1.15)';
    transition: 'opacity .2s ease-in-out,transform 1.2s ease-in-out';
  }
  .scrollbar-hidden::-webkit-scrollbar {
  width: 0.1em; 
  height: 0.5em;
  /* background-color: green; */
}
.two-line-typography {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-height: calc(var(--lh) * var(--max-lines));
  overflow: hidden;
}

/* .swiper-button-next, .swiper-button-prev {
    background-color: #F09600;
    height: 6px;
    width: 6px;
    border-radius: 36px;
 }  */

 /* .swiper-button-prev {
    padding-left: -200px;
 }  */

/* @keyframes getcoin {
  0% {
    transform: scale(5) translate(-1000px, -1000px);
  }
  10% {
    transform: scale(3) translate(-500px, -500px);
  }
  15% {
    transform: scale(2) translate(-200px, -200px);
  }
  25% {
    transform: scale(1.75) translate(-100px, -150px);
  }
  50% {
    transform: scale(1.5) translate(-50px, -100px);
  }
  75% {
    transform: scale(1.25) translate(-50, -50px);
  }
  100% {
    transform: scale(1);
    display: none;
  }
} */
`;
