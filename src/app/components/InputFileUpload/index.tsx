import * as React from 'react';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { uploadFiles } from 'utils/uploadImg';
import Box from '@mui/material/Box';
import LoadingButton from '@mui/lab/LoadingButton';
import { set } from 'lodash';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

interface Files {
  url: string;
  name: string;
}
// Define props type
interface InputFileUploadProps {
  path: string;
  onFilesSelected?: (files: Files[]) => void; // Callback to send file list to parent
}

const InputFileUpload: React.FC<InputFileUploadProps> = ({
  path,
  onFilesSelected,
}) => {
  const [fileNames, setFileNames] = React.useState<
    { url: string; name: string }[]
  >([]);
  const [loading, setLoading] = React.useState(false);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    if (files) {
      setLoading(true);
      const fileList: Files[] = await Promise.all(
        Array.from(files).map(async file => {
          const url = await uploadFiles([file], path, '');
          const name = file.name;
          return { url, name };
        }),
      );
      setLoading(false);
      setFileNames(fileList);

      // Call the callback if provided
      if (onFilesSelected) {
        onFilesSelected(fileList);
      }
    }
  };

  return (
    <div>
      <Button
        component="label"
        variant="contained"
        startIcon={<CloudUploadIcon />}
        sx={{ display: loading ? 'none' : 'flex', width: 'fit-content' }}
      >
        Upload files
        <VisuallyHiddenInput type="file" onChange={handleFileChange} multiple />
      </Button>
      <LoadingButton
        loading
        variant="outlined"
        sx={{ display: loading ? 'flex' : 'none', width: 'fit-content' }}
      >
        Loading...
      </LoadingButton>
      {fileNames.length > 0 && (
        <ul>
          <span>Files đã tải lên:</span>
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            {fileNames.map((name, index) => (
              <li key={index}>
                <img src={name.url} alt={name.name} width={100} height={100} />
              </li>
            ))}
          </Box>
        </ul>
      )}
    </div>
  );
};

export default InputFileUpload;
