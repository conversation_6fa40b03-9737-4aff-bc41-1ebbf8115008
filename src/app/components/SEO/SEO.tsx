import React from 'react';
// import { Helmet } from 'react-helmet-async';
// import featureImage from 'assets/images/product/type/product_type_5.webp';
// export default function SEO({ title, description, image, name, type, url }) {
//   return (
//     <Helmet>
//       {/* Standard metadata tags */}
//       <title>{title}</title>
//       <meta
//         property="og:image"
//         content={'https://phuonglinhjsc.vn' + featureImage}
//       />
//       <meta name="description" content={description} />
//       {/* End standard metadata tags */}
//       {/* Facebook tags */}
//       <meta property="og:type" content={type} />
//       <meta property="og:url" content={url} />
//       <meta property="og:title" content={title} />
//       <meta property="og:description" content={description} />
//       <meta property="og:image" content={image} />
//       {/* End Facebook tags */}
//       {/* Twitter tags */}
//       <meta name="twitter:creator" content={name} />
//       <meta name="twitter:card" content="article" />
//       <meta name="twitter:title" content={title} />
//       <meta name="twitter:description" content={description} />
//       {/* End Twitter tags */}
//     </Helmet>
//   );
// }
