import React from 'react';
import { GoogleMap, LoadScript, useJsApiLoader } from '@react-google-maps/api';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useEffect } from 'react';
import Box from '@mui/material/Box';
import { theme } from 'app/components/AppWrapper/theme';

const center = {
  lat: 10.797346,
  lng: 106.741398,
};

const Maps = () => {
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: 'AIzaSyBvB-Zlmc5zCxCVGZAu5BP2d55-b52PN84',
  });

  const [map, setMap] = React.useState(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const { width } = useWindowDimensions();
  useEffect(() => {
    if (width < 900) {
      setContainerWidth((90 / 100) * width);
    } else setContainerWidth((50 / 100) * width);
  }, [width]);
  const containerStyle = {
    width: containerWidth,
    height: '400px',
  };

  const onLoad = React.useCallback(function callback(map) {
    // This is just an example of getting and using the map instance!!! don't just blindly copy!
    const bounds = new window.google.maps.LatLngBounds(center);
    map.fitBounds(bounds);

    setMap(map);
  }, []);

  const onUnmount = React.useCallback(function callback(map) {
    setMap(null);
  }, []);

  return isLoaded ? (
    <Box
      sx={{
        mx: 'auto',
      }}
    >
      <GoogleMap
        mapContainerStyle={containerStyle}
        center={center}
        zoom={2}
        onLoad={onLoad}
        onUnmount={onUnmount}
      >
        {/* Child components, such as markers, info windows, etc. */}
        <></>
      </GoogleMap>
    </Box>
  ) : (
    <></>
  );
};

export default Maps;
