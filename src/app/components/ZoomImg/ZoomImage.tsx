import React, { useState, useRef, useEffect } from 'react';
import Box from '@mui/material/Box';

interface ZoomImageProps {
  src: string;
  alt: string;
  zoomFactor: number;
  style?: React.CSSProperties;
}

const ZoomImage: React.FC<ZoomImageProps> = ({
  src,
  alt,
  zoomFactor,
  style = {},
}) => {
  const [isZoomed, setIsZoomed] = useState<boolean>(false);
  const [display, setDisplay] = useState<string>('');
  const [zoomedPosition, setZoomedPosition] = useState<{
    x: number;
    y: number;
  }>({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState<number>(0);

  // Cập nhật kích thước dựa trên chiều rộng
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        setSize(width);
      }
    };

    // Khởi tạo kích thước
    updateSize();

    // Cập nhật kích thước khi resize
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Cleanup
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);

  const handleMouseEnter = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
  ) => {
    setIsZoomed(true);
    calculateZoomPosition(e);
    setDisplay('none');
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (isZoomed) {
      calculateZoomPosition(e);
    }
  };

  const handleMouseLeave = () => {
    setIsZoomed(false);
    setDisplay('');
  };

  const calculateZoomPosition = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
  ) => {
    const { left, top, width, height } =
      e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - left) / width;
    const y = (e.clientY - top) / height;
    setZoomedPosition({ x, y });
  };

  const calculateZoomedStyle = (): React.CSSProperties => {
    if (!isZoomed) return style;

    const { x, y } = zoomedPosition;
    const translateX = (-x * ((zoomFactor - 1) * 100)) / zoomFactor;
    const translateY = (-y * ((zoomFactor - 1) * 100)) / zoomFactor;

    return {
      ...style,
      transform: `scale(${zoomFactor}) translate(${translateX}%, ${translateY}%)`,
      transformOrigin: '0 0',
      zIndex: 999,
      width: '100%',
      height: '100%',
    };
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        height: `${size}px`, // Chiều cao sẽ bằng chiều rộng
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        overflow: 'hidden',
        boxShadow:
          '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
        '&:hover': {
          boxShadow:
            '0 14px 24px rgba(0, 0, 0, 0.55), 0 14px 18px rgba(0, 0, 0, 0.55)',
        },
      }}
      onMouseEnter={handleMouseEnter}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      <img
        src={src}
        alt={alt}
        style={{
          display,
          width: '100%',
          height: '100%',
          objectFit: 'cover',
        }}
        loading="lazy"
      />
      {isZoomed && <img src={src} alt={alt} style={calculateZoomedStyle()} />}
    </Box>
  );
};

export default ZoomImage;
