import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import PersonIcon from '@mui/icons-material/Person';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Face6Icon from '@mui/icons-material/Face6';
import ShoppingBagIcon from '@mui/icons-material/ShoppingBag';
import LogoutIcon from '@mui/icons-material/Logout';

interface Props {
  color?: string;
  icon?: any;
  style?: React.CSSProperties;
  onClickAccount?: () => void;
  onClickLogout?: () => void;
  onClickOrder?: () => void;
}

const Account: React.FC<Props> = ({
  icon,
  color,
  onClickAccount,
  onClickOrder,
  onClickLogout,
  style = {},
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        width: 'fit-content',
        height: 'fit-content',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        // alignItems: 'center',
        // borderRadius: '100%',
        overflow: 'hidden',
        '&:hover': {
          bgcolor: 'white',
          boxShadow: '',
        },
      }}
    >
      <Button
        id="basic-button-more"
        aria-controls={open ? 'basic-menu-more' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        // onMouseLeave={handleClose}
        sx={{
          display: 'flex',
          alignItems: 'center',
          color: color || 'white',
          textTransform: 'none',
          whiteSpace: 'nowrap',
          p: 0,
          m: 0,
          fontSize: '12px',
          fontWeight: 'normal',
          '&:hove': {
            bgcolor: 'white',
          },
        }}
      >
        {icon || <PersonIcon />}
        <KeyboardArrowDownIcon fontSize="small" />
      </Button>
      <Menu
        id="basic-menu-more"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button-more',
        }}
      >
        <MenuList>
          <MenuItem
            sx={{}}
            onClick={() => {
              onClickAccount && onClickAccount();
              handleClose();
            }}
          >
            <ListItemIcon>
              <Face6Icon fontSize="small" sx={{ color: 'green' }} />
            </ListItemIcon>
            <Typography>Tài khoản của tôi</Typography>
          </MenuItem>
          <MenuItem
            sx={{}}
            onClick={() => {
              onClickOrder && onClickOrder();
              handleClose();
            }}
          >
            <ListItemIcon>
              <ShoppingBagIcon fontSize="small" sx={{ color: 'orange' }} />
            </ListItemIcon>
            <Typography>Đơn hàng của tôi</Typography>
          </MenuItem>
          <MenuItem sx={{}} onClick={onClickLogout}>
            <ListItemIcon>
              <LogoutIcon fontSize="small" sx={{ color: 'red' }} />
            </ListItemIcon>
            <Typography>Đăng xuất</Typography>
          </MenuItem>
        </MenuList>
      </Menu>
    </Box>
  );
};

export default Account;
