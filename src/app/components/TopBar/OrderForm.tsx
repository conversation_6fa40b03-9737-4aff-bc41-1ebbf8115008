import React, { useState } from 'react';
import { Box, Button, TextField, Typography } from '@mui/material';

const OrderForm = () => {
  const [formValues, setFormValues] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    // console.log('Order Information:', formValues);
    // Bạn có thể thêm logic xử lý đặt hàng ở đây, như gửi thông tin đến server.
  };

  return (
    <Box
      component="form"
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '400px',
        margin: 'auto',
        mt: 4,
      }}
      onSubmit={handleSubmit}
    >
      <Typography variant="h4" component="h1" gutterBottom>
        Đặt Hàng
      </Typography>
      <TextField
        label="Họ và Tên"
        name="name"
        value={formValues.name}
        onChange={handleChange}
        required
      />
      <TextField
        label="Email"
        name="email"
        type="email"
        value={formValues.email}
        onChange={handleChange}
        required
      />
      <TextField
        label="Số Điện Thoại"
        name="phone"
        value={formValues.phone}
        onChange={handleChange}
        required
      />
      <TextField
        label="Địa Chỉ Giao Hàng"
        name="address"
        value={formValues.address}
        onChange={handleChange}
        required
      />
      <TextField
        label="Ghi Chú Đơn Hàng"
        name="notes"
        value={formValues.notes}
        onChange={handleChange}
        multiline
        rows={4}
      />
      <Button variant="contained" type="submit">
        Đặt Hàng
      </Button>
    </Box>
  );
};

export default OrderForm;
