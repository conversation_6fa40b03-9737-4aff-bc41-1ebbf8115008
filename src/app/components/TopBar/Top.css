
@keyframes count-down {
  0% {
    display: 'none';
  }
  10% {
    transform: scale(2, 2);
  }
  100% {
    transform: scale(1, 1);
  }
}
@keyframes count5s {
  0% {
    transform: scale(2, 2);
  }
  100% {
    transform: scale(1, 1);
  }
}
@keyframes getcoin2 {
  0% {
    display: none;
  }
  99% {
    display: none;
  }
  100% {
    display: block;
  }
}
@keyframes getcoin3 {
  0% {
    display: none;
  }
  99% {
    display: none;
  }
  100% {
    display: block;
  }
}
@keyframes getcoin {
  0% {
    transform: scale(5) translate(-1000px, -1000px);
  }
  10% {
    transform: scale(3) translate(-500px, -500px);
  }
  15% {
    transform: scale(2) translate(-200px, -200px);
  }
  25% {
    transform: scale(1.75) translate(-100px, -150px);
  }
  50% {
    transform: scale(1.5) translate(-50px, -100px);
  }
  75% {
    transform: scale(1.25) translate(-50, -50px);
  }
  100% {
    transform: scale(1);
    display: none;
  }
}
@keyframes chose-coin {
  0% {
    transform: scale(5) translate(-200px, 0);
  }
  100% {
    transform: scale(1)
  }
}
@keyframes increase-balance {
  0% {
    transform: scale(3) translate(0, -50px);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes decrease-balance {
  0% {
    transform: scale(3);
    opacity: 0;
  }
  100% {
    transform: scale(1) translate(0, -50px);
    opacity: 1;
  }
}
@keyframes spinned-balance {
  0% {
    transform: rotateX(3600deg);
    opacity: 0;
  }
  100% {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

@keyframes dot1 {
  0% {
    opacity: 1;
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes dot2 {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes dot3 {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes betting {
  0% {
    opacity: 0;
  }
  
  100% {
    opacity: 0.9;
  }
}
@keyframes spinning {
  0% {
    opacity: 0;
  }
  
  100% {
    opacity: 0.9;
  }
}
@keyframes center-spinning {
  0% {
    background-color: #ffbf36;
  }
  
  100% {
    background-color: #fe2121;
  }
}
@keyframes waitting-player {
  0% {
    opacity: 0;
  }
  
  100% {
    opacity: 0.9;
  }
}
@keyframes ready {
  0% {
    /* display: block; */
    opacity: 0.9;
    content: 'READY';
  }
  
  100% {
    /* display: none; */
    opacity: 0;
  }
}
@keyframes hidden {
  0% {
    display: none;
  }
  
  100% {
    display: none;
  }
}
@keyframes result {
  0% {
    opacity: 0;
  }
  
  100% {
    opacity: 1;
  }
}
@keyframes result-2part {
  0% {
    background-color: #fe2121;
  }
  
  100% {
    
  }
}
@keyframes result-5part {
  0% {
    background-color: #fe3434dd;
  }
  
  100% {
    
  }
}

@keyframes result2 {
  0% {
    opacity: 0;
  }
  
  100% {
    opacity: 1;
  }
}
@keyframes result-12 {
  0% {
    display: none;
    opacity: 0;
  }
  20% {
    display: none;
  }
  40% {
    display: block;
    opacity: 0;
    transform: scale(0.5);
  }
  
  100% {
    display: block;
    opacity: 1;
    transform: scale(2);
  }
}
@keyframes userChange {
  0% {
    font-size: 30px;
  }
  
  100% {
    font-size: 20px;
  }
}

/* .bet-chip {
  padding: 0px;
  height: 1px;
  background-color: #fe2121;
} */

/* .bet-option {
  padding: 20px 20px;
  
} */