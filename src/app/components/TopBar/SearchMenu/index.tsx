import React, { useState, useEffect, memo } from 'react';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Slide from '@mui/material/Slide';
import Fade from '@mui/material/Fade';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import ReceiptIcon from '@mui/icons-material/Receipt';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { TransitionProps } from '@mui/material/transitions';
import { alpha, useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { request } from 'utils/request';
import { useNavigate } from 'react-router-dom';

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="down" ref={ref} {...props} />;
});

interface Props {
  color?: string;
  icon?: any;
  style?: React.CSSProperties;
  onClickAccount?: () => void;
  onClickLogout?: () => void;
  onClickOrder?: () => void;
  // Thêm các props mới
  dialogOpen: boolean;
  onOpenDialog?: () => void;
  onCloseDialog?: () => void;
}

interface ProductImage {
  url: string;
  _id: string;
}

interface ProductVariant {
  color: string;
  price: number;
  images: ProductImage[];
  _id: string;
}

interface Category {
  _id: string;
  name: string;
  id: string;
}

interface Brand {
  _id: string;
  name: string;
  id: string;
}

interface Product {
  _id: string;
  id: string;
  name: string;
  images: ProductImage[];
  priceOrigin: number;
  price: number;
  variants?: ProductVariant[];
  categories?: Category[];
  brands?: Brand[];
  description?: string;
  createdAt: string;
}

const formatCurrency = (number: number) => {
  if (!number && number !== 0) return '';
  return number.toLocaleString('vi-VN', {
    style: 'currency',
    currency: 'VND',
  });
};

// Mẫu từ khóa tìm kiếm phổ biến
const popularSearches = [
  'Tai nghe bluetooth',
  'Baseus',
  'Tai nghe gaming',
  'Havit',
  'Chống ồn',
];

const SearchMenu: React.FC<Props> = React.memo(
  ({ dialogOpen, onOpenDialog, onCloseDialog }) => {
    const { t } = useTranslation();
    const theme = useTheme();

    const [open, setOpen] = useState(false);
    const [openSearch, setOpenSearch] = useState(false);
    // const [dialogOpen, setDialogOpen] = useState(false);
    const [options, setOptions] = useState<Product[]>([]);
    const [trendingProducts, setTrendingProducts] = useState<Product[]>([]);
    const [loading, setLoading] = useState(false);
    const [trendingLoading, setTrendingLoading] = useState(false);
    const [searchText, setSearchText] = useState<string>('');
    const [recentSearches, setRecentSearches] = useState<string[]>([]);
    const [searchInputFocused, setSearchInputFocused] = useState(false);

    const navigate = useNavigate();

    // Load recent searches from localStorage
    useEffect(() => {
      const saved = localStorage.getItem('recentSearches');
      if (saved) {
        try {
          setRecentSearches(JSON.parse(saved).slice(0, 5));
        } catch (e) {
          console.log('Failed to parse recent searches');
        }
      }
    }, []);

    // Fetch trending products (newest products) when dialog opens
    useEffect(() => {
      if (dialogOpen) {
        setOpenSearch(true);
        fetchTrendingProducts();
      } else {
        setOpenSearch(false);
      }
    }, [dialogOpen]);

    // Fetch trending products (newest products)
    const fetchTrendingProducts = async () => {
      setTrendingLoading(true);
      try {
        const response: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/products`,
          {
            limit: 6,
            sort: '-createdAt', // Sort by newest first
          },
        );
        setTrendingProducts(response.products || []);
      } catch (error) {
        console.error('Error fetching trending products:', error);
      } finally {
        setTrendingLoading(false);
      }
    };

    // Save recent search
    const saveRecentSearch = (term: string) => {
      if (!term.trim()) return;

      const updatedSearches = [
        term,
        ...recentSearches.filter(s => s !== term),
      ].slice(0, 5);

      setRecentSearches(updatedSearches);
      localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    };

    const fetchData = async (searchText: string) => {
      if (!searchText || searchText.trim().length < 2) {
        setLoading(false);
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const data: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/products`,
          {
            keyword: searchText,
            limit: 10,
          },
        );

        setOptions(data.products || []);
      } catch (error) {
        console.error('Error fetching products:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      if (!open) {
        setOptions([]);
      }
    }, [open]);

    const handleOpenDialog = () => {
      if (onOpenDialog) onOpenDialog();
      setSearchText('');
      setOptions([]);
    };

    const handleCloseDialog = () => {
      if (onCloseDialog) onCloseDialog();

      setSearchText('');
      setOptions([]);
      setOpen(false);
      setOpenSearch(false);
    };

    const handleSearch = () => {
      if (searchText.trim()) {
        saveRecentSearch(searchText);
        navigate(`/bo-suu-tap?keyword=${searchText}`);
        handleCloseDialog();
      }
    };

    const handleProductSelect = (product: Product) => {
      navigate(`/san-pham/${product.id}`);
      handleCloseDialog();
    };

    const handleKeywordClick = (keyword: string) => {
      setSearchText(keyword);
      fetchData(keyword);
    };

    // Function to get primary image URL
    const getProductImage = (product: Product): string => {
      if (product.images && product.images.length > 0) {
        return product.images[0].url;
      }
      return '';
    };

    // Function to get product price
    const getProductPrice = (product: Product): number => {
      if (product.price > 0) return product.price;
      return product.priceOrigin || 0;
    };

    const renderProductGrid = (
      products: Product[],
      isSearchResults = false,
    ) => {
      return (
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
            gap: 2,
            mt: isSearchResults ? 2 : 0,
          }}
        >
          {products.map(product => (
            <Paper
              key={product._id}
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                overflow: 'hidden',
                '&:hover': {
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                  transform: 'translateY(-4px)',
                  borderColor: alpha(theme.palette.primary.main, 0.3),
                },
              }}
              onClick={() => handleProductSelect(product)}
            >
              <Box
                sx={{
                  width: '100%',
                  paddingTop: '100%', // Tạo tỷ lệ 1:1 (chiều cao = chiều rộng)
                  borderRadius: 1,
                  mb: 1,
                  backgroundColor: alpha(theme.palette.divider, 0.1),
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                {getProductImage(product) ? (
                  <Box
                    component="img"
                    src={getProductImage(product)}
                    alt={product.name}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {t('search.product_image')}
                    </Typography>
                  </Box>
                )}

                {/* Badge cho sản phẩm mới */}
                {new Date(product.createdAt).getTime() >
                  Date.now() - 7 * 24 * 60 * 60 * 1000 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      right: 0,
                      backgroundColor: 'success.main',
                      color: 'white',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      px: 0.5,
                      borderRadius: '4px 0 0 0',
                    }}
                  >
                    NEW
                  </Box>
                )}
              </Box>

              <Typography
                variant="body1"
                sx={{
                  fontWeight: 500,
                  mb: 0.5,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  height: '48px',
                }}
              >
                {product.name}
              </Typography>

              <Typography
                variant="body2"
                sx={{ color: 'error.main', fontWeight: 'bold' }}
              >
                {getProductPrice(product) === 0
                  ? 'Liên hệ'
                  : formatCurrency(getProductPrice(product))}
              </Typography>

              {/* Hiển thị brand nếu có */}
              {product.brands && product.brands.length > 0 && (
                <Box sx={{ mt: 1 }}>
                  <Chip
                    label={product.brands[0].name}
                    size="small"
                    sx={{
                      textTransform: 'capitalize',
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    }}
                  />
                </Box>
              )}
            </Paper>
          ))}
        </Box>
      );
    };

    return (
      <>
        {/* <Box
          color="inherit"
          aria-label="search"
          onClick={handleOpenDialog}
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            py: 0.5,
            cursor: 'pointer',
            position: 'relative',
            '&:hover': {
              // background: alpha(theme.palette.primary.main, 0.08),
            },
            transition: 'all 0.2s ease-in-out',
          }}
        >
          <SearchIcon />
          <Typography
            sx={{
              fontSize: '14px',
              color: 'gray',
              fontWeight: 500,
              fontStyle: 'italic',
            }}
          >
            Tìm kiếm...
          </Typography>
        </Box> */}

        {/* <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          fullWidth
          maxWidth="lg"
          fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
          TransitionComponent={Transition}
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
              overflow: 'hidden',
              height: '90vh',
              maxHeight: '90vh',
              background: theme.palette.background.default,
            },
          }}
        >
        </Dialog> */}
        <Box
          sx={{
            position: 'relative',
            display: dialogOpen ? 'flex' : 'none',
            flexDirection: 'column',
            height: '100%',
            maxHeight: '100%',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              p: 2,
              position: 'sticky',
              top: 0,
              zIndex: 10,
              backdropFilter: 'blur(10px)',
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 600,
                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mr: 'auto',
                }}
              >
                {t('topbar.search')}
              </Typography>
            </Box>

            <Paper
              elevation={searchInputFocused ? 6 : 1}
              sx={{
                p: '2px 4px',
                display: 'flex',
                alignItems: 'center',
                borderRadius: 2,
                transition: 'all 0.3s ease',
                border: searchInputFocused
                  ? `2px solid ${theme.palette.primary.main}`
                  : `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              }}
            >
              <SearchIcon
                sx={{
                  mx: 1,
                  color: searchInputFocused
                    ? theme.palette.primary.main
                    : theme.palette.text.secondary,
                }}
              />
              <IconButton
                aria-label="close"
                onClick={handleCloseDialog}
                sx={{
                  position: 'absolute',
                  top: 2,
                  right: 2,
                  color: theme.palette.grey[500],
                  transition: 'all 0.2s',
                  '&:hover': {
                    color: theme.palette.primary.main,
                    transform: 'rotate(90deg)',
                  },
                }}
              >
                <CloseIcon />
              </IconButton>
              <Autocomplete
                open={open}
                freeSolo
                fullWidth
                disableClearable
                onOpen={() => setOpen(false)}
                onClose={() => setOpen(false)}
                inputValue={searchText}
                onFocus={() => setSearchInputFocused(true)}
                onBlur={() => setSearchInputFocused(false)}
                onChange={(_, value) => {
                  if (typeof value === 'string') {
                    setSearchText(value);
                  } else if (value && '_id' in value) {
                    handleProductSelect(value as Product);
                  }
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                getOptionLabel={option => {
                  if (typeof option === 'string') return option;
                  return option?.name || '';
                }}
                options={options}
                loading={loading}
                // noOptionsText={
                //   searchText
                //     ? t('search.no_results')
                //     : t('search.type_to_search')
                // }
                renderOption={(props, option) => (
                  <Box
                    component="li"
                    {...props}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      py: 1.5,
                      px: 2,
                      my: 0.5,
                      mx: 1,
                      borderRadius: 1,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        backgroundColor: alpha(
                          theme.palette.primary.main,
                          0.08,
                        ),
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        flexShrink: 0,
                        mr: 2,
                        position: 'relative',
                        overflow: 'hidden',
                        borderRadius: 1,
                        background: alpha(theme.palette.divider, 0.1),
                      }}
                    >
                      {getProductImage(option) && (
                        <Fade in timeout={500}>
                          <img
                            src={getProductImage(option)}
                            alt={option.name}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                            loading="lazy"
                          />
                        </Fade>
                      )}
                      {new Date(option.createdAt).getTime() >
                        Date.now() - 7 * 24 * 60 * 60 * 1000 && (
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            backgroundColor: 'success.main',
                            color: 'white',
                            fontSize: '11px',
                            fontWeight: 'bold',
                            px: 0.5,
                            borderRadius: '4px 0 0 0',
                          }}
                        >
                          NEW
                        </Box>
                      )}
                    </Box>
                    <Box
                      sx={{
                        flexGrow: 1,
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 500,
                          mb: 0.5,
                        }}
                      >
                        {option?.name}
                      </Typography>

                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'baseline',
                          mt: 0.5,
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'error.main',
                            fontWeight: 'bold',
                            fontSize: '1rem',
                          }}
                        >
                          {formatCurrency(getProductPrice(option))}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                )}
                renderInput={params => (
                  <TextField
                    {...params}
                    fullWidth
                    placeholder={`${t('topbar.search')}...`}
                    variant="standard"
                    autoFocus
                    value={searchText}
                    onChange={event => {
                      const value = event.target.value;
                      setSearchText(value);
                      if (value && value.length > 1) {
                        fetchData(value);
                      } else if (!value) {
                        setOptions([]);
                      }
                    }}
                    InputProps={{
                      ...params.InputProps,
                      disableUnderline: true,
                      endAdornment: (
                        <>
                          {loading ? (
                            <CircularProgress
                              color="primary"
                              size={20}
                              thickness={4}
                              sx={{ mr: 1 }}
                            />
                          ) : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
              />
            </Paper>
          </Box>

          <DialogContent
            sx={{
              flexGrow: 1,
              p: 0,
              overflow: 'auto',
              scrollbarWidth: 'thin',
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: alpha(theme.palette.primary.main, 0.2),
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: alpha(theme.palette.primary.main, 0.4),
              },
            }}
          >
            {/* Khi có kết quả tìm kiếm thì hiển thị chúng */}
            {searchText && options.length > 0 && (
              <Fade in timeout={500}>
                <Box sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <SearchIcon
                      sx={{
                        color: theme.palette.primary.main,
                        mr: 1,
                        fontSize: '1.2rem',
                      }}
                    />
                    <Typography variant="h6" sx={{ fontWeight: 500 }}>
                      {/* {t('search.results_for')} */}
                      Kết quả tìm kiếm cho "{searchText}" ({options.length})
                    </Typography>
                  </Box>

                  {renderProductGrid(options, true)}

                  {options.length > 6 && (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mt: 3,
                      }}
                    >
                      <Chip
                        icon={<SearchIcon />}
                        label={`Tìm kiếm tất cả cho "${searchText}"`}
                        clickable
                        color="primary"
                        onClick={handleSearch}
                        sx={{
                          px: 1,
                          borderRadius: '16px',
                          '&:hover': {
                            transform: 'scale(1.02)',
                          },
                          transition: 'all 0.2s',
                        }}
                      />
                    </Box>
                  )}
                </Box>
              </Fade>
            )}

            {/* Khi không có kết quả tìm kiếm nhưng đã nhập từ khóa */}
            {searchText && options.length === 0 && !loading && (
              <Box
                sx={{
                  p: 4,
                  textAlign: 'center',
                  color: 'text.secondary',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <SearchIcon
                  sx={{
                    fontSize: 60,
                    color: alpha(theme.palette.text.secondary, 0.3),
                    mb: 2,
                  }}
                />
                <Typography variant="h6">
                  {/* {t('search.no_results_for')} */}
                  Không tìm thấy kết quả cho "{searchText}"
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {/* {t('search.try_different_keywords')} */}
                  Hãy thử từ khóa khác
                </Typography>
              </Box>
            )}

            {/* Hiển thị màn hình chính khi không tìm kiếm */}
            {!searchText && (
              <Fade in timeout={500}>
                <Box sx={{ p: 3 }}>
                  {/* Tìm kiếm gần đây */}
                  {recentSearches.length > 0 && (
                    <Box sx={{ mb: 4 }}>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', mb: 2 }}
                      >
                        <ReceiptIcon
                          sx={{
                            color: theme.palette.primary.main,
                            mr: 1,
                            fontSize: '1.2rem',
                          }}
                        />
                        <Typography variant="h6" sx={{ fontWeight: 500 }}>
                          {/* {t('search.recent_searches')} */}
                          Tìm kiếm gần đây
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {recentSearches.map((term, index) => (
                          <Chip
                            key={index}
                            label={term}
                            onClick={() => handleKeywordClick(term)}
                            sx={{
                              borderRadius: '16px',
                              transition: 'all 0.2s',
                              '&:hover': {
                                backgroundColor: alpha(
                                  theme.palette.primary.main,
                                  0.1,
                                ),
                                transform: 'scale(1.05)',
                              },
                            }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {/* Từ khóa phổ biến */}
                  <Box sx={{ mb: 4 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <LocalOfferIcon
                        sx={{
                          color: theme.palette.primary.main,
                          mr: 1,
                          fontSize: '1.2rem',
                        }}
                      />
                      <Typography variant="h6" sx={{ fontWeight: 500 }}>
                        {/* {t('search.popular_searches')} */}
                        Từ khóa phổ biến
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {popularSearches.map((term, index) => (
                        <Chip
                          key={index}
                          label={term}
                          variant="outlined"
                          onClick={() => handleKeywordClick(term)}
                          sx={{
                            borderRadius: '16px',
                            transition: 'all 0.2s',
                            '&:hover': {
                              backgroundColor: alpha(
                                theme.palette.primary.main,
                                0.1,
                              ),
                              borderColor: theme.palette.primary.main,
                              transform: 'scale(1.05)',
                            },
                          }}
                        />
                      ))}
                    </Box>
                  </Box>

                  {/* Sản phẩm mới nhất - Lấy từ API thay vì dữ liệu mẫu */}
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <TrendingUpIcon
                        sx={{
                          color: theme.palette.primary.main,
                          mr: 1,
                          fontSize: '1.2rem',
                        }}
                      />
                      <Typography variant="h6" sx={{ fontWeight: 500 }}>
                        {/* {t('search.newest_products')} */}
                        Sản phẩm mới nhất
                      </Typography>
                    </Box>

                    {trendingLoading ? (
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          p: 4,
                        }}
                      >
                        <CircularProgress />
                      </Box>
                    ) : trendingProducts.length > 0 ? (
                      renderProductGrid(trendingProducts)
                    ) : (
                      <Typography
                        variant="body2"
                        sx={{
                          textAlign: 'center',
                          color: 'text.secondary',
                          p: 2,
                        }}
                      >
                        {/* {t('search.no_trending_products')} */}
                        Không có sản phẩm phân bộ
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Fade>
            )}
          </DialogContent>

          {searchText && options.length > 0 && (
            <Fade in timeout={300}>
              <Box
                sx={{
                  p: 2,
                  borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  backgroundColor: alpha(theme.palette.background.paper, 0.8),
                  backdropFilter: 'blur(10px)',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  {/* {t('search.press_enter')}  */}
                  Nhấn Enter để xem tất cả kết quả cho{' '}
                  {searchText && `"${searchText}"`}
                </Typography>
              </Box>
            </Fade>
          )}
        </Box>
      </>
    );
  },
);

export default SearchMenu;
