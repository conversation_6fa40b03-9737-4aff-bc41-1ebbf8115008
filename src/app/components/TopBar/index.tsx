/* eslint-disable jsx-a11y/alt-text */
import React, { useEffect, useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import SearchIcon from '@mui/icons-material/Search';
import Input from '@mui/material/Input';
import Badge, { BadgeProps } from '@mui/material/Badge';
import Toolbar from '@mui/material/Toolbar';
import Menu from '@mui/material/Menu';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import PhoneInTalkIcon from '@mui/icons-material/PhoneInTalk';
import FacebookIcon from '@mui/icons-material/Facebook';
import PhoneIcon from '@mui/icons-material/Phone';
import MailIcon from '@mui/icons-material/Mail';
import { SupportLanguages } from 'locales/i18n';
import MenuItem from '@mui/material/MenuItem';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import { theme } from 'app/components/AppWrapper/theme';
import PersonIcon from '@mui/icons-material/Person';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LanguageIcon from '@mui/icons-material/Language';
import ProductionQuantityLimitsIcon from '@mui/icons-material/ProductionQuantityLimits';
import { useDispatch, useSelector } from 'react-redux';
import { useUserSlice } from 'app/slice/user';
import { userInfoSelector } from 'app/slice/user/selectors';
import { useUtilsSlice } from 'app/slice/utils';
import { useNavigate } from 'react-router-dom';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';
import { keyframes } from '@emotion/react';
import Account from './Account';
import SearchMenu from './SearchMenu';
import Checkbox from '@mui/material/Checkbox';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import {
  Card,
  DialogActions,
  DialogTitle,
  FormControl,
  InputLabel,
  Select,
  useMediaQuery,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import OrderForm from './OrderForm';

import pic1 from 'assets/images/product/new/new_01.webp';
import pic2 from 'assets/images/product/new/new_02.webp';
import { OrderProduct } from 'app/slice/cart/types';
import Products from 'app/pages/HomePage/SlideProducts';
import { request } from 'utils/request';
import get from 'lodash/get';
interface TopBarProps {
  changeLanguage: (lng: string) => void;
  onSignIn: () => void;
  onSignUp: () => void;
  list?: any;
}

const formatCurrency = (number: number) => {
  return number?.toLocaleString('vi-VN', {
    style: 'currency',
    currency: 'VND',
  });
};

const getCart = keyframes`
  0% {
    transform: scale(5) translate(-1000px, 1000px);
  }
  10% {
    transform: scale(3) translate(-500px, 500px);
  }
  15% {
    transform: scale(2) translate(-200px, 200px);
  }
  25% {
    transform: scale(1.75) translate(-100px, 150px);
  }
  50% {
    transform: scale(1.5) translate(-90px, 100px);
  }
  75% {
    transform: scale(1.5) translate(-90, 50px);
  }
  100% {
    transform: scale(1);
  }
`;
const getCart2 = keyframes`
  0% {
    transform: scale(1)
  }
  50% {
    transform: scale(2)
  }
  100% {
    transform: scale(1);
  }
`;

const StyledBadge = styled(Badge)<BadgeProps>(({ theme }) => ({
  '& .MuiBadge-badge': {
    right: 5,
    top: 5,
    border: `2px solid ${theme.palette.background.paper}`,
    padding: '0 4px',
  },
}));

export default function TopBar(props: TopBarProps) {
  const { changeLanguage, onSignIn, onSignUp, list } = props;
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: cartActions } = useCartSlice();
  const { actions: userActions } = useUserSlice();
  const cartInfo = useSelector(cartInfoSelector);
  const userInfo = useSelector(userInfoSelector);
  const [user, setUser] = React.useState<any>({
    email: '',
    emailVerification: false,
    error: '',
    loading: '',
    phone: '',
    phoneVerification: false,
    role: '',
    token: '',
    userId: '',
    username: '',
  });
  const [orders, setOrders] = React.useState<any[]>([]);
  const [carts, setCarts] = React.useState<any>([]);
  const [paymentOption, setPaymentOption] = React.useState<string>('cash');
  const [languageAnchorEl, setLanguageAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const openSetLanguage = Boolean(languageAnchorEl);

  const { t } = useTranslation();
  const [language, setLanguage] = React.useState<string>(
    i18next.language || window.localStorage.i18nextLng,
  );

  const closeSetLanguage = () => {
    setLanguageAnchorEl(null);
  };

  const onSetLanguage = (event: React.MouseEvent<HTMLButtonElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChecked = index => {
    const cloneOrders = [...orders];
    cloneOrders[index] = !cloneOrders[index] ? cartItems[index] : '';
    setOrders(cloneOrders);
  };

  const handleLogout = () => {
    dispatch(userActions.signOut({}));
  };
  const [isAdmin, setIsAdmin] = React.useState<boolean>(false);
  const [isLogin, setIsLogin] = React.useState<boolean>(false);
  const [isOpenUserInfo, setIsOpenUserInfo] = React.useState<boolean>(false);
  const [isOpenForm, setIsOpenForm] = React.useState<boolean>(false);
  const [isAnimation, setIsAnimation] = React.useState<boolean>(false);
  const [isUserAction, setIsUserAction] = React.useState<boolean>(false);
  const [toggleAnimation, setToggleAnimation] = React.useState<boolean>(false);

  const [totalPriceOrigin, setTotalPriceOrigin] = React.useState<any>(0);
  const [totalBill, setTotalBill] = React.useState<any>(0);
  const [cartItems, setCartItems] = React.useState<any>([]);

  useEffect(() => {
    const totalBill = orders.reduce(
      (accumulator, currentValue, currentIndex) => {
        return (
          accumulator +
          (Number(currentValue?.price * currentValue?.quantity) || 0)
        );
      },
      0,
    );
    const totalPriceOrigin = orders.reduce(
      (accumulator, currentValue, currentIndex) => {
        return (
          accumulator +
          (Number(currentValue?.priceOrigin * currentValue?.quantity) || 0)
        );
      },
      0,
    );
    setTotalBill(totalBill || 0);
    setTotalPriceOrigin(totalPriceOrigin || 0);
  }, [orders]);

  useEffect(() => {
    if (userInfo?.role === 'ADMIN') {
      setIsAdmin(true);
    } else {
      setIsAdmin(false);
    }
    if (userInfo?.token) {
      setIsLogin(true);
    } else {
      setIsLogin(false);
    }
  }, [userInfo]);

  useEffect(() => {
    setToggleAnimation(!toggleAnimation);
    toggleAnimation && setIsAnimation(true);
    setCartItems(cartInfo?.products || []);
    // console.log('cartInfo: ', cartInfo?.products);
  }, [cartInfo?.products]);

  useEffect(() => {
    // dispatch(cartActions.fetchCarts({ token: userInfo?.token }));
  }, []);

  // useEffect(() => {
  //   fetchCarts();
  // }, [cartInfo?.products]);

  useEffect(() => {
    setUser(userInfo);
  }, [userInfo]);

  const handleCartAdd = (productId: any, quantity: number) => {
    const indexExist = cartItems.findIndex(
      item => item?.productId === productId,
    );

    const newCarts = cartItems.map(item => {
      return { productId: item.productId, quantity: item.quantity };
    });

    if (indexExist >= 0) {
      newCarts[indexExist] = {
        ...newCarts[indexExist],
        quantity: cartItems[indexExist].quantity + quantity,
      };
    } else {
      newCarts.push({ productId, quantity });
    }

    dispatch(
      cartActions.upsertCart({ products: newCarts, token: userInfo?.token }),
    );
  };

  const handleRemoveAdd = (productId: any, quantity: number) => {
    const indexExist = cartItems.findIndex(
      item => item?.productId === productId,
    );
    const newCarts = cartItems.map(item => {
      return { productId: item.productId, quantity: item.quantity };
    });
    if (indexExist >= 0) {
      if (cartItems[indexExist].quantity === quantity) {
        newCarts.splice(indexExist, 1);
      } else {
        newCarts[indexExist] = {
          ...newCarts[indexExist],
          quantity: cartItems[indexExist].quantity - quantity,
        };
      }
    } else {
      newCarts.push({ productId, quantity });
    }

    dispatch(
      cartActions.upsertCart({ products: newCarts, token: userInfo?.token }),
    );
  };

  const handleConfirmOrder = () => {
    // console.log({ cartItems });
    const main = async () => {
      try {
        const result: any = await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/orders/bulk`,
          orders,
          userInfo?.token as any,
        );
        handleSendContact();
        dispatch(cartActions.fetchCarts({ token: userInfo?.token }));
        navigate('/dat-hang');
        handleClose();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    main();
  };

  const [formValues, setFormValues] = useState<any>({
    name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
  });
  const handleSendContact = async () => {
    const dataSend = {
      // to: ['<EMAIL>', '<EMAIL>'],
      to: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      subject: 'Đơn hàng mới từ website phuonglinhjsc',
      content: `<p>Bạn có 1 đơn hàng mới.</p><p><strong>Tên khách hàng:</strong> ${
        formValues?.name
      }</p><p><strong>Số điện thoại:</strong> ${
        formValues?.phone
      }</p><p><strong>Email:</strong> ${
        formValues?.email || userInfo?.email
      }</p><p><strong>Địa chỉ:</strong> ${
        formValues?.address
      }</p><p><strong>Nội dung:</strong> ${
        formValues?.notes
      }</p><p><strong>Phương thức thanh toán:</strong> ${
        paymentOption === 'cash'
          ? 'Thanh toán khi nhận hàng'
          : 'Chuyển khoản ngân hàng'
      }</p>`,
    };
    const method = 'post';
    const url = `${process.env.REACT_APP_BACKEND_URL}/test/mail`;
    try {
      await request(method, url, dataSend);
      setFormValues({
        name: '',
        email: '',
        phone: '',
        address: '',
        notes: '',
      });
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Phương Linh cảm ơn quý khách đã để lại thông tin',
          variant: 'success',
        }),
      );
    } catch (error) {
      // console.log('err: ', error);
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: 'Gửi thông tin không thành công. Vui lòng thử lại sau',
      //     variant: 'error',
      //   }),
      // );
    }
  };
  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    // console.log('Order Information:', formValues);
    handleConfirmOrder();
    setIsOpenForm(false);
    // Bạn có thể thêm logic xử lý đặt hàng ở đây, như gửi thông tin đến server.
  };

  const UserInfo = () => {
    return (
      <Dialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('sm'))}
        fullWidth={true}
        maxWidth="sm"
        open={isOpenUserInfo}
        onClose={() => setIsOpenUserInfo(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                alignItem: 'center',
                justifyContent: 'center',
                mb: 2,
              }}
            >
              <AccountCircleIcon
                sx={{
                  color: 'red',
                  fontSize: '40px',
                  mx: 'auto',
                }}
              />
              <Typography
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  color: 'green',
                  px: 2,
                  fontWeight: 'bold',
                }}
              >
                {user?.username}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                p: 2,
                flexDirection: 'column',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(17, 253, 0, 0.241), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  alignItem: 'center',
                }}
              >
                <Typography
                  sx={{
                    width: '100px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: 'orangered',
                    px: 2,
                    borderRadius: 1,
                    boxShadow:
                      '0px 0px 2px 1px rgba(17, 253, 0, 0.241), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                  }}
                >
                  ID
                </Typography>
                <Typography
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    px: 2,
                    borderRadius: 1,
                  }}
                >
                  {user?.userId}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  alignItem: 'center',
                }}
              >
                <Typography
                  sx={{
                    width: '100px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: 'orangered',
                    px: 2,
                    borderRadius: 1,
                    boxShadow:
                      '0px 0px 2px 1px rgba(17, 253, 0, 0.241), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                  }}
                >
                  Phone
                </Typography>
                <Typography
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    px: 2,
                  }}
                >
                  {user?.phone}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  alignItem: 'center',
                }}
              >
                <Typography
                  sx={{
                    width: '100px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: 'orangered',
                    px: 2,
                    borderRadius: 1,
                    boxShadow:
                      '0px 0px 2px 1px rgba(17, 253, 0, 0.241), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                  }}
                >
                  role
                </Typography>
                <Typography
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    px: 2,
                  }}
                >
                  {user?.role}
                </Typography>
              </Box>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    );
  };
  const CreatFormOrder = () => {
    return (
      <Dialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('sm'))}
        fullWidth={true}
        maxWidth="sm"
        open={isOpenForm}
        onClose={() => setIsOpenForm(false)}
        // PaperProps={{
        //   sx: {
        //     borderRadius: 2,
        //     backgroundColor: '#efeff1',
        //     margin: 0,
        //     height: '80vh',
        //     [theme.breakpoints.down('sm')]: {
        //       height: '100vh',
        //     },
        //     overflow: 'hidden',
        //   },
        // }}
      >
        <DialogTitle>Thông tin đặt hàng</DialogTitle>
        <Box
          component="form"
          sx={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            p: 2,
            mt: 1,
          }}
        >
          <TextField
            size="small"
            label="Họ và Tên"
            name="name"
            value={formValues.name}
            onChange={handleChange}
            required
          />
          <TextField
            size="small"
            label="Email"
            name="email"
            type="email"
            value={formValues.email}
            onChange={handleChange}
            required
          />
          <TextField
            size="small"
            label="Số Điện Thoại"
            name="phone"
            value={formValues.phone}
            onChange={handleChange}
            required
          />
          <TextField
            size="small"
            label="Địa Chỉ Giao Hàng"
            name="address"
            value={formValues.address}
            onChange={handleChange}
            required
          />
          <TextField
            size="small"
            label="Ghi Chú Đơn Hàng"
            name="notes"
            value={formValues.notes}
            onChange={handleChange}
            multiline
            rows={4}
          />
          <FormControl
            sx={{
              width: 'fit-content',
              minWidth: '200px',
            }}
          >
            <InputLabel id="order-status">Phương thức thanh toán</InputLabel>
            <Select
              size="small"
              labelId="order-status-label"
              id="order-status"
              value={paymentOption}
              label="Phương thức thanh toán"
              onChange={e => {
                setPaymentOption(e.target.value);
              }}
            >
              <MenuItem value="cash">Thanh toán khi nhận hàng</MenuItem>
              <MenuItem value="banking">Chuyển khoản ngân hàng</MenuItem>
            </Select>
          </FormControl>
          <Card
            sx={{
              display: paymentOption === 'banking' ? '' : 'none',
            }}
          >
            <Typography
              sx={{
                p: 1,
                whiteSpace: 'pre-wrap',
              }}
            >
              {
                'Chuyển khoản qua ngân hàng cho chúng tôi theo thông tin:\nTên ngân hàng: Sacombank - Chi nhánh Quận 5 \nChủ tài khoản: CONG TY TNHH THUONG MAI – DAU TU PHUONG LINH\nSố tài khoản: ************ \nNội dung: Họ và tên khách hàng + Tên sản phẩm + SĐT \nVí dụ: Nguyễn A + Sạc dự phòng Adata AA 10050mAh (Titanium) + 0123xxx789'
              }
            </Typography>
            <Typography
              sx={{
                p: 1,
                whiteSpace: 'pre-wrap',
                fontStyle: 'italic',
                color: 'orangered',
              }}
            >
              {
                'Note: Sau khi thanh toán Anh/Chị vui lòng gửi bill vào Zalo hoặc Fanpage để chúng tôi xác nhận và giao hàng. Trân trọng!'
              }
            </Typography>
          </Card>
        </Box>
        <DialogActions>
          <Button variant="outlined" color="warning">
            Hủy
          </Button>
          <Button variant="contained" color="success" onClick={handleSubmit}>
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Toolbar
      sx={{
        display: 'flex',
        [theme.breakpoints.down('md')]: {
          flexDirection: 'column',
        },
        [theme.breakpoints.down('sm')]: {
          display: 'none',
        },
      }}
    >
      {/* <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Box
          sx={{
            flex: 4,
            maxWidth: '800px',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            borderRadius: 1,
            border: '2px solid #039141',
            // mx: 2,
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
        >
          <Box
            sx={{
              flex: 4,
              px: 2,
              py: '4px',
              mr: 1,
              borderRight: '1px solid #e0e0e0',
              [theme.breakpoints.down('md')]: {
                flex: 1,
              },
            }}
          >
            <SearchMenu />
          </Box>

          <Box
            flex={2}
            display={'flex'}
            flexDirection={'row'}
            justifyContent={'space-between'}
            alignItems={'center'}
          >
            <TextField
              id="outlined-select-currency"
              select
              InputProps={{ disableUnderline: true }}
              variant="standard"
              // margin="dense"
              // size="small"
              // color="success"
              label={t('topbar.allCategories')}
              // defaultValue="EUR"
              // helperText="Please select your currency"
              sx={{
                p: 0,
                width: '100%',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                textTransform: 'capitalize',
                '& .MuiInputLabel-standard': {
                  display: 'flex',
                  color: 'green',
                  fontSize: '0.8rem',
                  '&.Mui-focused': {
                    color: 'green',
                  },
                },
                '& .MuiInputBase-input': {
                  p: '2px',
                },
              }}
            >
              {list?.map(option => (
                <MenuItem
                  key={option.value}
                  value={option.value}
                  sx={{
                    textTransform: 'capitalize',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: 'green',
                  }}
                  onClick={() => navigate(`/bo-suu-tap/${option.id}`)}
                >
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Box>
        </Box>
        <Box
          sx={{
            width: 'fit-content',
            flex: 1,
            gap: 0.5,
            px: 3,
            // bgcolor: 'white',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'start',
            alignItems: 'start',
            color: 'green',
          }}
        >
          <Typography
            onClick={() => {
              window.location.href = `tel:0931878799`;
            }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              color: '',
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            <PhoneInTalkIcon
              sx={{
                p: '3px',
                border: '0.5px green solid',
                borderRadius: '100%',
              }}
            />
            093 187 87 99
          </Typography>
          <Typography
            onClick={() => {
              window.location.href = `mailto:<EMAIL>`;
            }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              cursor: 'pointer',
              fontSize: '12px',
            }}
          >
            <MailIcon
              sx={{
                p: '3px',
                border: '0.5px green solid',
                borderRadius: '100%',
              }}
            />
            <EMAIL>
          </Typography>
        </Box>
      </Box> */}

      {/* <Box
        sx={{
          width: 'fit-content',
          flex: 1,
          gap: 1,
          pl: 2,
          // bgcolor: 'white',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          borderLeft: '0.5px gray solid',
          [theme.breakpoints.down('md')]: {
            borderLeft: 'none',
            borderTop: '0.5px gray solid',
            mt: 1,
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            gap: 1,
            [theme.breakpoints.down('md')]: {
              flexDirection: 'row',
            },
          }}
        >
          <Box
            sx={{
              flex: 1,
              width: 'fit-content',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 1,
              color: 'green',
              fontSize: '12px',
              // [theme.breakpoints.down('md')]: {
              //   display: 'none',
              // },
            }}
          >
            <AccountCircleIcon fontSize="small" />
            {isLogin ? (
              <Account
                color="#039141"
                icon={t('topbar.account')}
                onClickAccount={() => {
                  if (user?.userId) {
                    // setIsOpenUserInfo(true);
                    navigate('/admin');
                  } else {
                    onSignUp();
                  }
                }}
                onClickOrder={() => navigate('/dat-hang')}
                onClickLogout={handleLogout}
              />
            ) : (
              <Typography
                sx={{
                  fontSize: '12px',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  if (user?.userId) {
                    // setIsOpenUserInfo(true);
                    navigate('/admin');
                  } else {
                    onSignIn();
                  }
                }}
              >
                Đăng nhập
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
              color: '#039141',
              // [theme.breakpoints.down('md')]: {
              //   display: 'none',
              // },
            }}
          >
            <LanguageIcon fontSize="small" />
            <Button
              // variant="text"
              // disableFocusRipple
              sx={{
                p: 0,
                m: 0,
                width: 'fit-content',
                textTransform: 'none',
                color: '#039141',
                whiteSpace: 'nowrap',
                backgroundColor: '',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'start',
                fontSize: '12px',
                fontWeight: 'normal',
                // gap: 1,
              }}
              onClick={onSetLanguage}
              // onMouseEnter={onSetLanguage}
            >
              {SupportLanguages[language] || 'Tiếng Việt'}
              <KeyboardArrowDownIcon fontSize="small" />
            </Button>
            <Menu
              id="basic-menu"
              anchorEl={languageAnchorEl}
              open={openSetLanguage}
              onClose={closeSetLanguage}
              onMouseLeave={closeSetLanguage}
              MenuListProps={{
                'aria-labelledby': 'basic-button',
              }}
            >
              {Object.keys(SupportLanguages).map(key => (
                <MenuItem
                  key={key}
                  onClick={() => {
                    setLanguage(key);
                    changeLanguage(key);
                    closeSetLanguage();
                  }}
                >
                  {SupportLanguages[key]}
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Box>

        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box
            // variant="text"
            // disableFocusRipple
            sx={{
              display: 'flex',
              alignItems: 'center',
              textTransform: 'none',
              color: 'green',
              whiteSpace: 'nowrap',
              // backgroundColor: 'red',
              px: 1,
              borderRadius: 5,
            }}
            onClick={handleClick}
          >
            <StyledBadge
              badgeContent={cartInfo?.products?.length || 0}
              color="warning"
              sx={{ mr: 1 }}
            >
              <IconButton
                sx={{
                  bgcolor: '',
                  position: 'relative',
                }}
              >
                <ShoppingCartIcon
                  fontSize="large"
                  sx={{
                    color: 'red',
                  }}
                />
              </IconButton>
              <IconButton
                sx={{
                  position: 'absolute',
                  display: `${isAnimation && toggleAnimation ? '' : 'none'}`,
                  animation: `${getCart}, ${getCart2}`,
                  animationDuration: '0.5s',
                  animationDelay: '0s, .5s',
                  animationTimingFunction: 'linear',
                }}
              >
                <ShoppingCartIcon
                  fontSize="large"
                  sx={{
                    color: 'red',
                  }}
                />
              </IconButton>
              <IconButton
                sx={{
                  position: 'absolute',
                  display: `${isAnimation && !toggleAnimation ? '' : 'none'}`,
                  animation: `${getCart}, ${getCart2}`,
                  animationDuration: '0.5s',
                  animationDelay: '0s, .5s',
                  animationTimingFunction: 'linear',
                }}
              >
                <ShoppingCartIcon
                  fontSize="large"
                  sx={{
                    color: 'red',
                  }}
                />
              </IconButton>
            </StyledBadge>
          </Box>
          <Menu
            id="long-menu"
            MenuListProps={{
              'aria-labelledby': 'long-button',
            }}
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            sx={{
              height: '100%',
              width: '600px',
              display: 'flex',
              flexDirection: 'column',
              p: 0,
              [theme.breakpoints.down('md')]: {
                width: '100%',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                height: '100%',
                overflowY: 'hidden',
                gap: '5px',
                position: 'relative',
              }}
            >
              <Box
                width={'100%'}
                height={350}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                }}
              >
                {cartItems.length > 0 ? (
                  cartItems.map(
                    (
                      {
                        _id: cartId,
                        productId,
                        quantity,
                        images,
                        collection,
                        name,
                        price,
                        priceOrigin,
                        variant,
                      },
                      index,
                    ) => (
                      <MenuItem
                        key={cartId}
                        // selected={item === cardItems[0]}
                        // onClick={handleClose}
                        sx={{
                          display: 'flex',
                          gap: 1,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          flexDirection: 'column',
                          [theme.breakpoints.down('md')]: {},
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                          }}
                        >
                          <Checkbox
                            size="small"
                            checked={orders[index] ? true : false}
                            onChange={() => handleChecked(index)}
                            inputProps={{ 'aria-label': 'controlled' }}
                            sx={{
                              width: 'fit-content',
                              height: 'fit-content',
                            }}
                          />
                          <Box
                            sx={{
                              width: '50px',
                              height: '50px',
                              minWidth: '50px',
                              minHeight: '50px',
                              borderRadius: 1,
                              overflow: 'hidden',
                              // background: `url("${item?.images?.[0].url}")`,
                              // backgroundRepeat: 'no-repeat',
                              // backgroundSize: 'cover',
                              // backgroundPosition: 'center',
                            }}
                          >
                            <img
                              src={images?.[0].url}
                              alt=""
                              width={'100%'}
                              height={'100%'}
                            />
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 1,
                            }}
                          >
                            <Typography variant="h6">{collection}</Typography>
                            <Typography
                              sx={{
                                width: '220px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                fontSize: '12px',
                              }}
                            >
                              {name}
                            </Typography>
                            <Box display={'flex'} gap={2}>
                              <Typography
                                sx={{
                                  fontSize: '12px',
                                  bgcolor: '#d9fec2',
                                  borderRadius: 1,
                                  px: variant.color ? 0.5 : 0,
                                }}
                              >
                                {variant.color}
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: '12px',
                                  bgcolor: '#d9fec2',
                                  px: variant.size ? 0.5 : 0,
                                  borderRadius: 1,
                                }}
                              >
                                {variant.size}
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: '12px',
                                  bgcolor: '#d9fec2',
                                  px: variant.model ? 0.5 : 0,
                                  borderRadius: 1,
                                }}
                              >
                                {variant.model}
                              </Typography>
                            </Box>
                            <Box display={'flex'} gap={2}>
                              <Typography
                                sx={{ color: 'red', fontSize: '12px' }}
                              >
                                {formatCurrency(variant.price || price)}
                              </Typography>
                              <Typography
                                sx={{
                                  textDecoration: 'line-through',
                                  fontSize: '12px',
                                }}
                              >
                                {formatCurrency(priceOrigin)}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                        <Box
                          display={'flex'}
                          flexDirection={'column'}
                          alignItems={'center'}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 2,
                            }}
                          >
                            <IconButton
                              sx={{
                                // width: '20px',
                                color: 'black',
                                p: 0.5,
                              }}
                              onClick={() => {
                                setIsUserAction(true);
                                handleRemoveAdd(productId, 1);
                              }}
                            >
                              <RemoveIcon
                                sx={{
                                  fontSize: '12px',
                                }}
                              />{' '}
                            </IconButton>
                            <Typography
                              sx={{
                                fontSize: '12px',
                              }}
                            >
                              {quantity}
                            </Typography>
                            <IconButton
                              sx={{
                                // width: '20px',
                                color: 'black',
                                p: 0.5,
                              }}
                              onClick={() => {
                                setIsUserAction(true);
                                handleCartAdd(productId, 1);
                              }}
                            >
                              <AddIcon
                                sx={{
                                  fontSize: '12px',
                                }}
                              />{' '}
                            </IconButton>
                          </Box>
                          <IconButton
                            sx={{}}
                            onClick={() => {
                              setIsUserAction(true);
                              handleRemoveAdd(productId, quantity);
                            }}
                          >
                            <DeleteIcon
                              sx={{ color: 'red', fontSize: '12px' }}
                            />
                          </IconButton>
                        </Box>
                      </MenuItem>
                    ),
                  )
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignContent: 'center',
                      p: 5,
                      m: 5,
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignContent: 'center',
                        p: 5,
                        bgcolor: '#fa5008',
                        borderRadius: '100%',
                        m: 2,
                      }}
                    >
                      <ProductionQuantityLimitsIcon
                        sx={{
                          fontSize: '50px',
                          p: 1,
                          bgcolor: 'white',
                          borderRadius: '100%',
                        }}
                      />
                    </Box>
                    <Typography
                      sx={{
                        mx: 'auto',
                        color: 'red',
                        fontStyle: 'italic',
                      }}
                    >
                      Giỏ hàng trống
                    </Typography>
                  </Box>
                )}
              </Box>
              <Box
                sx={{
                  display: cartItems.length > 0 ? '' : 'none',
                  width: '100%',
                  paddingBottom: 1,
                  // position: 'absolute',
                  // textAlign: 'bottom',
                  boxShadow: '0px 0px 2px 1px rgba(0,0,0,0.05)',
                }}
              >
                <Box
                  display={'flex'}
                  justifyContent={'space-between'}
                  padding={3}
                >
                  <Typography>Tổng cộng</Typography>
                  <Typography fontWeight={'bold'}>
                    {formatCurrency(totalBill)}
                  </Typography>
                </Box>
                <Typography
                  sx={{
                    display: totalPriceOrigin - totalBill > 0 ? '' : 'none',
                    color: 'red',
                    mx: 3,
                    fontStyle: 'italic',
                  }}
                >
                  {`Bạn đã tiết kiệm được ${formatCurrency(
                    totalPriceOrigin - totalBill,
                  )}!`}
                </Typography>
                <Box
                  sx={{
                    width: 'fit-content',
                    ml: 'auto',
                    mr: 3,
                  }}
                >
                  <Button
                    sx={{
                      bgcolor: 'black',
                      color: 'white',
                      '&:hover': {
                        color: 'black',
                      },
                    }}
                    onClick={() => {
                      if (orders?.length) setIsOpenForm(true);
                      // if (orders?.length) handleConfirmOrder();
                    }}
                  >
                    Đặt hàng
                  </Button>
                </Box>
              </Box>
            </Box>
          </Menu>
        </Box>
      </Box> */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          ml: 'auto',
          pr: 3,
          [theme.breakpoints.down('md')]: {
            pt: 2,
          },
        }}
      >
        <Typography
          sx={{
            fontSize: '12px',
            fontFamily: 'inherit',
          }}
        >
          Liên hệ nhanh với chúng tôi
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            border: 1,
            borderRadius: 2,
            cursor: 'pointer',
          }}
          onClick={() => {
            window.open(`https://oa.zalo.me/2492418257766933850`);
          }}
        >
          <Typography
            sx={{
              fontSize: 13,
            }}
          >
            Zalo
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            border: 1,
            borderRadius: 2,
            cursor: 'pointer',
          }}
          onClick={() => {
            window.open(`https://www.facebook.com/phuonglinhjsc.vn`);
          }}
        >
          <FacebookIcon fontSize="small" />
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            border: 1,
            borderRadius: 2,
            cursor: 'pointer',
          }}
          onClick={() => {
            window.open(`tel:0931878799`);
          }}
        >
          <PhoneIcon fontSize="small" />
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            border: 1,
            borderRadius: 2,
            cursor: 'pointer',
          }}
          onClick={() => {
            window.open(`mailto:<EMAIL>`);
          }}
        >
          <MailIcon fontSize="small" />
        </Box>
      </Box>
      {UserInfo()}
      {CreatFormOrder()}
    </Toolbar>
  );
}
