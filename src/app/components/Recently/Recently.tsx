import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { userInfoSelector } from 'app/slice/user/selectors';

import product1 from 'assets/images/product/Home_living/home-1.jpeg';
import product2 from 'assets/images/product/Home_living/home-2.webp';
import product3 from 'assets/images/product/Home_living/home-3.webp';
import product4 from 'assets/images/product/Home_living/home-4.webp';
import product5 from 'assets/images/product/Home_living/home-5.webp';
import product6 from 'assets/images/product/Home_living/home-6.webp';
import product7 from 'assets/images/product/Home_living/home-7.webp';
import product8 from 'assets/images/product/Home_living/home-8.webp';
import { request } from 'utils/request';
import { get } from 'lodash';

const InitListProduct = [
  {
    background: product1,
    href: 'brand-1',
    title: 'Máy Lọc Không Khí',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product2,
    href: 'brand-1',
    title: 'Loa Di Động',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product3,
    href: 'brand-1',
    title: 'Máy Hút Ẩm',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product4,
    href: 'brand-1',
    title: 'Máy Khuếch Tán',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product5,
    href: 'brand-1',
    title: 'Quạt Điện',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product6,
    href: 'brand-1',
    title: 'Máy Giữ Ẩm',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product7,
    href: 'brand-1',
    title: 'Đèn Bàn ',
    bestSaleId: 'BRAND_ID',
  },
  {
    background: product8,
    href: 'brand-1',
    title: 'Máy Hút Bụi',
    bestSaleId: 'BRAND_ID',
  },
];

interface Props {
  title?: string;
  buttonTitle?: string;
  list?: any;
}

const Recently: React.FC<Props> = ({ title, buttonTitle, list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [data, setData] = React.useState<any>([]);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );
  const userInfo = useSelector(userInfoSelector);

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>(InitListProduct);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (gameId: string) => {
    if (gameId === 'SPIN_WHEEL') {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'not found',
          variant: 'error',
        }),
      );
    }
  };
  const fetchData = async () => {
    try {
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/users/me`,
        {},
        userInfo?.token as any,
      );
      // console.log('dataRecently: ', result);
      setData(result?.productRecentlies);
    } catch (error) {
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: get(error, 'message', 'Error Undefined'),
      //     variant: 'error',
      //   }),
      // );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  useEffect(() => {
    fetchData();
  }, [width]);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 3,
      }}
    >
      <Box
        sx={{
          display: data && data.length > 0 ? 'flex' : 'none',
          flexDirection: 'row',
          justifyContent: 'space-between',
          mt: 1,
        }}
      >
        <Typography
          sx={{
            fontWeight: 'bold',
            textTransform: 'uppercase',
          }}
        >
          Đã xem
        </Typography>
        {/* <Button>Xem tất cả</Button> */}
      </Box>
      <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
          borderRadius: 5,
          paddingBottom: 2,
          paddingTop: '30px',
        }}
        spaceBetween={20}
        effect={'coverflow'}
        grabCursor={true}
        slidesPerView={'auto'}
        autoplay={{ delay: random, disableOnInteraction: true }}
        freeMode={true}
        modules={[Navigation, Pagination, Autoplay]}
        navigation={isNavigate}
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {data &&
          data.length > 0 &&
          data?.map(
            ({ _id, name, price, priceOrigin, images, id }, index: any) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: 'auto',
                  height: '300px',
                  minWidth: '200px',
                  background: '',
                  borderRadius: 5,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  justifyContent: 'start',
                  cursor: 'pointer',
                  // overflow: 'hidden',
                  transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                  transition:
                    'opacity .2s ease-in-out,transform .5s ease-in-out',
                  position: 'relative',
                  boxShadow:
                    '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                <Box
                  sx={{
                    width: '200px',
                    height: '200px',
                    background: `url("${images?.[0]?.url}")`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    borderRadius: '',
                    display: 'flex',
                    '&:hover': {},
                  }}
                ></Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    position: 'absolute',
                    background: '#000000c',
                  }}
                >
                  <Typography
                    className="two-line-typography"
                    sx={{
                      color: bestIndex === index ? 'red' : '',
                    }}
                  >
                    {name}
                  </Typography>
                </Box>
              </SwiperSlide>
            ),
          )}
      </Swiper>
    </Box>
  );
};

export default Recently;
