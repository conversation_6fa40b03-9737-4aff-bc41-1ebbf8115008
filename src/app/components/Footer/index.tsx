import * as React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  TextField,
  Card,
  Backdrop,
  CircularProgress,
} from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import PhoneIcon from '@mui/icons-material/Phone';
import MailIcon from '@mui/icons-material/Mail';
import IconButton from '@mui/material/IconButton';
import TwitterIcon from '@mui/icons-material/Twitter';
import GoogleIcon from '@mui/icons-material/Google';
import InstagramIcon from '@mui/icons-material/Instagram';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { theme } from 'app/components/AppWrapper/theme';
import Grid from '@mui/material/Unstable_Grid2';
import InputAdornment from '@mui/material/InputAdornment';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Dialog from '@mui/material/Dialog';
import DialogContentText from '@mui/material/DialogContentText';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import { useMediaQuery } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FacebookProvider,
  Like,
  Page,
  MessengerCheckbox,
  CustomChat,
  MessageUs,
} from 'react-facebook';
// import { Platform } from 'app/constants/app';
import validator from 'validator';
import get from 'lodash/get';
import set from 'lodash/set';
import { request } from 'utils/request';
import { useUtilsSlice } from 'app/slice/utils';
import { useDispatch, useSelector } from 'react-redux';
import { useUserSlice } from 'app/slice/user';
import { userInfoSelector } from 'app/slice/user/selectors';

const pic1 = 'https://phuonglinhjsc.vn/file/img/1727320215274_logosalenoti.png';

//   {
//     name: 'Chính sách mua hàng và thanh toán',
//     detail: '',
//     href: '#',
//   },
//   {
//     name: 'Chính sách bảo hành và đổi trả',
//     detail: '',
//     href: '#',
//   },
//   {
//     name: 'Chính sách giao hàng',
//     detail: '',
//     href: '#',
//   },
//   {
//     name: 'Chính sách bảo mật thông tin',
//     detail: '',
//     href: '#',
//   },
// {
//   name: 'Liên hệ',
//   detail: '',
//   href: '/about',
// },
// ];

// const Infos = [
//   {
//     name: 'Gọi mua hàng',
//     type: 'phone',
//     href: '0931878799',
//   },
//   {
//     name: 'Gọi khiếu nại',
//     type: 'phone',
//     href: '02822160427',
//   },
//   {
//     name: 'Email',
//     type: 'email',
//     href: '<EMAIL>',
//   },
// ];
interface Props {
  children?: React.ReactNode;
}
const Footer: React.FC<Props> = ({ children }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const { actions: utilsAction } = useUtilsSlice();
  const [email, setEmail] = React.useState('');
  const [data, setData] = React.useState('');
  const [alertContent, setAlertContent] = React.useState('');
  const [footer, setFooter] = React.useState<any>({});
  const [policy, setPolicy] = React.useState([]);
  const [infos, setInfos] = React.useState([]);
  const [open, setOpen] = React.useState<boolean>(false);
  const [openAlert, setOpenAlert] = React.useState<boolean>(false);
  const [openBackdrop, setOpenBackdrop] = React.useState(false);

  const handleCloseBackdrop = () => {
    setOpenBackdrop(false);
  };
  const handleOpenBackdrop = () => {
    setOpenBackdrop(true);
  };

  const SimpleBackdrop = () => {
    return (
      <div>
        <Backdrop
          sx={{ color: '#fff', zIndex: theme => theme.zIndex.drawer + 1 }}
          open={openBackdrop}
          onClick={handleCloseBackdrop}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
      </div>
    );
  };

  const isValidEmail = (email: string): boolean => {
    return validator.isEmail(email);
  };
  const handleSendContact = async () => {
    if (email && !isValidEmail(email)) {
      setAlertContent('Email không hợp lệ!');
      setOpenAlert(true);
      return;
    }
    handleOpenBackdrop();
    const newsData = {
      email,
    };

    const dataSend = {
      to: '<EMAIL>',
      subject: 'Website phuonglinhjsc gửi thông báo',
      content: `Có một khách hàng vừa để lại thông tin email từ Home page: ${newsData?.email}`,
    };

    const signature = `
    <div style="margin-top: 20px; border-top: 1px solid #dddddd; padding-top: 20px;">
      <table cellpadding="0" cellspacing="0" style="font-family: Arial, sans-serif;">
        <tr>
          <td style="padding-right: 15px;">
            <img src="https://phuonglinhjsc.vn/logo.png" alt="Logo" style="width: 120px; height: auto;"/>
          </td>
          <td style="border-left: 2px solid #dddddd; padding-left: 15px;">
            <p style="margin: 0; font-weight: bold; color: #2B3990; font-size: 16px;">PHƯƠNG LINH JSC</p>
            <p style="margin: 5px 0; color: #666666;">Customer Executive</p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Hotline:</span> 
              <a href="tel:02822160427" style="color: #2B3990; text-decoration: none;">0926 87 87 99</a>
            </p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Email:</span> 
              <a href="mailto:<EMAIL>" style="color: #2B3990; text-decoration: none;"><EMAIL></a>
            </p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Website:</span> 
              <a href="https://phuonglinhjsc.vn" style="color: #2B3990; text-decoration: none;">www.phuonglinhjsc.vn</a>
            </p>
            <div style="margin-top: 10px;">
              <a href="https://www.facebook.com/phuonglinhjsc.vn" style="text-decoration: none; margin-right: 10px;">
                <img src="https://phuonglinhjsc.vn/file/img/1727319813705_logomessenger.png" alt="Facebook" style="width: 20px; height: 20px;"/>
              </a>
            </div>
          </td>
        </tr>
      </table>
    </div>
  `;

    const dataReply = {
      to: newsData?.email,
      subject: 'Website phuonglinhjsc gửi thông báo',
      content: `
        <div style="font-family: Arial, sans-serif; color: #333333;">
          <p>Kính gửi Quý khách,</p>
          
          <p>Cảm ơn Quý khách đã quan tâm đến sản phẩm và dịch vụ của Phương Linh JSC.</p>
          
          <p>Để được tư vấn chi tiết và nhận những ưu đãi đặc quyền, Quý khách vui lòng liên hệ với bộ phận CSKH của chúng tôi qua Zalo: 
            <a href="https://zalo.me/02822160427" style="color: #2B3990;">0926 87 87 99</a>
          </p>
          
          <p>Trân trọng!</p>
          ${signature}
        </div>
      `,
    };

    const method = 'post';
    const url = `${process.env.REACT_APP_BACKEND_URL}/test/mail`;
    try {
      await request(method, url, dataSend);
      await request(method, url, dataReply);
      setEmail('');
      handleCloseBackdrop();
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Phương Linh cảm ơn quý khách đã để lại thông tin',
          variant: 'success',
        }),
      );
    } catch (error) {
      // console.log('err: ', error);
      handleCloseBackdrop();
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Gửi thông tin không thành công. Vui lòng thử lại sau',
          variant: 'error',
        }),
      );
    }
  };

  const fetchHomePage = () => {
    // const apiHandler = async () => {
    //   try {
    //     const result: any = await request(
    //       'get',
    //       `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
    //       { type: 'HOME' },
    //     );
    //     setFooter(result?.footer);
    //     setInfos(result?.footer?.info);
    //     setPolicy(result?.footer?.policy);
    //   } catch (error) {}
    // };
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/footer`,
        );
        setFooter(result?.footer);
        setInfos(result?.footer?.info);
        setPolicy(result?.footer?.policy);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const handleCloseAlert = () => {
    setOpenAlert(false);
  };

  const Alert = () => {
    return (
      <Dialog
        open={openAlert}
        onClose={handleCloseAlert}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {alertContent}
          </DialogContentText>
        </DialogContent>
      </Dialog>
    );
  };

  React.useEffect(() => {
    fetchHomePage();
  }, []);

  return (
    <>
      {/* <script
        src="https://homicen-dev.s3.ap-southeast-1.amazonaws.com/phuonglinh/jsfile/footer.js"
        type="text/javascript"
      ></script> */}
      <Box
        width="100%"
        height="fit-content"
        // borderTop="1px solid #3a3a3c"
        marginTop={5}
        sx={{
          bgcolor: '#062851',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            p: 2,
            gap: 5,
            // px: '100px',
            background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
            // pathname === '/san-pham' || pathname === '/bo-suu-tap'
            //   ? 'linear-gradient(to right, #dc3d2f, #fb6261)'
            //   : 'linear-gradient(to right, #19d7e4, #42b4f6)',
            [theme.breakpoints.down('lg')]: {
              flexDirection: 'column',
              gap: 1,
              px: 3,
            },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 1,
              // bgcolor:'yellow'
            }}
          >
            <Typography
              sx={{
                width: '100%',
                p: 0,
                fontSize: '30px',
                color: 'white',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                textAlign: 'center',
                [theme.breakpoints.down('lg')]: {
                  textAlign: 'center',
                },
                [theme.breakpoints.down('sm')]: {
                  fontSize: '25px',
                },
              }}
            >
              GỬI ĐỂ NHẬN BÁO GIÁ
            </Typography>
            <Typography
              sx={{
                width: '100%',
                p: 0,
                color: 'white',
                fontSize: '16px',
                // fontWeight: 'bold',
                textAlign: 'center',
                [theme.breakpoints.down('lg')]: {
                  textAlign: 'center',
                },
                [theme.breakpoints.down('sm')]: {
                  fontSize: '14px',
                },
              }}
            >
              Chúng tôi luôn có những chính sách ưu đãi cho thành viên đại lý
            </Typography>
          </Box>
          <Card
            sx={{
              width: '100%',
              maxWidth: '600px',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              p: 0,
              fontFamily: 'inherit',
              borderRadius: 8,
              overflow: 'hidden',
            }}
          >
            <TextField
              placeholder="Nhập email của bạn..."
              size="medium"
              variant="standard"
              sx={{
                width: '100%',
                bgcolor: 'white',
                fontStyle: 'italic',
                fontFamily: 'inherit',
                p: 2,
                '& .MuiInputBase-input': {
                  fontSize: '18px',
                  // fontWeight: 'bold',
                  fontFamily: '"Lexend Deca"',
                },
                [theme.breakpoints.down('sm')]: {
                  '& .MuiInputBase-input': {
                    fontSize: '14px',
                  },
                },
              }}
              value={email}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setEmail(event.target.value);
              }}
              InputProps={{
                disableUnderline: true,
                endAdornment: (
                  <InputAdornment position="start">
                    <Button
                      sx={{
                        height: '100%',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        // pathname === '/san-pham' || pathname === '/bo-suu-tap'
                        //   ? 'linear-gradient(to right, #dc3d2f, #fb6261)'
                        //   : 'linear-gradient(to right, #19d7e4, #42b4f6)',
                        color: 'white',
                        // textTransform: 'none',
                        fontWeight: 'bold',
                        fontFamily: '"Lexend Deca"',
                        border: '1px white solid',
                        whiteSpace: 'nowrap',
                        py: 3,
                        px: 4,
                        mr: '-15px',
                        borderRadius: 8,
                        [theme.breakpoints.down('md')]: {
                          px: 1.5,
                        },
                        [theme.breakpoints.down('sm')]: {
                          fontSize: '12px',
                        },
                      }}
                      onClick={handleSendContact}
                    >
                      Gửi ngay
                    </Button>
                  </InputAdornment>
                ),
              }}
            />
          </Card>
        </Box>
        <Grid
          container
          spacing={{ xs: 5, sm: 5, md: 1, lg: 2, xl: 5 }}
          columns={{ xs: 1, sm: 2, md: 4, lg: 4, xl: 5 }}
          sx={{
            py: 3,
            px: 2,
            justifyContent: 'center',
            // [theme.breakpoints.down('sm')]: {
            //   justifyContent: 'center',
            // },
          }}
        >
          <Grid xs={1} sm={1} md={1} lg={1}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems={'center'}
              gap={1}
              width={1}
            >
              <img
                width="140px"
                height="100px"
                src="https://phuonglinhjsc.vn/file/img/1727327515167_logophuonglinhjscfooter140x100.png"
                alt="logo"
                loading="lazy"
              />
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                  textTransform: 'uppercase',
                  color: 'white',
                  textAlign: 'center',
                }}
              >
                CÔNG TY TNHH THƯƠNG MẠI - ĐẦU TƯ PHƯƠNG LINH
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  color: 'white',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    border: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    window.open('https://oa.zalo.me/2492418257766933850');
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: 13,
                    }}
                  >
                    Zalo
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    border: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    window.open('https://www.facebook.com/phuonglinhjsc.vn');
                  }}
                >
                  <FacebookIcon fontSize="small" />
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    border: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    window.open('tel:02822160427');
                  }}
                >
                  <PhoneIcon fontSize="small" />
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    border: 1,
                    borderRadius: 2,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    window.open('mailto:<EMAIL>');
                  }}
                >
                  <MailIcon fontSize="small" />
                </Box>
              </Box>
            </Box>
          </Grid>
          <Grid xs={1} sm={1} md={1} lg={1}>
            <Box display="flex" flexDirection="column" gap={1} width={1}>
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                  fontSize: '25px',
                  textTransform: 'uppercase',
                  color: 'white',
                  [theme.breakpoints.down('sm')]: {
                    fontSize: '20px',
                  },
                }}
              >
                LIÊN HỆ
              </Typography>
              <Box
                sx={{
                  width: '80px',
                  background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                  p: 0.5,
                  borderRadius: 5,
                }}
              ></Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  gap: 1,
                  mt: 2,
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    fontFamily: 'inherit',
                    fontSize: '25px',
                    textTransform: 'uppercase',
                    color: 'white',
                    [theme.breakpoints.down('sm')]: {
                      fontSize: '20px',
                    },
                  }}
                >
                  KINH DOANH
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    color: 'white',
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                    }}
                  >
                    Email: <EMAIL>
                  </Typography>
                  {/* <Typography
                    sx={{
                      fontFamily: 'inherit',
                    }}
                  >
                    Email: <EMAIL>
                  </Typography> */}
                </Box>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    fontFamily: 'inherit',
                    fontSize: '25px',
                    textTransform: 'uppercase',
                    color: 'white',
                    [theme.breakpoints.down('sm')]: {
                      fontSize: '20px',
                    },
                  }}
                >
                  MUA HÀNG
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    color: 'white',
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                    }}
                  >
                    SĐT: 0931878799
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    color: 'white',
                    cursor: 'pointer',
                    '&:hover': {
                      color: '#19d7e4',
                    },
                  }}
                  onClick={() => {
                    navigate(
                      '/tin-tuc/phuong-linh-jsc-tuyen-%C4%91ai-ly-phan-phoi-toan-quoc',
                    );
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                    }}
                  >
                    Tuyển đại lý
                  </Typography>
                </Box>
                {/* <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                  fontSize: '25px',
                  textTransform: 'uppercase',
                  color: 'white',
                }}
              >
                BẢO HÀNH
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  color: 'white',
                }}
              >
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                  }}
                >
                  SĐT: 02822160427
                </Typography>
              </Box> */}
              </Box>

              {/* <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                width: 'fit-content',
              }}
            >
              {infos &&
                infos.map((item: any, index) => (
                  <Typography
                    key={index}
                    sx={{
                      display: 'flex',
                      gap: 1,
                      color: 'white',
                    }}
                  >
                    <Typography
                      sx={{
                        color: 'inherit',
                        fontSize: '14px',
                        fontWeight: 300,
                        fontFamily: 'inherit',
                        '&:hover': {
                          color: 'black',
                        },
                      }}
                    >
                      {item.name}
                    </Typography>
                    <Typography
                      sx={{
                        color: '#767681',
                        fontSize: '14px',
                        fontWeight: 300,
                        fontFamily: 'inherit',
                        cursor: 'pointer',
                        '&:hover': {
                          color: 'red',
                        },
                      }}
                      onClick={() => {
                        if (item.type === 'phone') {
                          window.location.href = `tel:${item.href}`;
                        } else if (item.type === 'email') {
                          window.location.href = `mailto:${item.href}`;
                        }
                      }}
                    >
                      {item.href}
                    </Typography>
                  </Typography>
                ))}
            </Box> */}
            </Box>
          </Grid>
          <Grid xs={1} sm={1} md={1} lg={1}>
            <Box display="flex" flexDirection="column" gap={1} width={1}>
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                  fontSize: 25,
                  textTransform: 'uppercase',
                  color: 'white',
                  [theme.breakpoints.down('sm')]: {
                    fontSize: '20px',
                  },
                }}
              >
                Chính sách hỗ trợ
              </Typography>
              <Box
                sx={{
                  width: '80px',
                  background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                  p: 0.5,
                  borderRadius: 5,
                }}
              ></Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  mt: 2,
                }}
              >
                {policy?.map((item: any, index) => (
                  <Box
                    key={index}
                    sx={{
                      cursor: 'pointer',
                      fontFamily: 'inherit',
                    }}
                    onClick={() => {
                      if (item.href) {
                        window.open(item.href);
                      } else {
                        if (item.id === 'Lien-he' || item.id === 'lien-he') {
                          navigate('/lien-he');
                        } else {
                          navigate(`/chinh-sach/${item.id}`);
                        }
                      }
                    }}
                  >
                    <Typography
                      sx={{
                        color: 'white',
                        fontSize: '16px',
                        fontWeight: 300,
                        fontFamily: 'inherit',
                        '&:hover': {
                          color: '#19d7e4',
                        },
                      }}
                    >
                      {item.name}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          </Grid>
          <Grid xs={1} sm={1} md={1} lg={1}>
            <Box
              display="flex"
              flexDirection="column"
              gap={1}
              width={1}
              justifyContent={'start'}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                  fontSize: 25,
                  textTransform: 'uppercase',
                  color: 'white',
                  [theme.breakpoints.down('sm')]: {
                    fontSize: '20px',
                  },
                }}
              >
                Liên kết
              </Typography>
              <Box
                sx={{
                  width: '80px',
                  background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                  p: 0.5,
                  borderRadius: 5,
                }}
              ></Box>

              <Typography
                sx={{
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: 300,
                  fontFamily: 'inherit',
                  cursor: 'pointer',
                  '&:hover': {
                    color: '#19d7e4',
                  },
                }}
                onClick={() => {
                  window.open(footer?.linkToFacebook);
                }}
              >
                Hãy kết nối với chúng tôi.
              </Typography>
              <Box>
                <IconButton
                  onClick={() => {
                    window.open(footer?.linkToFacebook);
                  }}
                  sx={{ color: '#0084ca' }}
                >
                  <FacebookIcon />
                </IconButton>
                <IconButton sx={{ color: 'red' }}>
                  <GoogleIcon />
                </IconButton>
                <IconButton sx={{ color: 'orangered' }}>
                  <InstagramIcon />
                </IconButton>
                <IconButton sx={{ color: 'red' }}>
                  <YouTubeIcon />
                </IconButton>
              </Box>
              <Box display={'flex'} width={'100%'}>
                <Button
                  sx={{
                    width: '150px',
                    p: 0,
                    m: 0,
                    '&:hover': {
                      bgcolor: 'transparent',
                    },
                  }}
                  onClick={() => {
                    window.open('http://online.gov.vn/Home/WebDetails/38823');
                  }}
                >
                  <img
                    src={pic1}
                    alt=""
                    width={'100%'}
                    height={'100%'}
                    loading="lazy"
                  />
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>

        <Box
          sx={{
            mx: 'auto',
            maxWidth: '60%',
            border: '0.5px solid #fff',
          }}
        ></Box>
      </Box>
      <Box
        // maxWidth="lg"
        minWidth="xs"
        width="100%"
        height="fit-content"
        paddingX={1}
        paddingTop={3}
        paddingBottom={1}
        overflow="hidden"
        sx={{
          bgcolor: '#062851',
        }}
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          paddingY={3}
          gap={2}
          color={'white'}
        >
          <Typography sx={{ fontFamily: 'inherit' }}>
            © Bản quyền thuộc về PhuongLinhJSC
          </Typography>
          <Typography sx={{ textAlign: 'center', fontFamily: 'inherit' }}>
            Công ty TNHH Thương Mại - Đầu Tư Phương Linh. GPĐKKD 0304835988 do
            sở KH & ĐT TP Hồ Chí Minh cấp ngày 02/02/2007.
          </Typography>
        </Box>
      </Box>
      <MuiDialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={open}
        onClose={() => setOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            // overflow: 'hidden',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
              mb: 1,
              overflow: 'auto',
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                // fontWeight: 'bold',
                whiteSpace: 'pre-wrap',
              }}
            >
              {data}
            </Typography>
          </Box>
        </DialogContent>
      </MuiDialog>
      {Alert()}
      {SimpleBackdrop()}
    </>
  );
};
export default Footer;
