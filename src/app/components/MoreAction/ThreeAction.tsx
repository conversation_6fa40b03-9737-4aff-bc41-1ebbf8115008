import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import MenuList from '@mui/material/MenuList';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import DoneIcon from '@mui/icons-material/Done';
import IconButton from '@mui/material/IconButton';

interface Props {
  color?: string;
  icon?: any;
  style?: React.CSSProperties;
  done: Boolean;
  edit: Boolean;
  remove: Boolean;
  onClickDone?: () => void;
  onClickRemove?: () => void;
  onClickEdit?: () => void;
}

const ThreeAction: React.FC<Props> = ({
  icon,
  color,
  done,
  edit,
  remove,
  onClickDone,
  onClickEdit,
  onClickRemove,
  style = {},
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        width: 'fit-content',
        height: 'fit-content',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        overflow: 'hidden',
      }}
    >
      <IconButton
        id="basic-button-more"
        aria-controls={open ? 'basic-menu-more' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onMouseEnter={handleClick}
        // onMouseLeave={handleClose}
        sx={{
          color: color || 'white',
        }}
      >
        {icon || <MoreVertIcon />}
      </IconButton>
      <Menu
        id="basic-menu-more"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button-more',
        }}
      >
        <MenuList onMouseLeave={handleClose}>
          <MenuItem
            sx={{
              display: !done ? 'none' : '',
            }}
            onClick={onClickDone}
          >
            <ListItemIcon>
              <DoneIcon fontSize="small" sx={{ color: 'green' }} />
            </ListItemIcon>
          </MenuItem>
          <MenuItem
            sx={{
              display: !edit ? 'none' : '',
            }}
            onClick={onClickEdit}
          >
            <ListItemIcon>
              <ModeEditIcon fontSize="small" sx={{ color: 'orange' }} />
            </ListItemIcon>
          </MenuItem>
          <MenuItem
            sx={{
              display: !remove ? 'none' : '',
            }}
            onClick={onClickRemove}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" sx={{ color: 'red' }} />
            </ListItemIcon>
          </MenuItem>
        </MenuList>
      </Menu>
    </Box>
  );
};

export default ThreeAction;
