import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import MenuList from '@mui/material/MenuList';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';

interface Props {
  color?: string;
  icon?: any;
  style?: React.CSSProperties;
  add: Boolean;
  edit: Boolean;
  remove: Boolean;
  onClickAdd?: () => void;
  onClickRemove?: () => void;
  onClickEdit?: () => void;
}

const MoreAction: React.FC<Props> = ({
  icon,
  color,
  add,
  edit,
  remove,
  onClickAdd,
  onClickEdit,
  onClickRemove,
  style = {},
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        width: 'fit-content',
        height: 'fit-content',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        overflow: 'hidden',
      }}
    >
      <IconButton
        id="basic-button-more"
        aria-controls={open ? 'basic-menu-more' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={e => {
          handleClick(e);
          e.stopPropagation();
        }}
        sx={{
          color: color || 'white',
        }}
      >
        {icon || <MoreVertIcon />}
      </IconButton>
      <Menu
        id="basic-menu-more"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button-more',
        }}
      >
        <MenuList>
          <MenuItem
            sx={{
              display: !add ? 'none' : '',
            }}
            onClick={e => {
              e.stopPropagation();
              onClickAdd && onClickAdd();
              handleClose();
            }}
          >
            <ListItemIcon>
              <QueueIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Thêm mới</ListItemText>
          </MenuItem>
          <MenuItem
            sx={{
              display: !edit ? 'none' : '',
            }}
            onClick={e => {
              e.stopPropagation();
              onClickEdit && onClickEdit();
              handleClose();
            }}
          >
            <ListItemIcon>
              <ModeEditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Thay đổi</ListItemText>
          </MenuItem>
          <MenuItem
            sx={{
              display: !remove ? 'none' : '',
            }}
            onClick={e => {
              e.stopPropagation();
              onClickRemove && onClickRemove();
              handleClose();
            }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Xoá</ListItemText>
          </MenuItem>
          <Divider />
          <MenuItem onClick={e => e.stopPropagation()}>
            <ListItemIcon>
              <MoreHorizIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Tuỳ chọn</ListItemText>
          </MenuItem>
        </MenuList>
      </Menu>
    </Box>
  );
};

export default MoreAction;
