import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { Children, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import Grid from '@mui/material/Unstable_Grid2';
import { theme } from 'app/components/AppWrapper/theme';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';
import { userInfoSelector } from 'app/slice/user/selectors';
import { DIALOG_TYPE } from 'app/slice/utils/types';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';

interface Props {
  title?: string;
  buttonTitle?: string;
  banner?: string;
  content?: string;
  href?: string;
  list?: string[];
  productId: any;
  quantity: any;
  variant?: any;
  children: React.ReactNode;
}

const AddToCart: React.FC<Props> = ({
  productId,
  quantity,
  variant,
  children,
}) => {
  const { actions: cartActions } = useCartSlice();
  const cartInfo = useSelector(cartInfoSelector);
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);
  const [carts, setCarts] = React.useState<any>([]);

  useEffect(() => {
    setCarts(cartInfo?.products || []);
    // console.log('cart: ', cartInfo?.products);
  }, [cartInfo]);

  const onSignIn = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.SIGN_IN }));
  };

  const handleCartAdd = (productId: any, quantity: number, variant: any) => {
    // console.log('new cart: ', { productId, quantity, variant });
    const indexExist = carts?.findIndex(
      item =>
        item?.productId === productId &&
        item.variant.color === variant.color &&
        item.variant.size === variant.size &&
        item.variant.model === variant.model,
    );

    const newCarts = carts?.map(item => {
      return {
        productId: item.productId,
        quantity: item.quantity,
        variant: item.variant,
      };
    });
    if (indexExist >= 0) {
      newCarts[indexExist] = {
        ...newCarts[indexExist],
        quantity: carts[indexExist].quantity + quantity,
      };
      // console.log('push exist: ', newCarts);
    } else {
      newCarts.push({ productId, quantity, variant });
      // console.log('push new: ', newCarts);
    }
    dispatch(
      cartActions.upsertCart({ products: newCarts, token: userInfo?.token }),
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        gap: 5,
        width: 'fit-content',
        my: 0,
        borderRadius: 1,
        cursor: 'pointer',
        zIndex: 2,
      }}
      onClick={e => {
        if (userInfo?.token) {
          e.stopPropagation();
          handleCartAdd(productId, quantity, variant);
        } else {
          onSignIn();
        }
      }}
    >
      {children}
    </Box>
  );
};

export default AddToCart;
