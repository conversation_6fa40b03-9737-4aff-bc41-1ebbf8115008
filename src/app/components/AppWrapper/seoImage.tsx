import featureImage from 'assets/images/product/type/product_type_5.webp';
import React from 'react';

const URL = process.env.REACT_APP_FRONTEND_URL;

interface Props {
  customImage?: string;
}
export const FeatureImage: React.FC<Props> = props => {
  return (
    <>
      {props.customImage ? (
        <meta property="og:image" content={URL + props.customImage} />
      ) : (
        <meta property="og:image" content={URL + featureImage} />
      )}
    </>
  );
};

export default FeatureImage;
