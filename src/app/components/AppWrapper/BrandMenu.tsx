import * as React from 'react';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import Popover from '@mui/material/Popover';
import { styled, alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Menu, { MenuProps } from '@mui/material/Menu';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import EditIcon from '@mui/icons-material/Edit';
import Divider from '@mui/material/Divider';
import ArchiveIcon from '@mui/icons-material/Archive';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';

import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { request } from 'utils/request';
import { useDispatch, useSelector } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';

type Product = {
  name: string;
  group?: string;
  collection?: string;
};

type Group = {
  name: string;
  products: Product[];
};

type Collection = {
  _id?: any;
  id?: string;
  name?: string;
  description?: string;
  parentIds?: any[];
  type?: string;
  isTag?: boolean;
  allowEdit?: boolean;
  collections?: Collection[];
};

const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorPosition={{ top: 200, left: 400 }}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'center',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'center',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    width: '80vw',
    background: 'transparent',
    color:
      theme.palette.mode === 'light'
        ? 'rgb(55, 65, 81)'
        : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
  },
}));

export default function BrandMenuPopover() {
  const [menuTree, setMenuTree] = React.useState<Collection[]>([]);
  const [menuProduct, setMenuProduct] = React.useState<any[]>([]);
  const [optionCategory, setOptionCategory] = React.useState<any[]>([]);
  const [menuIndex, setMenuIndex] = React.useState<number>(0);
  const [mouseEnter, setMouseEnter] = React.useState<boolean>(false);
  const [isAdmin, setIsAdmin] = React.useState<boolean>(false);
  const [initTimeout, setInitTimeout] = React.useState<any>();

  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
  const userInfo = useSelector(userInfoSelector);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { actions: utilsAction } = useUtilsSlice();

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
    // if (initTimeout) clearTimeout(initTimeout);
    setAnchorEl(event.currentTarget);
    // console.log('open')
  };

  const handlePopoverClose = () => {
    // if (initTimeout) clearTimeout(initTimeout);
    // setInitTimeout(setTimeout(() => setAnchorEl(null), 300));
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const handleMouseEnterItem = (index: number) => {
    setMenuIndex(index);
  };

  const handleClickCollection = (collection: Collection, params: any = {}) => {
    const id = get(collection, 'id', '');
    let uri = `/bo-suu-tap/${id}`;

    if (!isEmpty(id)) {
      setMouseEnter(false);
      if (!isEmpty(params)) {
        const query = new URLSearchParams(params).toString();
        uri += `?${query}`;
      }

      navigate(uri);
    }
  };

  const refreshMenuTree = () => {
    const apiMenuTreeHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections/menu-tree`,
        );
        const newMenuTree = [...result];
        newMenuTree.sort((a, b) => {
          return a.name.localeCompare(b.name);
        });
        setMenuTree(newMenuTree);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    const apiMenuProductHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'CATEGORY' },
        );

        const productMenu = result
          .filter(
            (value: any, index: number, self: any) =>
              index ===
              self.findIndex(
                (collection: any) => collection.name === value.name,
              ),
          )
          .map((c: any) => {
            const name = get(c, 'name');
            const collections = result.filter(
              (c: any) => name === get(c, 'name'),
            );

            return {
              name,
              collections,
            };
          });

        setMenuProduct(productMenu);
        if (productMenu) {
          const options = productMenu.map(item => {
            return {
              _id: item._id,
              value: item.name,
              label: item.name,
              id: item.collections[0].id,
            };
          });
          setOptionCategory(options);
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiMenuProductHandler();
    apiMenuTreeHandler();
  };

  useEffect(() => {
    // console.log('user: ', userInfo);
    // if (userInfo?.token && userInfo?.userId) {
    //   // User is login
    // }
    if (userInfo?.role === 'ADMIN') setIsAdmin(true);

    refreshMenuTree();
  }, [userInfo]);

  return (
    <div>
      <Typography
        aria-owns={open ? 'mouse-over-popover' : undefined}
        aria-haspopup="true"
        onMouseEnter={handlePopoverOpen}
        // onMouseLeave={handlePopoverClose}
        sx={{
          // fontWeight: 'bold',
          fontFamily: 'inherit',
        }}
      >
        Thương hiệu <KeyboardArrowDownIcon fontSize="small" />
      </Typography>
      <StyledMenu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handlePopoverClose}
        onMouseLeave={handlePopoverClose}
        // sx={{
        //   background: 'transparent'
        // }}
      >
        <Box
          className="scrollbar-small"
          py={2}
          width={1}
          height={1}
          display={'flex'}
          overflow={'auto'}
          gap={0.5}
          // sx={{
          //   background: 'transparent'
          // }}
        >
          <Box
            flex={1}
            sx={{
              background: '#e7f1f5',
              borderRadius: 3,
            }}
          >
            <MenuList>
              {menuTree?.map((item: Collection, index: number) => (
                <MenuItem
                  key={index}
                  onMouseEnter={() => {
                    handleMouseEnterItem(index);
                  }}
                  onClick={() => !item.allowEdit && handleClickCollection(item)}
                  sx={{
                    color: menuIndex === index ? 'gold' : '',
                    width: '150px',
                    borderRadius: 1,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: '12px',
                      fontWeight: 'bold',
                      textTransform: 'capitalize',
                    }}
                  >
                    {item?.name}
                  </Typography>
                  <KeyboardArrowRightIcon
                    sx={{
                      fontSize: '14px',
                      ml: 'auto',
                    }}
                  />
                </MenuItem>
              ))}
            </MenuList>
          </Box>
          <Box
            flex={5}
            sx={{
              background: '#e7f1f5',
              borderRadius: 3,
              p: 2,
            }}
          >
            <Stack
              direction="row"
              justifyContent={'start'}
              display={'flex'}
              flexWrap={'wrap'}
            >
              {menuTree[menuIndex]?.collections?.map(
                (category: Collection, index: number) => (
                  <Box
                    sx={{
                      flex: 1,
                      minWidth: '200px',
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      flexDirection: 'column',
                      mx: 2,
                    }}
                  >
                    <MenuItem
                      // onMouseEnter={() => {
                      //   handleMouseEnterItem(index);
                      // }}
                      onClick={() =>
                        handleClickCollection(category, {
                          brand: menuTree[menuIndex].id,
                        })
                      }
                      sx={{
                        p: 0,
                      }}
                    >
                      <Typography
                        sx={{
                          p: 0,
                          fontSize: '13px',
                          fontWeight: 'bold',
                          display: 'flex',
                          alignItems: 'center',
                          textTransform: 'capitalize',
                          '&:hover': {
                            color: 'gold',
                          },
                        }}
                      >
                        {category.name}
                      </Typography>
                    </MenuItem>

                    <MenuList
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 1,
                      }}
                    >
                      {category?.collections?.map(
                        (group: Collection, i: number) => (
                          <MenuItem
                            key={i}
                            onClick={() => {
                              // console.log('click', group);
                              handleClickCollection(group, {
                                brand: menuTree[menuIndex].id,
                                category: category.id,
                              });
                            }}
                            sx={{ px: 1, py: 0, borderRadius: 2 }}
                          >
                            <Typography
                              sx={{
                                p: 0,
                                fontSize: '13px',
                                color: '#696969',
                                '&:hover': {
                                  color: 'gold',
                                },
                              }}
                            >
                              {group.name}
                            </Typography>
                          </MenuItem>
                        ),
                      )}
                    </MenuList>
                  </Box>
                ),
              )}
            </Stack>
          </Box>
        </Box>
      </StyledMenu>
      {/* <Popover
        id="mouse-over-popover"
        sx={{
          pointerEvents: 'none',
        }}
        open={open}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        onMouseMove={handlePopoverOpen}
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
        disableRestoreFocus
      >
        <Box py={2} width={1} height={1} display={'flex'}>
          <Box flex={1}>
            <MenuList>
              {menuTree?.map((item: Collection, index: number) => (
                <MenuItem
                  key={index}
                  onMouseEnter={() => {
                    handleMouseEnterItem(index);
                  }}
                  onClick={() => !item.allowEdit && handleClickCollection(item)}
                  sx={{
                    color: menuIndex === index ? 'gold' : '',
                    width: '150px',
                    borderRadius: 1,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: '12px',
                      fontWeight: 'bold',
                      textTransform: 'capitalize',
                    }}
                  >
                    {item?.name}
                  </Typography>
                  <KeyboardArrowRightIcon
                    sx={{
                      fontSize: '14px',
                      ml: 'auto',
                    }}
                  />
                </MenuItem>
              ))}
            </MenuList>
          </Box>
          <Box flex={5}>
            <Stack
              direction="row"
              justifyContent={'start'}
              display={'flex'}
              flexWrap={'wrap'}
            >
              {menuTree[menuIndex]?.collections?.map(
                (category: Collection, index: number) => (
                  <Box
                    sx={{
                      flex: 1,
                      minWidth: '200px',
                      display: 'flex',
                      justifyContent: 'start',
                      alignItems: 'start',
                      flexDirection: 'column',
                      mx: 2,
                    }}
                  >
                    <MenuItem
                      // onMouseEnter={() => {
                      //   handleMouseEnterItem(index);
                      // }}
                      onClick={() =>
                        handleClickCollection(category, {
                          brand: menuTree[menuIndex].id,
                        })
                      }
                      sx={{
                        p: 0,
                      }}
                    >
                      <Typography
                        sx={{
                          p: 0,
                          fontSize: '13px',
                          fontWeight: 'bold',
                          display: 'flex',
                          alignItems: 'center',
                          textTransform: 'capitalize',
                          '&:hover': {
                            color: 'gold',
                          },
                        }}
                      >
                        {category.name}
                      </Typography>
                    </MenuItem>

                    <MenuList
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 1,
                      }}
                    >
                      {category?.collections?.map(
                        (group: Collection, i: number) => (
                          <MenuItem
                            key={i}
                            onClick={() => {
                              // console.log('click', group);
                              handleClickCollection(group, {
                                brand: menuTree[menuIndex].id,
                                category: category.id,
                              });
                            }}
                            sx={{ px: 1, py: 0, borderRadius: 2 }}
                          >
                            <Typography
                              sx={{
                                p: 0,
                                fontSize: '13px',
                                color: '#696969',
                                '&:hover': {
                                  color: 'gold',
                                },
                              }}
                            >
                              {group.name}
                            </Typography>
                          </MenuItem>
                        ),
                      )}
                    </MenuList>
                  </Box>
                ),
              )}
            </Stack>
          </Box>
        </Box>
      </Popover> */}
    </div>
  );
}
