/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import { useUtilsSlice } from 'app/slice/utils';
import { DIALOG_TYPE } from 'app/slice/utils/types';
import { ThemeProvider } from 'styled-components';
import { theme } from 'app/components/AppWrapper/theme';
import { request } from 'utils/request';
import _ from 'lodash';
import get from 'lodash/get';
import set from 'lodash/set';
import isEmpty from 'lodash/isEmpty';
import isNull from 'lodash/isNull';
import isUndefined from 'lodash/isUndefined';
import omit from 'lodash/omit';
import Box from '@mui/material/Box';
import AppBar from '@mui/material/AppBar';
import Paper from '@mui/material/Paper';
import Slide from '@mui/material/Slide';
import Toolbar from '@mui/material/Toolbar';
import useScrollTrigger from '@mui/material/useScrollTrigger';
import TopBar from 'app/components/TopBar';
import Footer from 'app/components/Footer';
import { appSelector } from 'app/slice/utils/selectors';
import AuthDialog from 'app/components/Dialog/AuthDialog';
import {
  Button,
  IconButton,
  Popper,
  Stack,
  TextField,
  useMediaQuery,
} from '@mui/material';
import Popover from '@mui/material/Popover';
import CssBaseline from '@mui/material/CssBaseline';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import NorthIcon from '@mui/icons-material/North';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { keyframes } from '@emotion/react';
import DoneIcon from '@mui/icons-material/Done';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import HomeIcon from '@mui/icons-material/Home';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import SpeakerIcon from '@mui/icons-material/Speaker';
import WatchIcon from '@mui/icons-material/Watch';
import HeadphonesIcon from '@mui/icons-material/Headphones';
import CableIcon from '@mui/icons-material/Cable';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CloseIcon from '@mui/icons-material/Close';
import LoopIcon from '@mui/icons-material/Loop';
import PowerIcon from '@mui/icons-material/Power';
import ArrowCircleUpIcon from '@mui/icons-material/ArrowCircleUp';
import KeyboardDoubleArrowUpIcon from '@mui/icons-material/KeyboardDoubleArrowUp';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import SmartphoneIcon from '@mui/icons-material/Smartphone';
import ChargingStationIcon from '@mui/icons-material/ChargingStation';
import SecurityUpdateGoodIcon from '@mui/icons-material/SecurityUpdateGood';
import MenuIcon from '@mui/icons-material/Menu';
import SearchIcon from '@mui/icons-material/Search';
import SearchMenu from '../TopBar/SearchMenu';
import Link from '@mui/material/Link';
import SwipeableDrawer from '@mui/material/SwipeableDrawer';
import { TreeItem, treeItemClasses } from '@mui/x-tree-view/TreeItem';
import { TreeItem2 } from '@mui/x-tree-view/TreeItem2';
import { styled, alpha } from '@mui/material/styles';
import Modal from '@mui/material/Modal';
import Dialog from '@mui/material/Dialog';

import { useNavigate } from 'react-router-dom';
import ThreeAction from '../MoreAction/ThreeAction';
import { useUserSlice } from 'app/slice/user';
import { useCartSlice } from 'app/slice/cart';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import useWindowScrollToTop from 'app/hook/useWindowScrollToTop';
import { TransitionProps } from '@mui/material/transitions';

const zalo = 'https://phuonglinhjsc.vn/file/img/1727319822643_iconzalo.webp';
const messenger =
  'https://phuonglinhjsc.vn/file/img/1727319813705_logomessenger.png';
interface AppProps {
  windows?: () => Window;
  children: React.ReactNode;
  changeLanguage: (lng: string) => void;
}

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'start',
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

const scaleUp1 = keyframes`
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
`;
const scaleUp2 = keyframes`
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
`;
const upUp = keyframes`
  0% {
    transform: translateY(0px)
  }
  100% {
    transform: translateY(-10px)
  }
`;

const Item = styled(Button)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: theme.palette.text.primary,
  textTransform: 'none',
  height: '100%',
}));

type Product = {
  name: string;
  group?: string;
  collection?: string;
};

type Group = {
  name: string;
  products: Product[];
};

type Collection = {
  _id?: any;
  id?: string;
  name?: string;
  description?: string;
  parentIds?: any[];
  type?: string;
  isTag?: boolean;
  allowEdit?: boolean;
  collections?: Collection[];
};

export enum CollectionType {
  COLLECTION = 'COLLECTION',
  BRAND = 'BRAND', // MENU 1
  CATEGORY = 'CATEGORY', // MENU 2
  GROUP = 'GROUP', // MENU 3
  PROMOTION = 'PROMOTION',
  NEW = 'NEW',
  OUTSTANDING = 'OUTSTANDING',
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  });
};
const handleClickZalo = () => {
  window.open('https://oa.zalo.me/2492418257766933850');
};
const handleClickMess = () => {
  window.open('https://m.me/214744035266547');
};

// Todo: check by userInfo
// const isAdmin = true;
export default function AppWrapper(props: AppProps) {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { windows, changeLanguage } = props;

  const { actions: utilsAction } = useUtilsSlice();
  const { actions: userAction } = useUserSlice();
  const { actions: cartAction } = useCartSlice();

  const userInfo = useSelector(userInfoSelector);
  const { dialogType } = useSelector(appSelector);

  const divRef = React.useRef();
  const otherBoxRef = React.useRef<any>(null);

  const { pathname } = useLocation();
  // console.log('pathname: ', pathname);
  useEffect(() => {
    scrollToTop();
  }, [pathname]);

  const [openSearch, setOpenSearch] = React.useState<boolean>(false);
  const [isAdmin, setIsAdmin] = React.useState<boolean>(false);
  const [openDrawer, setOpenDrawer] = React.useState<boolean>(false);
  const [moreBrand, setMoreBrand] = React.useState<boolean>(false);

  const [menuTree, setMenuTree] = React.useState<Collection[]>([]);
  const [menuProduct, setMenuProduct] = React.useState<any[]>([]);
  const [optionCategory, setOptionCategory] = React.useState<any[]>([]);
  const [menuIndex, setMenuIndex] = React.useState<number>(-1);
  const [textMenuColor, setTextMenuColor] = React.useState<string>('white');
  const [logo, setLogo] = React.useState<string>(
    'https://phuonglinhjsc.vn/file/img/1727319067929_logophuonglinhtrang.png',
  );

  const [mouseEnter, setMouseEnter] = React.useState<boolean>(false);
  const [mouseProductsEnter, setMouseProductsEnter] =
    React.useState<boolean>(false);

  useEffect(() => {
    // console.log('user: ', userInfo);
    // if (userInfo?.token && userInfo?.userId) {
    //   // User is login
    // }
    if (userInfo?.role === 'ADMIN') setIsAdmin(true);

    // refreshMenuTree();
  }, [userInfo]);

  useEffect(() => {
    console.log('openSearch: ', openSearch);
  }, [openSearch]);

  const handleOpenSearch = () => {
    setOpenSearch(true);
  };

  const handleCloseSearch = () => {
    setOpenSearch(false);
  };

  // fetch MenuTree API
  const refreshMenuTree = () => {
    const apiMenuTreeHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections/menu-tree`,
        );
        const newMenuTree = [...result];
        newMenuTree.sort((a, b) => {
          return a.name.localeCompare(b.name);
        });
        setMenuTree(newMenuTree);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    const apiMenuProductHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'CATEGORY' },
        );

        const productMenu = result
          .filter(
            (value: any, index: number, self: any) =>
              index ===
              self.findIndex(
                (collection: any) => collection.name === value.name,
              ),
          )
          .map((c: any) => {
            const name = get(c, 'name');
            const collections = result.filter(
              (c: any) => name === get(c, 'name'),
            );

            return {
              name,
              collections,
            };
          });

        setMenuProduct(productMenu);
        if (productMenu) {
          const options = productMenu.map(item => {
            return {
              _id: item._id,
              value: item.name,
              label: item.name,
              id: item.collections[0].id,
            };
          });
          setOptionCategory(options);
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    // apiMenuProductHandler();
    // apiMenuTreeHandler();
  };

  const handleMouseEnter = () => {
    setMouseEnter(true);
  };

  const handleMouseLeave = () => {
    setMouseEnter(false);
  };

  const handleMouseProductsEnter = () => {
    setMouseProductsEnter(true);
  };

  const handleMouseProductsLeave = () => {
    setMouseProductsEnter(false);
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    console.log('click');
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 64,
    target: windows ? windows() : undefined,
  });

  useEffect(() => {
    if (!trigger || pathname === '/' || pathname === '/ve-chung-toi')
      setLogo(
        'https://phuonglinhjsc.vn/file/img/1727319067929_logophuonglinhtrang.png',
      );
    if (trigger || (pathname !== '/' && pathname !== '/ve-chung-toi'))
      setLogo(
        'https://phuonglinhjsc.vn/file/img/1727319057300_logophuonglinhjscfooter140x100.png',
      );
  }, [trigger]);

  interface Props {
    window?: () => Window;
    children: React.ReactElement;
  }

  function ElevationScroll(props: Props) {
    const { children, window } = props;

    const trigger = useScrollTrigger({
      disableHysteresis: true,
      threshold: 64,
      target: window ? window() : undefined,
    });
    return React.cloneElement(children, {
      elevation: trigger ? 4 : 0,
      style: {
        backgroundColor:
          trigger || (pathname !== '/' && pathname !== '/ve-chung-toi')
            ? '#fff'
            : 'transparent', // Thay đổi màu nền khi cuộn
        transition: 'background-color 0.3s ease', // Thêm hiệu ứng chuyển đổi màu
        color:
          trigger || (pathname !== '/' && pathname !== '/ve-chung-toi')
            ? 'black'
            : '',
      },
    });
  }

  const handleMouseEnterItem = (index: number) => {
    setMenuIndex(index);
  };

  const onSignIn = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.SIGN_IN }));
  };

  const onSignUp = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.SIGN_UP }));
  };

  const dialogCloseAction = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.CLOSE }));
  };

  const dialogRenderer = () => {
    switch (dialogType) {
      case DIALOG_TYPE.SIGN_IN:
      case DIALOG_TYPE.SIGN_UP:
        return <AuthDialog dialogCloseAction={dialogCloseAction} />;
      default:
        return <></>;
    }
  };

  const upsertCollection = (collection: Collection) => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections/upsert`,
          collection,
        );

        refreshMenuTree();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const removeCollection = (collectionId: string) => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections/${collectionId}`,
        );

        refreshMenuTree();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const menu = () => {
    return (
      <>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            py: 1,
            flexDirection: 'column',
            borderRadius: 1,
            boxShadow:
              '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)',
            [theme.breakpoints.down('sm')]: {},
            width: '250px',
            height: '100%',
            fontFamily: 'inherit',
            overflow: 'auto',
          }}
        >
          <Box
            sx={{
              width: 'auto',
              height: '60px',
              // maxWidth: '100px',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: 'white',
              },
            }}
            onClick={() => {
              navigate('');
            }}
          >
            <img width="auto" alt="" height="100%" src={logo} loading="lazy" />
          </Box>
          <Typography
            sx={{
              px: 1,
              fontSize: '0.8rem',
              fontFamily: 'inherit',
              textTransform: 'uppercase',
              borderBottom: 1,
              borderColor: 'divider',
              fontWeight: 'bold',
            }}
            onClick={() => {
              navigate('/');
              setOpenDrawer(false);
            }}
          >
            Home
          </Typography>
          <Typography
            sx={{
              px: 1,
              fontSize: '0.8rem',
              textTransform: 'uppercase',
              borderBottom: 1,
              borderColor: 'divider',
              fontWeight: 'bold',
            }}
            onClick={() => {
              navigate('/ve-chung-toi');
              setOpenDrawer(false);
            }}
          >
            Về chúng tôi
          </Typography>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItem: 'center',
            }}
          >
            <Typography
              sx={{
                px: 1,
                fontSize: '0.8rem',
                textTransform: 'uppercase',
                borderBottom: 1,
                borderColor: 'divider',
                fontWeight: 'bold',
                display: 'flex',
                alignItem: 'center',
                gap: 1,
              }}
              onClick={() => {
                navigate('/san-pham');
                setOpenDrawer(false);
              }}
            >
              Sản phẩm
              {moreBrand ? (
                <KeyboardArrowUpIcon
                  fontSize="small"
                  onClick={e => {
                    setMoreBrand(false);
                    e.stopPropagation();
                  }}
                />
              ) : (
                <KeyboardArrowDownIcon
                  fontSize="small"
                  onClick={e => {
                    setMoreBrand(true);
                    e.stopPropagation();
                  }}
                />
              )}
            </Typography>
            <Box
              sx={{
                display: moreBrand ? 'flex' : 'none',
                flexDirection: 'column',
                gap: 1,
                pt: 1,
                // px: 2,
              }}
            >
              {menuTree?.map((item: Collection, index: number) => (
                <Typography
                  key={item._id}
                  sx={{
                    // fontSize: '0.9rem',
                    textTransform: 'capitalize',
                    borderBottom: 1,
                    borderColor: 'divider',
                    pl: 2,
                    cursor: 'pointer',
                    // fontWeight: 'bold',
                  }}
                  onClick={() => {
                    navigate(`/bo-suu-tap/${item.id}`);
                    setOpenDrawer(false);
                  }}
                >
                  {item.name}
                </Typography>
              ))}
            </Box>
          </Box>
          {/* <Typography
            sx={{
              px: 1,
              fontSize: '0.8rem',
              textTransform: 'uppercase',
              borderBottom: 1,
              borderColor: 'divider',
              fontWeight: 'bold',
            }}
            onClick={() => {
              navigate('/khuyen-mai');
              setOpenDrawer(false);
            }}
          >
            Khuyến mãi
          </Typography> */}
          <Typography
            sx={{
              px: 1,
              fontSize: '0.8rem',
              textTransform: 'uppercase',
              borderBottom: 1,
              borderColor: 'divider',
              fontWeight: 'bold',
            }}
            onClick={() => {
              navigate('/tin-tuc/tin-moi');
              setOpenDrawer(false);
            }}
          >
            Tin tức
          </Typography>
          <Typography
            sx={{
              px: 1,
              fontSize: '0.8rem',
              textTransform: 'uppercase',
              borderBottom: 1,
              borderColor: 'divider',
              fontWeight: 'bold',
            }}
            onClick={() => {
              navigate('/lien-he');
              setOpenDrawer(false);
            }}
          >
            Liên hệ
          </Typography>
        </Box>
      </>
    );
  };

  const findICon = (item: any) => {
    if (item.split(' ')[0].toLowerCase() === 'đồng') {
      return <WatchIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'loa') {
      return <SpeakerIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'tai') {
      return <HeadphonesIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'dây') {
      return <CableIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'cổng') {
      return <LoopIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'xạc') {
      return <ChargingStationIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'miếng') {
      return <SecurityUpdateGoodIcon sx={{ color: 'orangered' }} />;
    } else if (item.split(' ')[0].toLowerCase() === 'ốp') {
      return <SmartphoneIcon sx={{ color: 'orangered' }} />;
    } else {
      return <PowerIcon sx={{ color: 'orangered' }} />;
    }
  };

  const visible = useWindowScrollToTop();
  const [heightToolBar, setHeightToolBar] = React.useState(0);
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<any>(null);
  useEffect(() => {
    if (containerRef.current) {
      setHeightToolBar(containerRef.current.offsetHeight);
      // console.log('height: ', containerRef.current.offsetHeight);
    }
  }, [width]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <SwipeableDrawer
        anchor={'left'}
        open={openDrawer}
        onClose={() => setOpenDrawer(false)}
        onOpen={() => setOpenDrawer(true)}
        // PaperProps={{
        //     sx: {
        //       bgcolor: 'transparent',
        //       p: { md: 3, sm: 0 },
        //       boxShadow: 'none',
        //     },
        // }}
      >
        {menu()}
      </SwipeableDrawer>
      <Paper
        sx={{
          width: '100%',
          height: '100vh',
          // bgcolor: 'red',
          position: 'relative',
          // overflow: 'hidden'
        }}
      >
        {/* <ScrollToTop smooth /> */}
        {/* <Slide appear={false} direction="down" in={!trigger} ref={containerRef}> */}
        <ElevationScroll {...props}>
          <AppBar>
            <Box
              ref={containerRef}
              sx={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  width: '100%',
                  height: '80px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    // bgcolor: 'white',
                  },
                  [theme.breakpoints.down('sm')]: {
                    height: '60px',
                    p: 1,
                  },
                }}
                onClick={() => {
                  navigate('');
                }}
              >
                <img
                  width={'auto'}
                  height={'100%'}
                  alt="logo"
                  src={logo}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 3,
                  width: '100%',
                }}
              >
                <TopBar
                  changeLanguage={changeLanguage}
                  onSignIn={onSignIn}
                  onSignUp={onSignUp}
                  list={optionCategory}
                />
                <Box
                  display={'flex'}
                  width={1}
                  // height={1}
                  sx={{
                    // background: 'green',
                    justifyContent: 'center',
                    alignItems: 'center',
                    [theme.breakpoints.down('md')]: {
                      height: 'fit-content',
                      justifyContent: 'left',
                    },
                    [theme.breakpoints.down('sm')]: {
                      height: '100%',
                    },
                  }}
                >
                  <Stack
                    direction="row"
                    spacing={2}
                    sx={{
                      width: '100%',
                      display: 'flex',
                      color: 'inherit',
                      fontWeight: 'bold',
                      alignItems: 'center',
                      justifyContent: 'end',
                      mx: 2,
                      mr: 5,
                      mb: 1,
                      // cursor: 'pointer',
                      [theme.breakpoints.down('md')]: {
                        // justifyContent: 'start',
                      },
                      [theme.breakpoints.down('sm')]: {
                        mr: 1,
                      },
                    }}
                  >
                    <Link
                      underline="none"
                      href="/"
                      sx={{
                        height: '40px',
                        alignItems: 'end',
                        color: 'inherit',
                        // bgcolor: 'green',
                        fontSize: '14px',
                        fontWeight: 'inherit',
                        fontFamily: 'inherit',
                        textDecoration: 'none',
                        boxSizing: 'border-box',
                        // borderBottom: pathname === '/' ? 3 : 'none',
                        p: 1,
                        cursor: 'pointer',
                        '&:hover': {
                          // color: 'black',
                          borderBottom: 3,
                        },
                        [theme.breakpoints.down('md')]: {
                          display: 'none',
                        },
                      }}
                      // onClick={e => navigate('/')}
                    >
                      {/* <HomeIcon /> */}
                      <Typography
                        sx={{
                          fontWeight: 300,
                          fontFamily: 'inherit',
                        }}
                      >
                        Trang Chủ
                      </Typography>
                    </Link>
                    <Link
                      underline="none"
                      href="/ve-chung-toi"
                      sx={{
                        height: '40px',
                        color: 'inherit',
                        // bgcolor: 'green',
                        fontSize: '14px',
                        textDecoration: 'none',
                        p: 1,
                        // borderBottom: pathname === '/ve-chung-toi' ? 3 : 'none',
                        cursor: 'pointer',
                        '&:hover': {
                          // color: 'black',
                          borderBottom: 3,
                        },
                        [theme.breakpoints.down('md')]: {
                          display: 'none',
                        },
                      }}
                      // onClick={e => navigate('/ve-chung-toi')}
                    >
                      <Typography
                        sx={{
                          fontWeight: 300,
                          height: '20px',
                          fontFamily: 'inherit',
                        }}
                      >
                        Về Chúng Tôi
                      </Typography>
                    </Link>
                    {/* <BrandMenuPopover /> */}
                    {/* <Box
                      sx={{
                        display: 'inline-block',
                      }}
                    >
                      <Typography
                        // underline="none"
                        // href="/bo-suu-tap"
                        sx={{
                          color: 'inherit',
                          // bgcolor: 'green',
                          display: 'block',
                          alignItems: 'center',
                          p: 1,
                          fontWeight: 'inherit',
                          cursor: 'pointer',
                          '&:hover': {
                            color: 'black',
                          },
                          [theme.breakpoints.down('md')]: {
                            display: 'none',
                          },
                        }}
                        aria-owns={open ? 'mouse-over-popover' : undefined}
        aria-haspopup="true"
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
                        // onMouseEnter={handlePopoverOpen}
                        // onMouseLeave={handlePopoverClose}
                      >
                        Thương hiệu <KeyboardArrowDownIcon fontSize="small" />
                      </Typography>
                    </Box> */}
                    {/* <Link
                  underline="none"
                  href="/khuyen-mai"
                  sx={{
                    color: 'white',
                    bgcolor: 'green',
                    fontSize: '14px',
                    p: 1,
                    cursor: 'pointer',
                    '&:hover': {
                      color: 'black',
                    },
                    [theme.breakpoints.down('md')]: {
                      display: 'none',
                    },
                  }}
                >
                  <Typography>Khuyến Mãi</Typography>
                </Link> */}
                    <Link
                      underline="none"
                      href="/tin-tuc/tin-moi"
                      sx={{
                        height: '40px',
                        color: 'inherit',
                        // bgcolor: 'green',
                        fontSize: '14px',
                        p: 1,
                        // borderBottom:
                        //   pathname === '/tin-tuc/tin-moi' ? 3 : 'none',
                        cursor: 'pointer',
                        '&:hover': {
                          // color: 'black',
                          borderBottom: 3,
                        },
                        [theme.breakpoints.down('md')]: {
                          display: 'none',
                        },
                      }}
                    >
                      <Typography
                        sx={{
                          fontWeight: 300,
                          height: '20px',
                          fontFamily: 'inherit',
                        }}
                      >
                        Tin Tức
                      </Typography>
                    </Link>
                    <Link
                      underline="none"
                      href="/lien-he"
                      sx={{
                        height: '40px',
                        color: 'inherit',
                        // bgcolor: 'green',
                        fontSize: '14px',
                        p: 1,
                        // borderBottom: pathname === '/lien-he' ? 3 : 'none',
                        cursor: 'pointer',
                        '&:hover': {
                          // color: 'black',
                          borderBottom: 3,
                        },
                        [theme.breakpoints.down('md')]: {
                          display: 'none',
                        },
                      }}
                    >
                      <Typography
                        sx={{
                          fontWeight: 300,
                          height: '20px',
                          fontFamily: 'inherit',
                        }}
                      >
                        Liên Hệ
                      </Typography>
                    </Link>
                    <Link
                      underline="none"
                      href="/san-pham"
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        color: 'white',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        fontSize: '14px',
                        p: 1,
                        py: 0.5,
                        borderRadius: 5,
                        cursor: 'pointer',
                        '&:hover': {
                          // color: 'black',
                        },
                        [theme.breakpoints.down('md')]: {
                          display: 'none',
                        },
                      }}
                    >
                      <Typography
                        sx={{
                          fontWeight: 300,
                          fontFamily: 'inherit',
                        }}
                      >
                        Trang sản phẩm
                      </Typography>
                      <ArrowForwardIcon
                        sx={{
                          fontSize: '14px',
                          bgcolor: 'white',
                          color: 'black',
                          borderRadius: '100%',
                        }}
                      />
                    </Link>
                    <Box
                      sx={{
                        width: '250px',
                        px: 2,
                        border: 0.5,
                        borderColor: 'gray',
                        borderRadius: 5,
                        [theme.breakpoints.down('sm')]: {
                          width: 'fit-content',
                          border: 'none',
                        },
                      }}
                    >
                      <Box
                        color="inherit"
                        aria-label="search"
                        onClick={handleOpenSearch}
                        sx={{
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          py: 0.5,
                          cursor: 'pointer',
                          position: 'relative',
                          '&:hover': {
                            // background: alpha(theme.palette.primary.main, 0.08),
                          },
                          transition: 'all 0.2s ease-in-out',
                          // [theme.breakpoints.down('sm')]: {
                          //   display: 'none',
                          // },
                        }}
                      >
                        <SearchIcon />
                        <Typography
                          sx={{
                            fontSize: '14px',
                            color: 'gray',
                            fontWeight: 500,
                            fontStyle: 'italic',
                            [theme.breakpoints.down('sm')]: {
                              display: 'none',
                            },
                          }}
                        >
                          Tìm kiếm...
                        </Typography>
                      </Box>
                      {/* <SearchMenu
                        dialogOpen={openSearch}
                        onCloseDialog={handleCloseSearch}
                        onOpenDialog={handleOpenSearch}
                      /> */}
                    </Box>
                    <Item
                      sx={{
                        height: 'fit-content',
                        display: 'flex',
                        alignItems: 'end',
                        color: 'inherit',
                        bgcolor: 'transparent',
                        fontSize: '14px',
                        left: 0,
                        '&:hover': {
                          color: 'black',
                        },
                        [theme.breakpoints.up('md')]: {
                          display: 'none',
                        },
                        [theme.breakpoints.down('sm')]: {
                          mt: -20,
                        },
                      }}
                      onClick={e => setOpenDrawer(true)}
                    >
                      <MenuIcon />
                    </Item>
                  </Stack>
                </Box>
              </Box>
            </Box>
          </AppBar>
        </ElevationScroll>
        <Toolbar
          sx={{
            display:
              pathname !== '/' && pathname !== '/ve-chung-toi' ? '' : 'none',
            height: heightToolBar,
          }}
        />
        {/* <Box
          sx={{
            px: 1,
            m: 1,
            border: 1,
            borderRadius: 1,
            [theme.breakpoints.up('md')]: {
              display: 'none',
            },
          }}
        >
          <SearchMenu />
        </Box> */}
        <Paper
          component="main"
          sx={{
            flexGrow: 1,
            width: '100%',
            height: 'auto',
            // position: 'relative',
          }}
        >
          <Box
            sx={{
              width: '100%',
              height: 'auto',
              overflow: 'hidden',
              // Padding bottom for bottom bar show
              [theme.breakpoints.down('md')]: {
                pb: '56px',
                px: 1,
              },
            }}
          >
            <main> {props.children} </main>

            {dialogRenderer()}
            <Box
              sx={{
                mt: 'auto',
              }}
            >
              <Footer />
            </Box>
          </Box>
        </Paper>
      </Paper>
      <Box
        sx={{
          width: 'fit-content',
          display: 'flex',
          flexDirection: 'column',
          position: 'fixed',
          // bgcolor: 'green',
          right: '0px',
          bottom: '10px',
          p: 5,
          [theme.breakpoints.down('md')]: {
            p: 2,
          },
        }}
      >
        <Box
          sx={{
            display: visible ? 'flex' : 'none',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          <IconButton
            sx={{
              width: '60px',
              height: '60px',
              animation: `${upUp} 1s ease-in-out infinite alternate`,
            }}
            onClick={scrollToTop}
          >
            {/* <img src={zalo} alt="" width={'100%'} height={'100%'} /> */}
            <KeyboardDoubleArrowUpIcon
              sx={{
                fontSize: '25px',
                color: 'orangered',
              }}
            />
          </IconButton>
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          <Box
            sx={{
              width: '60px',
              height: '60px',
              position: 'absolute',
              bgcolor: 'red',
              borderRadius: '100%',
              // transform: 'scale(2)',
              animation: `${scaleUp1} 2s ease-in-out infinite`,
            }}
          ></Box>
          <IconButton
            sx={{
              width: '60px',
              height: '60px',
              animation: `${scaleUp2} 1s ease-in-out infinite alternate`,
            }}
            onClick={handleClickZalo}
          >
            <img
              src={zalo}
              alt=""
              width={'100%'}
              height={'100%'}
              loading="lazy"
            />
          </IconButton>
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          {/* <Box
            sx={{
              width: '60px',
              height: '60px',
              position: 'absolute',
              bgcolor: 'red',
              borderRadius: '100%',
              // transform: 'scale(2)',
              animation: `${scaleUp1} 2s ease-in-out infinite`,
            }}
          ></Box> */}
          <IconButton
            sx={{
              width: '60px',
              height: '60px',
              animation: `${scaleUp2} 1s ease-in-out infinite alternate`,
              // overflow: 'hidden',
            }}
            onClick={handleClickMess}
          >
            <img
              src={messenger}
              alt=""
              width={'100%'}
              height={'100%'}
              loading="lazy"
            />
            {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 800">
              <defs>
                <radialGradient
                  id="a"
                  cx="101.9"
                  cy="809"
                  r="1.1"
                  gradientTransform="matrix(800 0 0 -800 -81386 648000)"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#09f" />
                  <stop offset=".6" stopColor="#a033ff" />
                  <stop offset=".9" stopColor="#ff5280" />
                  <stop offset="1" stopColor="#ff7061" />
                </radialGradient>
              </defs>
              <path
                fill="url(#a)"
                d="M400 0C174.7 0 0 165.1 0 388c0 116.6 47.8 217.4 125.6 287 6.5 5.8 10.5 14 10.7 22.8l2.2 71.2a32 32 0 0 0 44.9 28.3l79.4-35c6.7-3 14.3-3.5 21.4-1.6 36.5 10 75.3 15.4 115.8 15.4 225.3 0 400-165.1 400-388S625.3 0 400 0z"
              />
              <path
                fill="#FFF"
                d="m159.8 501.5 117.5-186.4a60 60 0 0 1 86.8-16l93.5 70.1a24 24 0 0 0 28.9-.1l126.2-95.8c16.8-12.8 38.8 7.4 27.6 25.3L522.7 484.9a60 60 0 0 1-86.8 16l-93.5-70.1a24 24 0 0 0-28.9.1l-126.2 95.8c-16.8 12.8-38.8-7.3-27.5-25.2z"
              />
            </svg> */}
          </IconButton>
        </Box>
      </Box>
      <Dialog
        open={openSearch}
        onClose={handleCloseSearch}
        TransitionComponent={Transition}
        fullWidth
        maxWidth="lg"
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
            overflow: 'hidden',
            height: '90vh',
            maxHeight: '90vh',
            background: theme.palette.background.default,
          },
        }}
      >
        <IconButton
          aria-label="close"
          onClick={handleCloseSearch}
          sx={{
            position: 'absolute',
            top: 2,
            right: 2,
            color: theme.palette.grey[500],
            transition: 'all 0.2s',
            '&:hover': {
              color: theme.palette.primary.main,
              transform: 'rotate(90deg)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
        <SearchMenu
          dialogOpen={openSearch}
          onCloseDialog={handleCloseSearch}
          onOpenDialog={handleOpenSearch}
        />
      </Dialog>
    </ThemeProvider>
  );
}
