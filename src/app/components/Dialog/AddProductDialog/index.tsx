/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import {
  Button,
  Card,
  Checkbox,
  FormControl,
  IconButton,
  ListItemIcon,
  Tooltip,
  Typography,
  useMediaQuery,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import TextField from '@mui/material/TextField';
import CloseIcon from '@mui/icons-material/Close';
import { LoadingIndicator } from '../../LoadingIndicator';
import { useDispatch } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import QueueIcon from '@mui/icons-material/Queue';
import get from 'lodash/get';
import set from 'lodash/set';
import { filterArrayByProperty } from 'utils/app';
import Autocomplete from '@mui/material/Autocomplete';
import s3Client from 'utils/s3';
import { uploadFiles } from 'utils/uploadImg';
import { request } from 'utils/request';
import { CollectionType } from 'app/components/AppWrapper';
import { useParams, useSearchParams } from 'react-router-dom';
import InputFileUpload from 'app/components/InputFileUpload';
import Draggable from 'react-draggable';

export default function AddProductDialog({
  open = false,
  onClose,
  params: initParams,
}) {
  const theme = useTheme();
  const dispatch = useDispatch();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { path: collectionId } = params;
  const brandId = searchParams.get('brand');
  const categoryId = searchParams.get('category');
  const { product: initProduct = {} } = initParams;

  const { actions: utilsAction } = useUtilsSlice();
  const [loading, setLoading] = useState(false);
  const [imageIndex, setImageIndex] = useState<number>(-1);

  const [allCollection, setAllCollection] = useState<any[]>([]);
  const [brandColl, setBrandColl] = useState<any[]>([]);
  const [categoryColl, setCategoryColl] = useState<any[]>([]);
  const [categoryList, setCategoryList] = React.useState<any[]>([]);
  const [brandList, setBrandList] = React.useState<any[]>([]);
  const [partnerList, setPartnerList] = React.useState<any[]>([]);
  const [brandDefault, setBrandDefault] = useState<any>();
  const [categoryDefault, setCategoryDefault] = useState<any>();
  const [paertnerDefault, setPartnerDefault] = useState<any>();
  const [groupColl, setGroupColl] = useState<any[]>([]);
  const [variants, setVariants] = useState<any[]>(initProduct?.variants || []);
  const [groupDefault, setGroupDefault] = useState<any>();
  const [isOpenVariant, setIsOpenVariant] = useState<boolean>(false);

  const [product, setProduct] = useState<any>({
    name: '',
    collections: [],
    brands: [],
    categories: [],
    partners: [],
    description: '',
    images: [],
    priceOrigin: 0,
    price: 0,
    ...initProduct,
  });

  // console.log('_id: ', initProduct._id);

  const fetchAllCollection = () => {
    const fetchAPI = async () => {
      const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`;
      try {
        const collectionList: any = await request('get', url);
        if (collectionList && collectionList.length) {
          setAllCollection(collectionList);
        }
      } catch (ignore) {
        // console.log('Ignore', ignore);
      }
    };

    fetchAPI();
  };

  const handleDrag = (e: any, data: any, index: number) => {
    const newImages = [...product.images];
    const [draggedImage] = newImages.splice(index, 1);

    const newIndex = Math.max(
      0,
      Math.min(newImages.length, Math.floor(data.x / 150)),
    );
    newImages.splice(newIndex, 0, draggedImage);

    setProduct({ ...product, images: newImages });
  };

  const moveItemToTop = (data: any[], index: number) => {
    // Kiểm tra các trường hợp đặc biệt
    if (!Array.isArray(data)) {
      throw new Error('Dữ liệu đầu vào phải là một mảng');
    }

    if (index < 0 || index >= data.length) {
      throw new Error('Index không hợp lệ');
    }

    // Nếu phần tử đã ở đầu mảng, không cần di chuyển
    if (index === 0) {
      return [...data];
    }

    // Tạo một bản sao của mảng để tránh thay đổi mảng gốc
    const result = [...data];

    // Lấy phần tử cần di chuyển
    const itemToMove = result[index];

    // Xóa phần tử khỏi vị trí hiện tại
    result.splice(index, 1);

    // Thêm phần tử vào đầu mảng
    result.unshift(itemToMove);

    setProduct({ ...product, images: result });
  };

  const handleVariantImageSelection = (variantIndex, image) => {
    const newVariants = [...variants];

    // Đảm bảo mảng images đã được khởi tạo
    if (!newVariants[variantIndex].images) {
      newVariants[variantIndex].images = [];
    }

    // Tìm index của ảnh trong mảng images (nếu có)
    const imageIndex = newVariants[variantIndex].images.findIndex(
      img => img.url === image.url,
    );

    // Nếu ảnh đã tồn tại, xóa khỏi mảng
    if (imageIndex !== -1) {
      newVariants[variantIndex].images.splice(imageIndex, 1);
    } else {
      // Nếu chưa tồn tại, thêm vào mảng
      newVariants[variantIndex].images.push(image);
    }

    setVariants(newVariants);
  };

  const fetchBrands = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands`,
          {},
        );

        // console.log('brands: ', result);
        setBrandList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchCategories = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/category`,
          {},
        );

        // console.log('CATEGORY: ', result);
        setCategoryList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const fetchPartners = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners`,
          {},
        );

        // console.log('CATEGORY: ', result);
        setPartnerList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchProduct = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/products/${initProduct._id}`,
          {},
        );

        console.log('product: ', result);
        setProduct(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    if (initProduct._id) {
      apiHandler();
    }
  };

  useEffect(() => {
    // fetchAllCollection();
    fetchBrands();
    fetchCategories();
    fetchPartners();
    fetchProduct();
  }, []);

  useEffect(() => {
    setProduct({
      ...product,
      ...initProduct,
    });
  }, [initProduct]);

  interface Files {
    url: string;
    name: string;
  }
  const handleFilesSelected = (files: Files[]) => {
    console.log('Selected files:', files);
    const images = [...product.images];
    images.push(...Array.from(files));
    const newImages = filterArrayByProperty(images, 'name _id');
    const newProduct = { ...product };
    newProduct.images = newImages;
    console.log('Selected files newProduct: ', newProduct);
    setProduct(newProduct);
    // Additional logic to handle the files
  };

  const onHandleAddProduct = () => {
    const main = async () => {
      // const images = get(product, 'images', []);
      // const imageUrls = await Promise.all(
      //   images.map(async (image: any) => {
      //     let { file, name, url = '' } = image;
      //     if (file && name) {
      //       // const uploadInfo = await s3Client.uploadFile(
      //       //   `phuonglinh/productImage/${Date.now()}-${name}`,
      //       //   file,
      //       // );

      //       // url = get(uploadInfo, 'Location');
      //       url = await uploadFiles([file], 'product', '');
      //     }

      //     return { url };
      //   }),
      // );

      const collections: any[] = [];
      const brandId = brandDefault?._id;
      const categoryId = categoryDefault?._id;
      const groupId = groupDefault?._id;
      if (brandId) collections.push(brandId);
      if (categoryId) collections.push(categoryId);
      if (groupId) collections.push(groupId);

      const productData = {
        name: product.name,
        description: product.description,
        collections,
        brands: product.brands,
        categories: product.categories,
        partners: product.partners,
        images: product.images,
        priceOrigin: product.priceOrigin,
        price: product.price,
        variants,
      };

      let method: string;
      let url: string;
      if (product._id) {
        // Update product
        method = 'put';
        url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/products/${product._id}`;
      } else {
        // create product
        method = 'post';
        url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/products`;
      }

      try {
        await request(method, url, productData);
        onClose();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    main();
  };

  return (
    <MuiDialog
      fullScreen
      disableEnforceFocus
      // fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
      // fullWidth={true}
      maxWidth="md"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          borderRadius: 2,
          backgroundColor: '#efeff1',
          margin: 0,
          // height: '80vh',
          [theme.breakpoints.down('sm')]: {
            height: '100vh',
          },
          overflow: 'hidden',
        },
      }}
    >
      <DialogContent
        sx={{
          padding: 0,
          overflow: 'hidden',
        }}
      >
        {loading && <LoadingIndicator />}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            [theme.breakpoints.down('sm')]: {
              flexDirection: 'column',
            },
            width: '100%',
            height: '100%',
            overflow: 'auto',
          }}
        >
          <IconButton
            className="rotateZ"
            sx={{
              p: 1,
              position: 'absolute',
              right: '32px',
              top: '20px',
              color: 'white',
              backgroundColor: '#63636735',
            }}
            aria-label="toggle password visibility"
            onClick={onClose}
            onMouseDown={() => {}}
            edge="end"
          >
            <CloseIcon fontSize="small" />
          </IconButton>
          <Box
            sx={{
              [theme.breakpoints.down('sm')]: {
                flex: 30 / 100,
                display: 'none',
              },
            }}
          ></Box>
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              p: 7,
              [theme.breakpoints.down('sm')]: {},
            }}
          >
            <Typography
              sx={{
                width: 'fit-content',
                p: 1,
                mx: 'auto',
                mb: 1,
                color: 'orangered',
                textTransform: 'uppercase',
                borderRadius: 5,
                boxShadow:
                  '0px 0px 2px 1px rgba(37, 197, 255, 0.683), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              {product.brands[0]?.name}
            </Typography>
            <Typography
              sx={{
                width: 'fit-content',
                px: 1,
                mx: 'auto',
                mb: 1,
                color: '#55b8ff',
                textTransform: 'capitalize',
                borderRadius: 5,
                boxShadow:
                  '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              {product.categories?.map((item: any) => item.name).join(', ')}
            </Typography>
            <Autocomplete
              multiple
              // readOnly={!isEmpty(categoryDefault)}
              id="tags-standard"
              options={categoryList}
              getOptionLabel={option => {
                return option.name || '';
              }}
              // inputValue={get(categoryDefault, 'name', '')}
              value={product.categories || []}
              onChange={(e: any, value: any) => {
                // setCategoryDefault(value);
                const newProduct = {
                  ...product,
                };
                newProduct.categories = value;
                setProduct(newProduct);
              }}
              renderInput={params => (
                <TextField
                  {...params}
                  variant="standard"
                  label="Danh mục sản phẩm"
                  placeholder="Thêm"
                />
              )}
            />
            <Autocomplete
              id="tags-standard"
              options={brandList}
              onChange={(e: any, value: any) => {
                const newProduct = {
                  ...product,
                };
                newProduct.brands = [value];
                setProduct(newProduct);
                // setGroupDefault(value);
              }}
              getOptionLabel={option => {
                return option.name || '';
              }}
              // inputValue={get(groupDefault, 'name', '')}
              value={product.brands[0]}
              renderInput={params => (
                <TextField
                  {...params}
                  variant="standard"
                  label="Thương hiệu"
                  placeholder="Thêm"
                />
              )}
            />
            <TextField
              id="standard-basic"
              label="Tên sản phẩm"
              variant="standard"
              onChange={event => {
                const text = event.target.value;
                setProduct({
                  ...product,
                  name: text,
                });
              }}
              value={product.name}
            />
            <Autocomplete
              multiple
              // readOnly={!isEmpty(categoryDefault)}
              id="tags-standard"
              options={partnerList}
              getOptionLabel={option => {
                return option.name || '';
              }}
              // inputValue={get(categoryDefault, 'name', '')}
              value={product.partners || []}
              onChange={(e: any, value: any) => {
                // setCategoryDefault(value);
                const newProduct = {
                  ...product,
                };
                newProduct.partners = value;
                setProduct(newProduct);
              }}
              renderInput={params => (
                <TextField
                  {...params}
                  variant="standard"
                  label="Được bán tại"
                  placeholder="Thêm"
                  sx={{
                    textTransform: 'capitalize',
                  }}
                />
              )}
            />
            <TextField
              id="standard-basic"
              multiline
              rows={4}
              label="Mô tả"
              variant="standard"
              onChange={event => {
                const text = event.target.value;
                setProduct({
                  ...product,
                  description: text,
                });
              }}
              value={product.description}
            />
            <TextField
              id="standard-basic"
              label="Giá gốc"
              variant="standard"
              onChange={event => {
                const text = event.target.value;
                setProduct({
                  ...product,
                  priceOrigin: Number(text),
                });
              }}
              value={product.priceOrigin}
            />
            <TextField
              id="standard-basic"
              label="Giá bán"
              variant="standard"
              onChange={event => {
                const text = event.target.value;
                setProduct({
                  ...product,
                  price: Number(text),
                });
              }}
              value={product.price}
            />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                mt: 2,
                p: 1,
                border: '1px solid gray',
              }}
            >
              <Button
                variant="outlined"
                sx={{
                  width: 'fit-content',
                }}
                onClick={() => setIsOpenVariant(true)}
              >
                Thuộc tính
              </Button>
              <Box
                sx={{
                  display: isOpenVariant ? 'flex' : 'none',
                  flexDirection: 'column',
                  gap: 1,
                }}
              >
                {variants?.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      p: 4,
                      border: '1px solid gray',
                      borderRadius: 1,
                      position: 'relative',
                    }}
                  >
                    <IconButton
                      onClick={() => {
                        const newVariant = [...variants];
                        newVariant.splice(index, 1);
                        setVariants(newVariant);
                      }}
                      sx={{
                        position: 'absolute',
                        top: 2,
                        right: 2,
                        color: 'red',
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                    <Card
                      sx={{
                        display: 'flex',
                        gap: 1,
                        alignItems: 'center',
                        p: 1,
                        mb: 1,
                      }}
                    >
                      <TextField
                        size="small"
                        label="màu sắc"
                        value={item.color}
                        onChange={(e: any) => {
                          const newVariant = [...variants];
                          newVariant[index] = {
                            ...newVariant[index],
                            color: e.target.value,
                          };
                          setVariants(newVariant);
                          // setProduct({ ...product, variant: newVariant });
                        }}
                      />
                      <TextField
                        size="small"
                        label="size"
                        value={item.size}
                        onChange={(e: any) => {
                          const newVariant = [...variants];
                          newVariant[index] = {
                            ...newVariant[index],
                            size: e.target.value,
                          };
                          setVariants(newVariant);
                          // setProduct({ ...product, variant: newVariant });
                        }}
                      />
                      <TextField
                        size="small"
                        label="model"
                        value={item.model}
                        onChange={(e: any) => {
                          const newVariant = [...variants];
                          newVariant[index] = {
                            ...newVariant[index],
                            model: e.target.value,
                          };
                          setVariants(newVariant);
                          // setProduct({ ...product, variant: newVariant });
                        }}
                      />
                      <TextField
                        size="small"
                        label="giá"
                        value={item.price}
                        onChange={(e: any) => {
                          const newVariant = [...variants];
                          newVariant[index] = {
                            ...newVariant[index],
                            price: e.target.value,
                          };
                          setVariants(newVariant);
                          // setProduct({ ...product, variant: newVariant });
                        }}
                      />
                    </Card>
                    <Card
                      sx={{
                        p: 1,
                      }}
                    >
                      <Typography>
                        Chỉ định hình ảnh theo màu hiện tại
                      </Typography>
                      <Box
                        width={1}
                        height={1}
                        display={'flex'}
                        gap={2}
                        overflow={'auto'}
                      >
                        {product.images.map(
                          (itemImg: any, indexImg: number) => (
                            <Box
                              key={indexImg}
                              height={'fit-content'}
                              display={'flex'}
                              justifyContent={'center'}
                              alignItems={'center'}
                              position={'relative'}
                              border={1}
                              onMouseEnter={() => setImageIndex(index)}
                              onMouseLeave={() => setImageIndex(-1)}
                            >
                              <Box
                                width={128}
                                height={128}
                                component="img"
                                src={itemImg.url}
                                loading="lazy"
                                sx={{
                                  backgroundSize: 'cover',
                                  backgroundRepeat: 'no-repeat',
                                }}
                              ></Box>
                              <Checkbox
                                size="small"
                                checked={variants[index].images
                                  ?.map(el => el.url)
                                  .includes(itemImg.url)}
                                onChange={() =>
                                  handleVariantImageSelection(index, itemImg)
                                }
                                inputProps={{ 'aria-label': 'controlled' }}
                                sx={{
                                  position: 'absolute',
                                  top: -3,
                                  right: -3,
                                }}
                              />
                              {/* <IconButton
                                onClick={() => {
                                  const images = [...product.images];
                                  images.splice(index, 1);
                                  const newImages = filterArrayByProperty(
                                    images,
                                    'name _id',
                                  );
                                  const newProduct = { ...product };
                                  newProduct.images = newImages;
                                  setProduct(newProduct);
                                }}
                                sx={{
                                  position: 'absolute',
                                  bgcolor: 'red',
                                  '&:hover': {
                                    bgcolor: 'red',
                                  },
                                  display: imageIndex === index ? '' : 'none',
                                }}
                              >
                                <DeleteIcon />
                              </IconButton> */}
                            </Box>
                          ),
                        )}
                      </Box>
                    </Card>
                  </Box>
                ))}
                <Tooltip title="Thêm thuộc tính mới">
                  <IconButton
                    onClick={() => {
                      const newVariant = [...variants];
                      newVariant.push({
                        color: 'màu mới',
                        images: [],
                      });
                      setVariants(newVariant);
                    }}
                    sx={{
                      width: 'fit-content',
                    }}
                  >
                    <QueueIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            <Box width={1} height={1} marginTop={2}>
              <Typography
                sx={{
                  fontStyle: 'italic',
                  color: 'orangered',
                  display: product.images.length > 0 ? '' : 'none',
                }}
              >
                * Note: click vào ảnh bất kỳ để di chuyển đến đầu list
              </Typography>
              <Box
                width={1}
                height={1}
                marginTop={2}
                display={'flex'}
                gap={2}
                overflow={'auto'}
                zIndex={10}
              >
                {product.images.map((item, index) => (
                  <Box
                    height={'fit-content'}
                    display={'flex'}
                    justifyContent={'center'}
                    alignItems={'center'}
                    position={'relative'}
                    onMouseEnter={() => setImageIndex(index)}
                    onMouseLeave={() => setImageIndex(-1)}
                    onClick={() => {
                      moveItemToTop(product.images, index);
                    }}
                  >
                    <img
                      src={item.url}
                      alt={item.name}
                      loading="lazy"
                      crossOrigin="anonymous"
                      style={{
                        width: 128,
                        height: 128,
                        backgroundSize: 'cover',
                        backgroundRepeat: 'no-repeat',
                      }}
                    />
                    <IconButton
                      onClick={() => {
                        const images = [...product.images];
                        images.splice(index, 1);
                        const newImages = filterArrayByProperty(
                          images,
                          'name _id',
                        );
                        const newProduct = { ...product };
                        newProduct.images = newImages;
                        setProduct(newProduct);
                      }}
                      sx={{
                        position: 'absolute',
                        bgcolor: 'red',
                        '&:hover': {
                          bgcolor: 'red',
                        },
                        display: imageIndex === index ? '' : 'none',
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                  // <Draggable
                  //   key={item.url}
                  //   axis="x"
                  //   bounds="parent"
                  //   position={{ x: 0, y: 0 }}
                  //   // onStop={(e, data) => handleDrag(e, data, index)}
                  // >
                  // </Draggable>
                ))}
              </Box>
            </Box>
            <Box
              sx={{
                width: '100%',
                position: 'relative',
                mt: 10,
              }}
            >
              <InputFileUpload
                onFilesSelected={handleFilesSelected}
                path={product.name || 'product'}
              />
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            p: 1,
            ml: 'auto',
          }}
        >
          <Button
            variant="outlined"
            sx={
              {
                // color: 'green',
              }
            }
            onClick={() => onHandleAddProduct()}
          >
            OK
          </Button>
          <Button
            sx={{
              color: 'orange',
            }}
            onClick={onClose}
          >
            Huỷ
          </Button>
        </Box>
      </DialogActions>
    </MuiDialog>
  );
}
