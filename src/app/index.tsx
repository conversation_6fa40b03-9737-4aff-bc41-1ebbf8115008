/**
 *
 * App
 *
 * This component is the skeleton around the actual pages, and should only
 * contain code that should be seen on all pages. (e.g. navigation bar)
 */

import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

import { GlobalStyle } from 'styles/global-styles';

import { HomePage } from './pages/HomePage/Loadable';
import { DetailPage } from './pages/DetailPage/Loadable';
import { ListProductPage } from './pages/ListProduct/Loadable';
import { ProductPage } from './pages/ProductPage/Loadable';
import { PromotionPage } from './pages/PromotionPage/Loadable';
import { NewsPage } from './pages/NewsPage/Loadable';
import { NewsInfoPage } from './pages/NewsInfoPage/Loadable';
import { AboutPage } from './pages/AboutPage/Loadable';
import { OrderPage } from './pages/OrderPage/Loadable';
import { ContactPage } from './pages/ContactPage/Loadable';
import { AdminPage } from './pages/AdminPage/Loadable';
import { TermsPage } from './pages/TermsPage/Loadable';
import { FileManagerPage } from './pages/FilePage/Loadable';
import { NotFoundPage } from './pages/NotFoundPage/Loadable';
import { useTranslation } from 'react-i18next';
import { SnackbarProvider, VariantType, enqueueSnackbar } from 'notistack';
import AppWrapper from './components/AppWrapper';
import { useDispatch, useSelector } from 'react-redux';
import { useUtilsSlice } from './slice/utils';
import { snackbarSelector } from './slice/utils/selectors';

export function App() {
  const { i18n } = useTranslation();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const snackbarData = useSelector(snackbarSelector);
  const changeLanguage = (lng: string) => i18n.changeLanguage(lng);

  const showSnackbar = (messages, variant: VariantType) => {
    enqueueSnackbar(messages, {
      variant,
      onExit: () => {
        dispatch(utilsAction.showSnackbar({ message: '', variant: 'default' }));
      },
    });
  };

  React.useEffect(() => {
    if (snackbarData.message) {
      showSnackbar(snackbarData.message, snackbarData.variant);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [snackbarData]);

  return (
    <BrowserRouter>
      <GlobalStyle />
      <SnackbarProvider>
        <AppWrapper changeLanguage={changeLanguage}>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/home" element={<HomePage />} />
            <Route path="/san-pham" element={<ProductPage />} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/san-pham/:path" element={<DetailPage />} />
            <Route path="/bo-suu-tap" element={<ListProductPage />} />
            <Route path="/bo-suu-tap/:path" element={<ListProductPage />} />
            <Route path="/khuyen-mai/" element={<PromotionPage />} />
            <Route path="/tin-tuc/tin-moi" element={<NewsPage />} />
            <Route path="/tin-tuc/khuyen-mai" element={<NewsPage />} />
            <Route path="/tin-tuc/thu-thuat" element={<NewsPage />} />
            <Route path="/tin-tuc/video" element={<NewsPage />} />
            <Route path="/tin-tuc/:path" element={<NewsInfoPage />} />
            <Route path="/chinh-sach/:path" element={<TermsPage />} />
            <Route path="/lien-he/" element={<ContactPage />} />
            <Route path="/ve-chung-toi/" element={<AboutPage />} />
            <Route path="/dat-hang/" element={<OrderPage />} />
            <Route path="/quan-ly-hinh-anh/" element={<FileManagerPage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </AppWrapper>
      </SnackbarProvider>
    </BrowserRouter>
  );
}
