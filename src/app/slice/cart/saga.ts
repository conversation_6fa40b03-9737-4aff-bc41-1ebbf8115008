import { all, call, put, takeLatest } from 'redux-saga/effects';
import { cartActions } from '.';
import { utilsActions } from '../utils';
import { request } from 'utils/request';
import get from 'lodash/get';

function* fetchCarts(action: any) {
  const { token } = action.payload;

  try {
    const res = yield call(
      request,
      'get',
      `${process.env.REACT_APP_BACKEND_URL}/api/v1/carts`,
      {},
      token,
    );
    yield put(cartActions.setCartSuccess(res));
    // yield put(
    //   utilsActions.showSnackbar({
    //     message: `Saved`,
    //     variant: 'success',
    //   }),
    // );
  } catch (error: any) {
    // yield put(
    //   utilsActions.showSnackbar({
    //     message: get(error, 'message', 'Error Undefined'),
    //     variant: 'error',
    //   }),
    // );
    yield put(cartActions.setCartError(error.body));
  }
}

function* upsertCart(action: any) {
  const { token, products } = action.payload;
  // console.log('products upsert: ', products);
  try {
    const res = yield call(
      request,
      'put',
      `${process.env.REACT_APP_BACKEND_URL}/api/v1/carts/upsert`,
      products,
      token,
    );
    yield put(cartActions.setCartSuccess(res));
    // yield put(
    //   utilsActions.showSnackbar({
    //     message: `Saved`,
    //     variant: 'success',
    //   }),
    // );
  } catch (error: any) {
    // yield put(
    //   utilsActions.showSnackbar({
    //     message: get(error, 'message', 'Error Undefined'),
    //     variant: 'error',
    //   }),
    // );
    yield put(cartActions.setCartError(error.body));
  }
}

// Root saga
export default function* cartSaga() {
  // if necessary, start multiple sagas at once with `all`
  yield all([takeLatest(cartActions.fetchCarts.type, fetchCarts)]);
  yield all([takeLatest(cartActions.upsertCart.type, upsertCart)]);
}
