import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from 'utils/@reduxjs/toolkit'; // Importing from `utils` makes them more type-safe ✅
import { useInjectReducer, useInjectSaga } from 'utils/redux-injectors';
import { getOrderProducts, saveCarts } from '../utils/localStorage';
import { LOADING_STATE } from '../utils/types';
import { CartState } from './types';
import { ActionError } from 'types';
import cartSaga from './saga';

// The initial state of the user
export const initialState: CartState = {
  loading: LOADING_STATE.EMPTY,
  products: getOrderProducts(),
  error: '',
};

const slice = createSlice({
  name: 'cart' as never,
  initialState,
  reducers: {
    clearLoading(state) {
      state = {
        ...initialState,
        loading: LOADING_STATE.LOADED,
      };
    },
    upsertCart(state, action: PayloadAction<any>) {
      // Here we say lets change the username in my user state when changeUsername actions fires
      // Type-safe: It will expect `string` when firing the action. ✅
      state = {
        ...initialState,
        loading: LOADING_STATE.LOADING,
      };
    },
    fetchCarts(state, action: PayloadAction<any>) {
      // Here we say lets change the username in my user state when changeUsername actions fires
      // Type-safe: It will expect `string` when firing the action. ✅
      const products = action.payload;
      state.products = products;
      state.loading = LOADING_STATE.LOADED;
      saveCarts(products);
    },
    setCartSuccess(state, action: PayloadAction<any>) {
      // Here we say lets change the username in my user state when changeUsername actions fires
      // Type-safe: It will expect `string` when firing the action. ✅
      const products = action.payload;
      state.products = products;
      state.loading = LOADING_STATE.LOADED;
      saveCarts(products);
    },
    setCartError(state, action: PayloadAction<ActionError>) {
      state.loading = LOADING_STATE.LOADED_ERROR;
      state.error = action.payload.message;
    },
  },
});

/**
 * `actions` will be used to trigger change in the state from where ever you want
 */
export const { actions: cartActions } = slice;

export const useCartSlice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer as any });
  useInjectSaga({ key: slice.name, saga: cartSaga });
  return { actions: slice.actions };
};
