import { LOADING_STATE } from '../utils/types';

/* --- STATE --- */
export interface UserState {
  token: string | null;
  error: any;
  loading: LOADING_STATE;
  username: string | null;
  email: string | null;
  emailVerification: boolean | null;
  phone: string | null;
  phoneVerification: boolean | null;
  userId: string | null;
  role: string | null;
  // connectedAddresses: string[];
}

export interface Signup {
  email?: string;
  phone?: string;
  password: string;
}

export interface SignIn {
  username: string;
  password: string;
}
