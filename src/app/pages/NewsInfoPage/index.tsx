import Box from '@mui/material/Box';
import * as React from 'react';
import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { keyframes } from '@emotion/react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import QueueIcon from '@mui/icons-material/Queue';
import Grid from '@mui/material/Unstable_Grid2';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import DeleteIcon from '@mui/icons-material/Delete';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import MenuItem from '@mui/material/MenuItem';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import { Card, useMediaQuery } from '@mui/material';

import { useDispatch, useSelector } from 'react-redux';
import { useUserSlice } from 'app/slice/user';
import { userInfoSelector } from 'app/slice/user/selectors';
import { useUtilsSlice } from 'app/slice/utils';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';

import { formatTime } from 'utils/moment';
import JoditEditor from 'jodit-react';
import {
  editorConfigJodit,
  createMarkup,
  updateFiguresForImages,
} from 'utils/jodit';
import YouTube, { YouTubeProps } from 'react-youtube';

import s3Client from 'utils/s3';
import { request } from 'utils/request';
import { filterArrayByProperty } from 'utils/app';
import get from 'lodash/get';
import set from 'lodash/set';

import styled from 'styled-components';
import Recently from 'app/components/Recently/Recently';

import banner1 from 'assets/images/news/638463803756808331_group-2085661142.webp';
import { detailInit } from './detail';
import { InfoBox } from '@react-google-maps/api';
import { uploadFiles } from 'utils/uploadImg';

export function NewsInfoPage() {
  const [news, setNews] = React.useState<any>({});
  const [promotions, setPromotion] = React.useState<any[]>([]);
  const [tips, setTips] = React.useState<any[]>([]);
  const [videos, setVideos] = React.useState<any[]>([]);
  const [newsInfo, setNewsInfo] = React.useState<any>({});
  const [data, setData] = React.useState<any>('');
  const [name, setName] = React.useState<any>('');
  const [imageUrl, setImageUrl] = React.useState<any>('');
  const [label, setLabel] = React.useState<any>('tips');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [valueTab, setValueTab] = React.useState('1');
  const [openNew, setOpenNew] = React.useState<boolean>(false);
  const [isAddDetail, setIsAddDetail] = React.useState<boolean>(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const navigate = useNavigate();

  const params = useParams();
  const [searchParams] = useSearchParams();
  const { path } = params;
  const pathname = useLocation().pathname;

  const onClose = () => {
    setOpenNew(false);
  };

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: cartActions } = useCartSlice();
  const { actions: userActions } = useUserSlice();
  const cartInfo = useSelector(cartInfoSelector);
  const userInfo = useSelector(userInfoSelector);

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValueTab(newValue);
    // console.log('event: ', event);
    // console.log('valueTab: ', newValue);
  };

  const vietnameseToAscii = inputStr => {
    // Chuyển đổi từ tiếng Việt có dấu sang không dấu
    var convertedStr = inputStr
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Thay đổi dấu cách thành '-'
    convertedStr = convertedStr.replace(/\s+/g, '-');

    return convertedStr;
  };

  const handleEditNews = async () => {
    const finalData = updateFiguresForImages(data);
    const newsData = {
      detail: finalData,
      description: news?.description,
      mainKeyword: news?.mainKeyword,
      keyword: news?.keyword,
      name: news?.name,
      imageUrl,
      id: news?.id,
      canonical: news?.canonical,
    };
    const method = 'put';
    const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${news?._id}`;
    try {
      const result = await request(method, url, newsData);
      setIsAddDetail(false);
      fetchNewsPage();
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const handleDeleteNewsId = async id => {
    const method = 'delete';
    const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${id}`;
    try {
      const result = await request(method, url);
      navigate('/news');
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const handleCreateNews = async () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news`,
          { name, imageUrl, label },
        );
        if (result) {
          setOpenNew(false);
          setName('');
          setImageUrl('');
          setLabel('tips');
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    apiHandler();
  };

  const filterObjects = (arr, key, value) => {
    return arr.filter(item => item[key] === value);
  };

  const fetchNewsPage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${path}`,
          // { id: path },
        );

        // console.log({ result });
        if (result) {
          setNews(result);
          setData(result?.detail);
          setImageUrl(result.imageUrl);
          // console.log('news:  ', result)
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const handleScrollToBox = () => {
    const boxElement = document.getElementById('news-box');
    if (boxElement) {
      boxElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const onClickRef = index => {
    setNewsInfo(news[index]);
    setData(news[index]?.detail);
    handleScrollToBox();
  };

  const onPlayerReady: YouTubeProps['onReady'] = event => {
    // access to player in all event handlers via event.target
    event.target.pauseVideo();
  };
  const opts1: YouTubeProps['opts'] = {
    height: '150',
    width: '300',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 1,
    },
  };

  const opts2: YouTubeProps['opts'] = {
    height: '300',
    width: '500',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 1,
    },
  };
  const convertMongoCreatedAt = createdAt => {
    const now = new Date().getTime();
    const createdAtDate = new Date(createdAt).getTime();

    // Tính số mili giây chênh lệch giữa thời điểm hiện tại và thời điểm tạo
    const diffMilliseconds = now - createdAtDate;

    // Tính số phút chênh lệch
    const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));

    // Tính số giờ chênh lệch
    const diffHours = Math.floor(diffMilliseconds / (1000 * 60 * 60));

    // Tính số ngày chênh lệch
    const diffDays = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24));
    if (!createdAt) return '';
    if (diffHours < 1) {
      return `${diffMinutes} phút trước`;
    } else if (diffDays < 1) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 2) {
      return `${diffDays} ngày trước`;
    } else {
      return formatTime(createdAt); // Trả về thời gian tạo gốc nếu đã quá 2 ngày
    }
  };

  React.useEffect(() => {
    fetchNewsPage();
    // console.log('path: ', path, 'pathname:', pathname);
  }, [valueTab]);

  const AddDetailDialog = () => {
    React.useEffect(() => {}, []);
    return (
      <MuiDialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={isAddDetail}
        onClose={() => setIsAddDetail(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignContent: 'center',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                fontWeight: 'bold',
              }}
            >
              Chỉnh sửa nội dung tin tức
            </Typography>
            <Box>
              <IconButton color="warning" onClick={handleEditNews}>
                <SaveAsIcon />
              </IconButton>
              <IconButton
                color="warning"
                onClick={() => handleDeleteNewsId(news._id)}
              >
                <DeleteIcon />
              </IconButton>
              <IconButton color="warning" onClick={() => setIsAddDetail(false)}>
                <HighlightOffIcon />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              py: 5,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <TextField
              size="small"
              label="Tiêu đề"
              value={news?.name}
              onChange={e => {
                const setCloneNews = {
                  ...news,
                  name: e.target.value,
                  id: vietnameseToAscii(e.target.value),
                };
                setNews(setCloneNews);
              }}
            />
            <TextField
              size="small"
              label="Customer URL"
              value={news?.id}
              onChange={e => {
                const setCloneNews = {
                  ...news,
                  id: e.target.value,
                };
                setNews(setCloneNews);
              }}
            />
            <TextField
              size="small"
              label="Link canonical"
              value={news?.canonical}
              onChange={e => {
                const setCloneNews = {
                  ...news,
                  canonical: e.target.value,
                };
                setNews(setCloneNews);
              }}
            />
            <TextField
              size="small"
              label="mô tả"
              value={news?.description}
              onChange={e => {
                const setCloneNews = { ...news, description: e.target.value };
                setNews(setCloneNews);
              }}
            />
            <TextField
              size="small"
              label="main-keyword"
              value={news?.mainKeyword}
              onChange={e => {
                const setCloneNews = { ...news, mainKeyword: e.target.value };
                setNews(setCloneNews);
              }}
            />
            <TextField
              size="small"
              label="second-keyword"
              value={news?.keyword}
              onChange={e => {
                const setCloneNews = { ...news, keyword: e.target.value };
                setNews(setCloneNews);
              }}
            />
            <Box>
              <Box>
                <input
                  id="lead-banner"
                  name="file"
                  type="file"
                  onChange={event => {
                    const image = {
                      name: '',
                      file: undefined,
                      url: '',
                    };

                    image.file = get(event, 'currentTarget.files[0]');
                    if (image.file) {
                      const reader = new FileReader();
                      reader.readAsDataURL(image.file);
                      reader.onload = async () => {
                        set(image, 'url', reader.result);
                        set(image, 'name', get(image, 'file.name', ''));
                        // const uploadInfo = await s3Client.uploadFile(
                        //   `phuonglinh/productImage/${Date.now()}-${image.name}`,
                        //   image.file,
                        // );
                        // image.url = get(uploadInfo, 'Location');
                        image.url = await uploadFiles([image.file], 'news', '');
                        // const upload = UploadImg(image);
                        // console.log('upload: ', uploadInfo);
                        setImageUrl(image.url);
                      };
                    }
                    event.preventDefault();
                  }}
                  className="form-control"
                />
              </Box>
              <Box>
                <img
                  src={imageUrl}
                  alt="banner"
                  width={'100%'}
                  height={'100%'}
                />
              </Box>
            </Box>
            <JoditEditor
              value={data}
              config={editorConfigJodit}
              onChange={value => setData(value)}
            />
          </Box>
        </DialogContent>
      </MuiDialog>
    );
  };

  const TruncatedTypography = styled(Typography)`
    padding: 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  `;

  return (
    <>
      <Helmet>
        <title>{news?.name || 'Tin tức'}</title>
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100%',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: 'fit-content',
            // maxHeight: `${detailHeight}`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            borderRadius: '10px',
            overflow: 'hidden',
            position: 'relative',
            boxShadow:
              '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
          }}
        >
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: 1,
              px: 4,
              top: 0,
              fontSize: '12px',
              textTransform: 'capitalize',
              boxShadow:
                '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
            }}
          >
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/');
              }}
            >
              Trang chủ
            </Typography>
            <NavigateNextIcon fontSize="small" />
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/tin-tuc/tin-moi');
              }}
            >
              Tin tức
            </Typography>
            <NavigateNextIcon fontSize="small" />
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/');
              }}
            >
              {news?.label === 'tips'
                ? 'Thủ thuật'
                : news?.label === 'promotion'
                ? 'Khuyến mại'
                : ''}
            </Typography>
          </Box>
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              background: 'white',
              gap: 1,
              p: 1,
              px: 4,
              top: 0,
              zIndex: 100,
              fontSize: '12px',
              position: 'fixed',
              textTransform: 'capitalize',
              boxShadow:
                '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
            }}
          >
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/');
              }}
            >
              Trang chủ
            </Typography>
            <NavigateNextIcon fontSize="small" />
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/tin-tuc/tin-moi');
              }}
            >
              Tin tức
            </Typography>
            <NavigateNextIcon fontSize="small" />
            <Typography
              sx={{
                fontSize: '12px',
                cursor: 'pointer',
                '&:hover': {
                  color: 'gold',
                },
              }}
              onClick={() => {
                navigate('/');
              }}
            >
              {news?.label === 'tips'
                ? 'Thủ thuật'
                : news?.label === 'promotion'
                ? 'Khuyến mại'
                : ''}
            </Typography>
          </Box>
          <Typography
            variant="h4"
            sx={{
              width: '50%',
              [theme.breakpoints.down('sm')]: {
                width: '95%',
                height: '100%',
                fontSize: '20px',
                fontWeight: 'bold',
              },
            }}
          >
            {news?.name}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              width: '50%',
              color: 'gray',
              fontStyle: 'italic',
              textAlign: 'left',
              fontSize: '12px',
              p: 1,
              [theme.breakpoints.down('sm')]: {
                width: '95%',
                height: '100%',
              },
            }}
          >
            <Typography>admin</Typography>
            <Typography>{convertMongoCreatedAt(news?.createdAt)}</Typography>
          </Box>
          <Box
            id="news-box"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {
                width: '95%',
                height: '100%',
              },
              width: '50%',
              height: 'auto',
              overflowY: 'hidden',
            }}
            dangerouslySetInnerHTML={createMarkup(data)}
          ></Box>
          <Box
            sx={{
              display:
                userInfo?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                  ? 'flex'
                  : 'none',
              flexDirection: 'column',
              alignItems: 'center',
              position: '',
              width: '100%',
              bottom: 0,
              background: '',
            }}
          >
            <IconButton
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textTransform: 'none',
                background: 'white',
                color: 'gray',
                boxShadow:
                  '0px 0px 20px 2px rgba(0, 0, 0, 0.05), inset 0px 0px 2px 2px rgba(0, 0, 0, 0.05)',
              }}
              onClick={() => {
                setIsAddDetail(true);
              }}
            >
              <QueueIcon fontSize="small" />{' '}
            </IconButton>
          </Box>
        </Box>
        <Recently />
        {AddDetailDialog()}
      </Paper>
    </>
  );
}
