import {
  Button,
  Paper,
  CircularProgress,
  Box,
  Alert,
  Typography,
  Tabs,
  Tab,
  Container,
  AppBar,
  Too<PERSON><PERSON>,
  <PERSON>,
  Divider,
  Card,
  Fab,
  Tooltip,
  Avatar,
  Badge,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
  useTheme,
  B<PERSON>crum<PERSON>,
  Link,
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ImageIcon from '@mui/icons-material/Image';
import CategoryIcon from '@mui/icons-material/Category';
import PeopleIcon from '@mui/icons-material/People';
import CodeIcon from '@mui/icons-material/Code';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import HomeIcon from '@mui/icons-material/Home';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import { useEffect, lazy, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';
import { appSelector } from 'app/slice/utils/selectors';
import { DIALOG_TYPE } from 'app/slice/utils/types';
import { useAdminData } from './hooks/useAdminData';

// Lazy load components for better performance
const UserInfo = lazy(() => import('./components/UserInfo'));
const CategoryManagement = lazy(
  () => import('./components/CategoryManagement'),
);
const UserManagement = lazy(() => import('./components/UserManagement'));
const ScriptManagement = lazy(() => import('./components/ScriptManagement'));
const BannerManagement = lazy(() => import('./components/BannerManagement'));
const CollectionManagement = lazy(
  () => import('./components/CollectionManagement'),
);
const AboutUsManagement = lazy(() => import('./components/AboutUsManagement'));

export enum CollectionType {
  COLLECTION = 'COLLECTION',
  BRAND = 'BRAND',
  CATEGORY = 'CATEGORY',
  GROUP = 'GROUP',
  PROMOTION = 'PROMOTION',
  NEW = 'NEW',
  OUTSTANDING = 'OUTSTANDING',
}

export function AdminPage() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);
  const { dialogType } = useSelector(appSelector);

  // Use custom hook for data management
  const { data, actions } = useAdminData();

  // Local state for user info
  const [user, setUser] = React.useState<any>({
    email: '',
    emailVerification: false,
    error: '',
    loading: '',
    phone: '',
    phoneVerification: false,
    role: '',
    token: '',
    userId: '',
    username: '',
  });

  // Tab state for navigation
  const [currentTab, setCurrentTab] = React.useState(0);

  // Handle sign in when not authenticated
  const onSignIn = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.SIGN_IN }));
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Tab configuration
  const adminTabs = [
    { label: 'Tổng quan', icon: '📊' },
    { label: 'Banner & Layout', icon: '🎨' },
    { label: 'Danh mục', icon: '🏷️' },
    { label: 'Người dùng', icon: '👥' },
    { label: 'Scripts', icon: '📜' },
  ];

  // Render tab content
  const renderTabContent = () => {
    switch (currentTab) {
      case 0: // Tổng quan
        return (
          <Box sx={{ p: 4, bgcolor: '#f8fafc' }}>
            {/* Header Section */}
            <Box sx={{ mb: 4 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 1,
                  background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                📊 Dashboard Quản Trị
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Tổng quan hệ thống và thống kê nhanh
              </Typography>
            </Box>

            {/* Statistics Cards */}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr',
                  sm: 'repeat(2, 1fr)',
                  md: 'repeat(4, 1fr)',
                },
                gap: 3,
                mb: 4,
              }}
            >
              {/* Brands Card */}
              <Card
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '50%',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      borderRadius: 2,
                      fontSize: '2rem',
                    }}
                  >
                    🏢
                  </Box>
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {data.brandList.length}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Thương hiệu
                    </Typography>
                  </Box>
                </Box>
              </Card>

              {/* Categories Card */}
              <Card
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '50%',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      borderRadius: 2,
                      fontSize: '2rem',
                    }}
                  >
                    🏷️
                  </Box>
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {data.categoryList.length}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Danh mục
                    </Typography>
                  </Box>
                </Box>
              </Card>

              {/* Partners Card */}
              <Card
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '50%',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      borderRadius: 2,
                      fontSize: '2rem',
                    }}
                  >
                    🤝
                  </Box>
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {data.partnerList.length}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Đối tác
                    </Typography>
                  </Box>
                </Box>
              </Card>

              {/* Users Card */}
              <Card
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '50%',
                  },
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      borderRadius: 2,
                      fontSize: '2rem',
                    }}
                  >
                    👥
                  </Box>
                  <Box>
                    <Typography variant="h4" fontWeight="bold">
                      {data.listUser.length}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Người dùng
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Box>

            {/* Main Content Grid */}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr',
                  lg: '2fr 1fr',
                },
                gap: 4,
                mb: 4,
              }}
            >
              {/* User Info Section */}
              <Card
                sx={{
                  p: 4,
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  👤 Thông tin tài khoản
                </Typography>
                <UserInfo user={user} />
              </Card>

              {/* Quick Actions */}
              <Card
                sx={{
                  p: 4,
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  ⚡ Thao tác nhanh
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/quan-ly-hinh-anh')}
                    sx={{
                      py: 2,
                      background:
                        'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                      boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                      '&:hover': {
                        background:
                          'linear-gradient(45deg, #1976D2 30%, #1CB5E0 90%)',
                      },
                    }}
                    size="large"
                    startIcon={<span style={{ fontSize: '1.2rem' }}>🖼️</span>}
                  >
                    Quản lý hình ảnh
                  </Button>

                  <Button
                    variant="outlined"
                    onClick={() => actions.refetchData()}
                    sx={{
                      py: 2,
                      borderWidth: 2,
                      '&:hover': {
                        borderWidth: 2,
                        bgcolor: 'primary.light',
                        color: 'white',
                      },
                    }}
                    size="large"
                    startIcon={<span style={{ fontSize: '1.2rem' }}>🔄</span>}
                  >
                    Làm mới dữ liệu
                  </Button>
                </Box>
              </Card>
            </Box>

            {/* System Status & Recent Activity */}
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr',
                  md: 'repeat(2, 1fr)',
                },
                gap: 4,
              }}
            >
              {/* System Status */}
              <Card
                sx={{
                  p: 4,
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  🔧 Trạng thái hệ thống
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'success.light',
                      borderRadius: 1,
                      color: 'success.contrastText',
                    }}
                  >
                    <Typography variant="body2" fontWeight="500">
                      🟢 API Server
                    </Typography>
                    <Chip label="Online" color="success" size="small" />
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'success.light',
                      borderRadius: 1,
                      color: 'success.contrastText',
                    }}
                  >
                    <Typography variant="body2" fontWeight="500">
                      💾 Database
                    </Typography>
                    <Chip label="Connected" color="success" size="small" />
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'info.light',
                      borderRadius: 1,
                      color: 'info.contrastText',
                    }}
                  >
                    <Typography variant="body2" fontWeight="500">
                      📊 Data Sync
                    </Typography>
                    <Chip label="Synced" color="info" size="small" />
                  </Box>
                </Box>
              </Card>

              {/* Quick Stats */}
              <Card
                sx={{
                  p: 4,
                  borderRadius: 3,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  📈 Thống kê nhanh
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200',
                    }}
                  >
                    <Typography
                      variant="body2"
                      fontWeight="500"
                      color="text.secondary"
                    >
                      📦 Tổng sản phẩm
                    </Typography>
                    <Typography
                      variant="h6"
                      fontWeight="bold"
                      color="primary.main"
                    >
                      {data.newProductList?.length || 0}
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200',
                    }}
                  >
                    <Typography
                      variant="body2"
                      fontWeight="500"
                      color="text.secondary"
                    >
                      🎨 Banner hoạt động
                    </Typography>
                    <Typography
                      variant="h6"
                      fontWeight="bold"
                      color="secondary.main"
                    >
                      {data.bannerReward?.length || 0}
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200',
                    }}
                  >
                    <Typography
                      variant="body2"
                      fontWeight="500"
                      color="text.secondary"
                    >
                      🖼️ Hình ảnh đối tác
                    </Typography>
                    <Typography
                      variant="h6"
                      fontWeight="bold"
                      color="info.main"
                    >
                      {data.partnerImages?.length || 0}
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Box>
          </Box>
        );

      case 1: // Banner & Layout
        return (
          <Box sx={{ p: 4, bgcolor: '#f8fafc', position: 'relative' }}>
            {/* Sticky Save Button */}
            <Box
              sx={{
                position: 'sticky',
                top: 0,
                zIndex: 1000,
                bgcolor: 'rgba(248, 250, 252, 0.95)',
                backdropFilter: 'blur(10px)',
                borderRadius: 2,
                p: 2,
                mb: 3,
                border: '1px solid rgba(255,255,255,0.2)',
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: { xs: 'flex-start', md: 'center' },
                  flexDirection: { xs: 'column', md: 'row' },
                  gap: { xs: 2, md: 0 },
                }}
              >
                <Box>
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    color="primary.main"
                  >
                    🎨 Quản lý Banner & Layout
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Quản lý hình ảnh banner, bộ sưu tập và nội dung trang chủ
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="outlined"
                    size="medium"
                    onClick={() => actions.refetchData()}
                    disabled={data.isLoading}
                    sx={{
                      borderWidth: 2,
                      '&:hover': {
                        borderWidth: 2,
                      },
                    }}
                    startIcon={<span>🔄</span>}
                  >
                    Làm mới
                  </Button>

                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => actions.saveHomeSchema(data.homeSchema)}
                    disabled={data.isLoading}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      background:
                        'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                      boxShadow: '0 3px 5px 2px rgba(76, 175, 80, .3)',
                      '&:hover': {
                        background:
                          'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                      },
                      '&:disabled': {
                        background:
                          'linear-gradient(45deg, #BDBDBD 30%, #E0E0E0 90%)',
                      },
                    }}
                    startIcon={
                      data.isLoading ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : (
                        <span>💾</span>
                      )
                    }
                  >
                    {data.isLoading ? 'Đang lưu...' : 'Lưu tất cả'}
                  </Button>
                </Box>
              </Box>
            </Box>

            {/* Banner Management Section */}
            <Card
              sx={{
                mb: 4,
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                }}
              >
                <Typography variant="h6" fontWeight="bold">
                  🖼️ Quản lý Banner
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Upload và quản lý banner cho trang chủ và mobile
                </Typography>
              </Box>
              <Box sx={{ p: 3 }}>
                <BannerManagement
                  homeSchema={data.homeSchema}
                  onUpdateHomeSchema={actions.updateHomeSchema}
                  onSaveHomeSchema={actions.saveHomeSchema}
                />
              </Box>
            </Card>

            {/* Collection Management Section */}
            <Card
              sx={{
                mb: 4,
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  color: 'white',
                }}
              >
                <Typography variant="h6" fontWeight="bold">
                  📚 Quản lý Bộ sưu tập
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Cấu hình danh mục và bộ sưu tập sản phẩm
                </Typography>
              </Box>
              <Box sx={{ p: 3 }}>
                <CollectionManagement
                  homeSchema={data.homeSchema}
                  onUpdateHomeSchema={actions.updateHomeSchema}
                />
              </Box>
            </Card>

            {/* About Us Management Section */}
            <Card
              sx={{
                mb: 4,
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  p: 3,
                  background:
                    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  color: 'white',
                }}
              >
                <Typography variant="h6" fontWeight="bold">
                  ℹ️ Quản lý Về chúng tôi
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Cập nhật thông tin về công ty và banner giữa trang
                </Typography>
              </Box>
              <Box sx={{ p: 3 }}>
                <AboutUsManagement
                  homeSchema={data.homeSchema}
                  onUpdateHomeSchema={actions.updateHomeSchema}
                />
              </Box>
            </Card>

            {/* Floating Action Button */}
            <Tooltip title="Lưu tất cả thay đổi" placement="left">
              <Fab
                color="primary"
                sx={{
                  position: 'fixed',
                  bottom: 24,
                  right: 24,
                  background:
                    'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                  boxShadow: '0 6px 20px rgba(76, 175, 80, .4)',
                  '&:hover': {
                    background:
                      'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                    transform: 'scale(1.1)',
                  },
                  transition: 'all 0.3s ease',
                  zIndex: 1000,
                }}
                onClick={() => actions.saveHomeSchema(data.homeSchema)}
                disabled={data.isLoading}
              >
                {data.isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  <SaveIcon />
                )}
              </Fab>
            </Tooltip>
          </Box>
        );

      case 2: // Danh mục
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              🏷️ Quản lý Danh mục
            </Typography>
            <CategoryManagement
              brandList={data.brandList}
              categoryList={data.categoryList}
              partnerList={data.partnerList}
              onRemoveBrand={actions.removeBrand}
              onRemoveCategory={actions.removeCategory}
              onRemovePartner={actions.removePartner}
              onAddBrand={actions.addBrand}
              onAddCategory={actions.addCategory}
              onAddPartner={actions.addPartner}
              onUpdatePartnerImage={actions.updatePartnerImage}
            />
          </Box>
        );

      case 3: // Người dùng
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              👥 Quản lý Người dùng
            </Typography>
            <UserManagement
              listUser={data.listUser}
              onDeleteUser={actions.deleteUser}
              onUpdateUserRole={actions.updateUserRole}
            />
          </Box>
        );

      case 4: // Scripts
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              📜 Quản lý Scripts
            </Typography>
            <ScriptManagement />
          </Box>
        );

      default:
        return null;
    }
  };

  // Update user info when userInfo changes
  useEffect(() => {
    if (!userInfo?.token) {
      onSignIn();
      return;
    }
    setUser(userInfo);
  }, [userInfo, dialogType]);

  // Loading component for Suspense
  const LoadingComponent = () => (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 4,
      }}
    >
      <CircularProgress />
    </Box>
  );

  return (
    <>
      <Helmet>
        <title>AdminPage</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      {!userInfo?.token ? (
        <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            🔐 Cần đăng nhập
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Vui lòng đăng nhập để truy cập trang quản trị
          </Typography>
          <Button variant="contained" onClick={onSignIn} size="large">
            Đăng nhập
          </Button>
        </Container>
      ) : (
        <Box sx={{ width: '100%', minHeight: '100vh', bgcolor: '#f5f5f5' }}>
          {/* Header */}
          <AppBar
            position="static"
            elevation={0}
            sx={{ bgcolor: 'white', color: 'text.primary' }}
          >
            <Toolbar>
              <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
                🛠️ Trang Quản Trị - Phương Linh
              </Typography>
              <Chip
                label={`👋 ${user?.email || 'Admin'}`}
                color="primary"
                variant="outlined"
              />
            </Toolbar>
          </AppBar>

          {/* Global Loading */}
          {data.isLoading && (
            <Box
              sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={40} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Đang cập nhật dữ liệu...
                </Typography>
              </Box>
            </Box>
          )}

          {/* Global Error */}
          {data.error && (
            <Container maxWidth="lg" sx={{ pt: 2 }}>
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="body2">{data.error}</Typography>
              </Alert>
            </Container>
          )}

          {/* Admin Only Content */}
          {user?.role === 'ADMIN' ? (
            <Container maxWidth="lg" sx={{ py: 0 }}>
              {/* Navigation Tabs */}
              <Paper elevation={1} sx={{ mb: 0 }}>
                <Tabs
                  value={currentTab}
                  onChange={handleTabChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    borderBottom: 1,
                    borderColor: 'divider',
                    '& .MuiTab-root': {
                      minHeight: 64,
                      fontSize: '1rem',
                      fontWeight: 500,
                    },
                  }}
                >
                  {adminTabs.map((tab, index) => (
                    <Tab
                      key={index}
                      label={
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <span>{tab.icon}</span>
                          <span>{tab.label}</span>
                        </Box>
                      }
                    />
                  ))}
                </Tabs>
              </Paper>

              {/* Tab Content */}
              <Paper elevation={1} sx={{ minHeight: '70vh' }}>
                <Suspense
                  fallback={
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      <CircularProgress />
                      <Typography variant="body2" sx={{ mt: 2 }}>
                        Đang tải...
                      </Typography>
                    </Box>
                  }
                >
                  {renderTabContent()}
                </Suspense>
              </Paper>
            </Container>
          ) : (
            <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
              <Typography variant="h4" gutterBottom>
                ⚠️ Không có quyền truy cập
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Bạn cần quyền Admin để truy cập trang này
              </Typography>
            </Container>
          )}
        </Box>
      )}
    </>
  );
}
