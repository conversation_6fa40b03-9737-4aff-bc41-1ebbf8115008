import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { request } from 'utils/request';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';
import get from 'lodash/get';

export interface AdminData {
  homeSchema: any;
  brandList: any[];
  categoryList: any[];
  partnerList: any[];
  listUser: any[];
  bannerReward: any[];
  partnerImages: any[];
  newProductList: any[];
  collectionList: any[];
  collectionImageList: any[];
  collections: any[];
  middleBanner: any;
  us: any;
  isLoading: boolean;
  error: string | null;
}

export const useAdminData = () => {
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);

  const [data, setData] = useState<AdminData>({
    homeSchema: {},
    brandList: [],
    categoryList: [],
    partnerList: [],
    listUser: [],
    bannerReward: [],
    partnerImages: [],
    newProductList: [],
    collectionList: [],
    collectionImageList: [],
    collections: [],
    middleBanner: {
      imageUrl: '',
      title: 'Home Appliances',
      subTitle: 'Decorate and heal your life.',
    },
    us: {
      aboutUs: {
        content: '',
        imageUrl: '',
      },
      whyUs: [],
    },
    isLoading: false,
    error: null,
  });

  // Trigger để refetch data
  const [refetchTrigger, setRefetchTrigger] = useState(0);

  const showError = useCallback(
    (error: any) => {
      dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    },
    [dispatch, utilsAction],
  );

  const showSuccess = useCallback(
    (message: string) => {
      dispatch(
        utilsAction.showSnackbar({
          message,
          variant: 'success',
        }),
      );
    },
    [dispatch, utilsAction],
  );

  const fetchHomePage = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, isLoading: true, error: null }));

      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
        { type: 'HOME' },
      );

      setData(prev => ({
        ...prev,
        homeSchema: result,
        bannerReward: result?.banner || [],
        isLoading: false,
      }));

      // Process homeSchema data
      if (result) {
        const {
          banner,
          us,
          middleBanner,
          categoryList,
          newProducts,
          collectionList,
          slidePartner,
        } = result;

        const updates: Partial<AdminData> = {};

        if (banner && banner.length > 0) {
          updates.bannerReward = banner[0];
        }

        if (slidePartner && slidePartner.length > 0) {
          const newImages = slidePartner.map((item: any) => ({
            url: item.imageUrl,
          }));
          updates.partnerImages = newImages;
        }

        if (us) {
          updates.us = us;
        }

        if (middleBanner) {
          updates.middleBanner = middleBanner;
        }

        if (newProducts && newProducts.length) {
          updates.newProductList = newProducts;
        }

        if (collectionList && collectionList.length) {
          updates.collectionList = [
            collectionList.filter((item: any) => !get(item, 'imageUrl'))[0],
          ];
          updates.collectionImageList = collectionList.filter((item: any) =>
            get(item, 'imageUrl'),
          );
        }

        setData(prev => ({ ...prev, ...updates }));
      }
    } catch (error) {
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: get(error, 'message', 'Lỗi khi tải dữ liệu trang chủ'),
      }));
      showError(error);
    }
  }, [showError]);

  const fetchBrands = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, error: null }));
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands`,
        {},
      );
      setData(prev => ({ ...prev, brandList: result }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        error: get(error, 'message', 'Lỗi khi tải danh sách thương hiệu'),
      }));
      showError(error);
    }
  }, [showError]);

  const fetchCategories = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, error: null }));
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v2/category`,
        {},
      );
      setData(prev => ({ ...prev, categoryList: result }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        error: get(error, 'message', 'Lỗi khi tải danh sách danh mục'),
      }));
      showError(error);
    }
  }, [showError]);

  const fetchPartners = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, error: null }));
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners`,
        {},
      );
      setData(prev => ({ ...prev, partnerList: result }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        error: get(error, 'message', 'Lỗi khi tải danh sách đối tác'),
      }));
      showError(error);
    }
  }, [showError]);

  const fetchUsers = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, error: null }));
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/users`,
        {},
        userInfo?.token as any,
      );
      setData(prev => ({ ...prev, listUser: result }));
    } catch (error) {
      setData(prev => ({
        ...prev,
        error: get(error, 'message', 'Lỗi khi tải danh sách người dùng'),
      }));
      showError(error);
    }
  }, [userInfo?.token, showError]);

  const refetchData = useCallback(() => {
    setRefetchTrigger(prev => prev + 1);
  }, []);

  const removeBrand = useCallback(
    async (id: number) => {
      try {
        await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands/${id}`,
          {},
        );
        refetchData();
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError],
  );

  const removeCategory = useCallback(
    async (id: number) => {
      try {
        await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/category/${id}`,
          {},
        );
        refetchData();
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError],
  );

  const removePartner = useCallback(
    async (id: number) => {
      try {
        await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners/${id}`,
          {},
        );
        refetchData();
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError],
  );

  const addBrand = useCallback(
    async (name: string) => {
      try {
        await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands/`,
          { name },
        );
        refetchData();
        showSuccess('Thêm thương hiệu thành công');
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError, showSuccess],
  );

  const addCategory = useCallback(
    async (name: string) => {
      try {
        await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/category/`,
          { name },
        );
        refetchData();
        showSuccess('Thêm danh mục thành công');
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError, showSuccess],
  );

  const addPartner = useCallback(
    async (name: string) => {
      try {
        await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners`,
          { name },
        );
        refetchData();
        showSuccess('Thêm đối tác thành công');
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError, showSuccess],
  );

  const updatePartnerImage = useCallback(
    async (partnerId: string, imageUrl: string) => {
      try {
        await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners/${partnerId}`,
          {
            images: [{ url: imageUrl }],
          },
        );
        refetchData();
        showSuccess('Cập nhật ảnh đối tác thành công');
      } catch (error) {
        showError(error);
      }
    },
    [refetchData, showError, showSuccess],
  );

  const deleteUser = useCallback(
    async (userId: string) => {
      try {
        if (!window.confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
          return;
        }
        await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/users/${userId}`,
          {},
          userInfo?.token as any,
        );
        refetchData();
        showSuccess('Xóa người dùng thành công');
      } catch (error) {
        showError(error);
      }
    },
    [userInfo?.token, refetchData, showError, showSuccess],
  );

  const updateUserRole = useCallback(
    async (userId: string, currentRole: string) => {
      try {
        const newRole = currentRole === 'COLLAB' ? 'CUSTOMER' : 'COLLAB';
        await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/users/${userId}`,
          { role: newRole },
          userInfo?.token as any,
        );
        refetchData();
        showSuccess('Cập nhật vai trò thành công');
      } catch (error) {
        showError(error);
      }
    },
    [userInfo?.token, refetchData, showError, showSuccess],
  );

  // Initial data fetch - chỉ chạy một lần khi component mount
  useEffect(() => {
    fetchHomePage();
    fetchBrands();
    fetchCategories();
    fetchPartners();
  }, []); // Empty dependency array để chỉ chạy một lần

  // Fetch users khi có token
  useEffect(() => {
    if (userInfo?.token) {
      fetchUsers();
    }
  }, [userInfo?.token, fetchUsers]);

  // Refetch data khi có trigger
  useEffect(() => {
    if (refetchTrigger > 0) {
      const refetchAll = async () => {
        setData(prev => ({ ...prev, isLoading: true, error: null }));
        try {
          await Promise.all([
            fetchHomePage(),
            fetchBrands(),
            fetchCategories(),
            fetchPartners(),
            userInfo?.token ? fetchUsers() : Promise.resolve(),
          ]);
        } finally {
          setData(prev => ({ ...prev, isLoading: false }));
        }
      };
      refetchAll();
    }
  }, [
    refetchTrigger,
    userInfo?.token,
    fetchHomePage,
    fetchBrands,
    fetchCategories,
    fetchPartners,
    fetchUsers,
  ]);

  const updateHomeSchema = useCallback((newHomeSchema: any) => {
    setData(prev => ({ ...prev, homeSchema: newHomeSchema }));
  }, []);

  const saveHomeSchema = useCallback(
    async (homeSchemaData: any) => {
      try {
        const result: any = await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/upsert`,
          homeSchemaData,
        );

        setData(prev => ({ ...prev, homeSchema: result }));
        showSuccess('Cập nhật dữ liệu thành công');
        return result;
      } catch (error) {
        showError(error);
        throw error;
      }
    },
    [showError, showSuccess],
  );

  return {
    data,
    actions: {
      removeBrand,
      removeCategory,
      removePartner,
      addBrand,
      addCategory,
      addPartner,
      updatePartnerImage,
      deleteUser,
      updateUserRole,
      refetchData,
      updateHomeSchema,
      saveHomeSchema,
    },
  };
};
