import React, { useState, useEffect } from 'react';
import { Box, Button, TextField, Typography } from '@mui/material';
import s3Client from 'utils/s3';

const JS_HEADER_PATH_FILE = 'phuonglinh/jsfile/header.js';
const JS_BODY_PATH_FILE = 'phuonglinh/jsfile/body.js';
const JS_FOOTER_PATH_FILE = 'phuonglinh/jsfile/footer.js';

const ScriptManagement: React.FC = () => {
  const [jsHeaderContent, setJsHeaderContent] = useState('');
  const [jsBodyContent, setJsBodyContent] = useState('');
  const [jsFooterContent, setJsFooterContent] = useState('');

  useEffect(() => {
    const loadScripts = async () => {
      try {
        // Load header script
        const headerFile = await s3Client.getFile(JS_HEADER_PATH_FILE);
        if (headerFile && headerFile.Body) {
          setJsHeaderContent(headerFile.Body.toString());
        }
      } catch (error) {
        console.log('Header script not found');
      }

      try {
        // Load body script
        const bodyFile = await s3Client.getFile(JS_BODY_PATH_FILE);
        if (bodyFile && bodyFile.Body) {
          setJsBodyContent(bodyFile.Body.toString());
        }
      } catch (error) {
        console.log('Body script not found');
      }

      try {
        // Load footer script
        const footerFile = await s3Client.getFile(JS_FOOTER_PATH_FILE);
        if (footerFile && footerFile.Body) {
          setJsFooterContent(footerFile.Body.toString());
        }
      } catch (error) {
        console.log('Footer script not found');
      }
    };

    loadScripts();
  }, []);

  const handleUploadJsFile = async (path: string, content: string) => {
    try {
      // Check if file exists and delete it
      const file = await s3Client.getHeadObject(path);
      if (file) {
        await s3Client.deleteFile(path);
      }
    } catch (error) {
      // File doesn't exist, continue
    }

    try {
      // Upload new content
      await s3Client.uploadFile(path, content);
      console.log(`Script uploaded successfully: ${path}`);
    } catch (error) {
      console.error(`Error uploading script: ${path}`, error);
    }
  };

  return (
    <>
      {/* Header Script */}
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          p: 2,
          flexDirection: 'column',
          borderRadius: 1,
          boxShadow:
            '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)',
          width: '100%',
          height: '100%',
        }}
      >
        <Typography
          variant="h5"
          sx={{
            py: 2,
            textTransform: 'uppercase',
          }}
        >
          Header Script
        </Typography>
        <TextField
          label="Header Script"
          multiline
          rows={7}
          value={jsHeaderContent}
          onChange={event => {
            setJsHeaderContent(event.target.value);
          }}
        />
        <Button
          variant="contained"
          onClick={() => {
            handleUploadJsFile(JS_HEADER_PATH_FILE, jsHeaderContent);
          }}
          sx={{ ml: 'auto' }}
        >
          Apply
        </Button>
      </Box>

      {/* Body Script */}
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          p: 2,
          flexDirection: 'column',
          borderRadius: 1,
          boxShadow:
            '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)',
          width: '100%',
          height: '100%',
        }}
      >
        <Typography
          variant="h5"
          sx={{
            py: 2,
            textTransform: 'uppercase',
          }}
        >
          Body Script
        </Typography>
        <TextField
          label="Body Script"
          multiline
          rows={7}
          value={jsBodyContent}
          onChange={event => {
            setJsBodyContent(event.target.value);
          }}
        />
        <Button
          variant="contained"
          onClick={() => {
            handleUploadJsFile(JS_BODY_PATH_FILE, jsBodyContent);
          }}
          sx={{ ml: 'auto' }}
        >
          Apply
        </Button>
      </Box>

      {/* Footer Script */}
      <Box
        sx={{
          display: 'flex',
          gap: 2,
          p: 2,
          flexDirection: 'column',
          borderRadius: 1,
          boxShadow:
            '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)',
          width: '100%',
          height: '100%',
        }}
      >
        <Typography
          variant="h5"
          sx={{
            py: 2,
            textTransform: 'uppercase',
          }}
        >
          Footer Script
        </Typography>
        <TextField
          label="Footer Script"
          multiline
          rows={7}
          value={jsFooterContent}
          onChange={event => {
            setJsFooterContent(event.target.value);
          }}
        />
        <Button
          variant="contained"
          onClick={() => {
            handleUploadJsFile(JS_FOOTER_PATH_FILE, jsFooterContent);
          }}
          sx={{ ml: 'auto' }}
        >
          Apply
        </Button>
      </Box>
    </>
  );
};

export default ScriptManagement;
