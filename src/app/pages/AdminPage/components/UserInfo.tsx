import React from 'react';
import { <PERSON>, Button, Typography, Avatar, Chip, Divider } from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { useDispatch } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { useUserSlice } from 'app/slice/user';
import { DIALOG_TYPE } from 'app/slice/utils/types';

interface UserInfoProps {
  user: any;
}

const UserInfo: React.FC<UserInfoProps> = ({ user }) => {
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: userActions } = useUserSlice();

  const handleCreateUser = () => {
    dispatch(utilsAction.setDialogApp({ dialogType: DIALOG_TYPE.SIGN_UP }));
  };

  const handleLogout = () => {
    dispatch(userActions.signOut({}));
  };

  const getRoleInfo = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return { text: 'Admin', color: 'error' as const, icon: '👑' };
      case 'COLLAB':
        return { text: 'CTV', color: 'warning' as const, icon: '🤝' };
      case 'CUSTOMER':
        return { text: 'Khách hàng', color: 'info' as const, icon: '👤' };
      default:
        return {
          text: 'Không xác định',
          color: 'default' as const,
          icon: '❓',
        };
    }
  };

  const roleInfo = getRoleInfo(user?.role);

  return (
    <Box sx={{ width: '100%' }}>
      {/* User Profile Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 3,
          mb: 3,
          p: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 2,
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -30,
            right: -30,
            width: 80,
            height: 80,
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '50%',
          },
        }}
      >
        <Avatar
          sx={{
            width: 80,
            height: 80,
            bgcolor: 'rgba(255,255,255,0.2)',
            fontSize: '2rem',
            border: '3px solid rgba(255,255,255,0.3)',
          }}
        >
          {user?.username?.charAt(0)?.toUpperCase() || '👤'}
        </Avatar>

        <Box sx={{ flex: 1 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {user?.username?.split('@')[0] || 'Admin User'}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Typography variant="body1">{roleInfo.icon}</Typography>
            <Typography variant="body1" fontWeight="500">
              {roleInfo.text}
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            {user?.email || user?.username}
          </Typography>
        </Box>
      </Box>

      {/* User Details */}
      <Box sx={{ mb: 3 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          color="text.primary"
        >
          📋 Thông tin chi tiết
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* User ID */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              bgcolor: 'grey.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200',
            }}
          >
            <Typography variant="body2" fontWeight="500" color="text.secondary">
              🆔 User ID
            </Typography>
            <Typography variant="body2" fontWeight="500">
              {user?.userId || 'N/A'}
            </Typography>
          </Box>

          {/* Phone */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              bgcolor: 'grey.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200',
            }}
          >
            <Typography variant="body2" fontWeight="500" color="text.secondary">
              📱 Số điện thoại
            </Typography>
            <Typography variant="body2" fontWeight="500">
              {user?.phone || 'Chưa cập nhật'}
            </Typography>
          </Box>

          {/* Email Verification */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              bgcolor: 'grey.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200',
            }}
          >
            <Typography variant="body2" fontWeight="500" color="text.secondary">
              ✉️ Email xác thực
            </Typography>
            <Chip
              label={user?.emailVerification ? 'Đã xác thực' : 'Chưa xác thực'}
              color={user?.emailVerification ? 'success' : 'warning'}
              size="small"
            />
          </Box>

          {/* Phone Verification */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              bgcolor: 'grey.50',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200',
            }}
          >
            <Typography variant="body2" fontWeight="500" color="text.secondary">
              📞 SĐT xác thực
            </Typography>
            <Chip
              label={user?.phoneVerification ? 'Đã xác thực' : 'Chưa xác thực'}
              color={user?.phoneVerification ? 'success' : 'warning'}
              size="small"
            />
          </Box>
        </Box>
      </Box>

      <Divider sx={{ my: 3 }} />

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          color="text.primary"
        >
          ⚙️ Thao tác
        </Typography>

        <Button
          variant="contained"
          onClick={handleCreateUser}
          startIcon={<PersonAddIcon />}
          sx={{
            py: 1.5,
            background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
            boxShadow: '0 3px 5px 2px rgba(76, 175, 80, .3)',
            '&:hover': {
              background: 'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
            },
          }}
        >
          Tạo tài khoản mới
        </Button>

        <Button
          variant="outlined"
          onClick={handleLogout}
          startIcon={<LogoutIcon />}
          color="error"
          sx={{
            py: 1.5,
            borderWidth: 2,
            '&:hover': {
              borderWidth: 2,
              bgcolor: 'error.light',
              color: 'white',
            },
          }}
        >
          Đăng xuất
        </Button>
      </Box>
    </Box>
  );
};

export default UserInfo;
