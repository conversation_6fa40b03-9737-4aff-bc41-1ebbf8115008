import React, { useState } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  IconButton,
  Chip,
  Avatar,
  Divider,
  Collapse,
  Tooltip,
  Alert,
  Grid,
  Paper,
  Badge,
  LinearProgress,
  Fab,
  Zoom,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ImageIcon from '@mui/icons-material/Image';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import LinkIcon from '@mui/icons-material/Link';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import { uploadFiles } from 'utils/uploadImg';
import get from 'lodash/get';
import set from 'lodash/set';

interface BannerManagementProps {
  homeSchema: any;
  onUpdateHomeSchema: (newHomeSchema: any) => void;
  onSaveHomeSchema: (homeSchemaData: any) => Promise<any>;
}

const BannerManagement: React.FC<BannerManagementProps> = ({
  homeSchema,
  onUpdateHomeSchema,
  onSaveHomeSchema,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['slideBrand']),
  );
  const [uploadingItems, setUploadingItems] = useState<Set<string>>(new Set());
  const [previewMode, setPreviewMode] = useState<boolean>(false);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };
  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    bannerType: string,
    index: number,
  ) => {
    const uploadKey = `${bannerType}-${index}`;
    setUploadingItems(prev => new Set(prev).add(uploadKey));

    const image = {
      name: '',
      file: undefined as File | undefined,
      url: '',
    };

    image.file = get(event, 'currentTarget.files[0]');
    if (image.file) {
      try {
        const reader = new FileReader();
        reader.readAsDataURL(image.file);
        reader.onload = async () => {
          set(image, 'url', reader.result);
          set(image, 'name', get(image, 'file.name', ''));

          image.url = await uploadFiles([image.file!], 'banner', '');

          const newBanners = [...(homeSchema[bannerType] || [])];
          newBanners[index] = {
            ...newBanners[index],
            imageUrl: image.url,
          };

          onUpdateHomeSchema({
            ...homeSchema,
            [bannerType]: newBanners,
          });

          setUploadingItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(uploadKey);
            return newSet;
          });
        };
      } catch (error) {
        console.error('Upload failed:', error);
        setUploadingItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(uploadKey);
          return newSet;
        });
      }
    }
    event.preventDefault();
  };

  const handleBannerFieldChange = (
    bannerType: string,
    index: number,
    field: string,
    value: string,
  ) => {
    const newBanners = [...(homeSchema[bannerType] || [])];
    newBanners[index] = {
      ...newBanners[index],
      [field]: value,
    };

    onUpdateHomeSchema({
      ...homeSchema,
      [bannerType]: newBanners,
    });
  };

  const handleAddBanner = (bannerType: string) => {
    const newBanners = [...(homeSchema[bannerType] || [])];
    newBanners.push({
      imageUrl: '',
      href: '',
      content: '',
    });

    onUpdateHomeSchema({
      ...homeSchema,
      [bannerType]: newBanners,
    });
  };

  const handleRemoveBanner = (bannerType: string, index: number) => {
    const newBanners = [...(homeSchema[bannerType] || [])];
    newBanners.splice(index, 1);

    onUpdateHomeSchema({
      ...homeSchema,
      [bannerType]: newBanners,
    });
  };

  const getBannerTypeIcon = (bannerType: string) => {
    switch (bannerType) {
      case 'slideBrand':
        return '🏷️';
      case 'banner':
        return '🖼️';
      case 'bannerMobile':
        return '📱';
      default:
        return '📷';
    }
  };

  const getBannerTypeColor = (bannerType: string) => {
    switch (bannerType) {
      case 'slideBrand':
        return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
      case 'banner':
        return 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
      case 'bannerMobile':
        return 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
      default:
        return 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)';
    }
  };

  const renderBannerSection = (bannerType: string, title: string) => {
    const isExpanded = expandedSections.has(bannerType);
    const bannerCount = homeSchema[bannerType]?.length || 0;

    return (
      <Card
        elevation={3}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          mb: 3,
          border: '1px solid rgba(255,255,255,0.2)',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
            transform: 'translateY(-2px)',
          },
        }}
      >
        {/* Section Header */}
        <Box
          sx={{
            p: 3,
            background: getBannerTypeColor(bannerType),
            color: 'white',
            cursor: 'pointer',
          }}
          onClick={() => toggleSection(bannerType)}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  width: 48,
                  height: 48,
                  bgcolor: 'rgba(255,255,255,0.2)',
                  fontSize: '1.5rem',
                }}
              >
                {getBannerTypeIcon(bannerType)}
              </Avatar>

              <Box>
                <Typography variant="h6" fontWeight="bold">
                  {title}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {bannerCount === 0
                    ? 'Chưa có banner nào'
                    : `${bannerCount} banner`}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip
                label={bannerCount}
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              />

              <Tooltip title={isExpanded ? 'Thu gọn' : 'Mở rộng'}>
                <IconButton size="small" sx={{ color: 'white' }}>
                  {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Box>

        {/* Section Content */}
        <Collapse in={isExpanded} timeout="auto">
          <Box sx={{ p: 3 }}>
            {bannerCount === 0 && (
              <Alert severity="info" sx={{ borderRadius: 2, mb: 3 }}>
                <Typography>
                  Chưa có banner nào. Nhấn nút "Thêm banner" để bắt đầu!
                </Typography>
              </Alert>
            )}

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {homeSchema[bannerType]?.map((item: any, index: number) => {
                const uploadKey = `${bannerType}-${index}`;
                const isUploading = uploadingItems.has(uploadKey);

                return (
                  <Paper
                    key={index}
                    elevation={2}
                    sx={{
                      p: 3,
                      borderRadius: 3,
                      border: '1px solid #e0e0e0',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                        transform: 'translateY(-1px)',
                      },
                    }}
                  >
                    <Grid container spacing={3}>
                      {/* Image Section */}
                      <Grid item xs={12} md={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography
                            variant="subtitle2"
                            gutterBottom
                            color="text.secondary"
                          >
                            Hình ảnh banner
                          </Typography>

                          <Box
                            sx={{
                              width: '100%',
                              height: 160,
                              borderRadius: 3,
                              overflow: 'hidden',
                              position: 'relative',
                              bgcolor: '#f8f9fa',
                              border: '2px dashed #dee2e6',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              mb: 2,
                            }}
                          >
                            {isUploading && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  bgcolor: 'rgba(255,255,255,0.9)',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexDirection: 'column',
                                  gap: 2,
                                  zIndex: 10,
                                }}
                              >
                                <CloudUploadIcon
                                  sx={{
                                    fontSize: '2rem',
                                    color: 'primary.main',
                                  }}
                                />
                                <Typography
                                  variant="body2"
                                  color="primary.main"
                                >
                                  Đang tải lên...
                                </Typography>
                                <LinearProgress sx={{ width: '80%' }} />
                              </Box>
                            )}

                            {item.imageUrl ? (
                              <>
                                <img
                                  src={item.imageUrl}
                                  alt={`Banner ${index + 1}`}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'contain',
                                    borderRadius: '12px',
                                    backgroundColor: '#ffffff',
                                    padding: '8px',
                                  }}
                                  onError={e => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    const errorDiv =
                                      target.parentElement?.querySelector(
                                        '.error-placeholder',
                                      ) as HTMLElement;
                                    if (errorDiv)
                                      errorDiv.style.display = 'flex';
                                  }}
                                />
                                <Box
                                  className="error-placeholder"
                                  sx={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    display: 'none',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'column',
                                    bgcolor: 'error.light',
                                    color: 'error.contrastText',
                                    borderRadius: 3,
                                  }}
                                >
                                  <ImageIcon sx={{ fontSize: '3rem', mb: 1 }} />
                                  <Typography
                                    variant="body2"
                                    textAlign="center"
                                  >
                                    Không thể tải hình ảnh
                                  </Typography>
                                </Box>
                              </>
                            ) : (
                              <Box
                                sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  height: '100%',
                                  color: 'text.secondary',
                                }}
                              >
                                <PhotoCameraIcon
                                  sx={{ fontSize: '3rem', mb: 1 }}
                                />
                                <Typography variant="body2" textAlign="center">
                                  Chưa có hình ảnh
                                </Typography>
                              </Box>
                            )}
                          </Box>

                          <Button
                            variant="contained"
                            component="label"
                            size="medium"
                            disabled={isUploading}
                            sx={{
                              background:
                                'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                              boxShadow: '0 3px 5px 2px rgba(33, 150, 243, .3)',
                              '&:hover': {
                                background:
                                  'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                              },
                              '&:disabled': {
                                background:
                                  'linear-gradient(45deg, #BDBDBD 30%, #E0E0E0 90%)',
                              },
                            }}
                            startIcon={
                              isUploading ? (
                                <CloudUploadIcon />
                              ) : (
                                <PhotoCameraIcon />
                              )
                            }
                          >
                            {isUploading ? 'Đang tải...' : 'Chọn hình ảnh'}
                            <input
                              type="file"
                              hidden
                              accept="image/*"
                              onChange={event =>
                                handleImageUpload(event, bannerType, index)
                              }
                            />
                          </Button>

                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{
                              display: 'block',
                              textAlign: 'center',
                              fontSize: '0.75rem',
                              mt: 1,
                            }}
                          >
                            💡 Hình ảnh sẽ giữ nguyên tỷ lệ
                          </Typography>
                        </Box>
                      </Grid>

                      {/* Form Fields */}
                      <Grid item xs={12} md={8}>
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 3,
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                            }}
                          >
                            <Typography variant="h6" fontWeight="bold">
                              Banner #{index + 1}
                            </Typography>

                            <Tooltip title="Xóa banner">
                              <IconButton
                                onClick={() =>
                                  handleRemoveBanner(bannerType, index)
                                }
                                color="error"
                                sx={{
                                  '&:hover': {
                                    bgcolor: 'error.light',
                                    color: 'white',
                                  },
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>

                          <TextField
                            fullWidth
                            label="URL liên kết"
                            value={item.href || ''}
                            onChange={e =>
                              handleBannerFieldChange(
                                bannerType,
                                index,
                                'href',
                                e.target.value,
                              )
                            }
                            placeholder="https://example.com"
                            InputProps={{
                              startAdornment: (
                                <LinkIcon
                                  sx={{ mr: 1, color: 'text.secondary' }}
                                />
                              ),
                            }}
                            helperText="URL trang web sẽ chuyển đến khi click vào banner"
                          />

                          {bannerType === 'slideBrand' && (
                            <TextField
                              fullWidth
                              label="Tên hiển thị"
                              value={item.content || ''}
                              onChange={e =>
                                handleBannerFieldChange(
                                  bannerType,
                                  index,
                                  'content',
                                  e.target.value,
                                )
                              }
                              placeholder="Tên thương hiệu"
                              InputProps={{
                                startAdornment: (
                                  <EditIcon
                                    sx={{ mr: 1, color: 'text.secondary' }}
                                  />
                                ),
                              }}
                              helperText="Tên thương hiệu sẽ hiển thị cùng với logo"
                            />
                          )}
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                );
              })}
            </Box>

            {/* Add New Banner Button */}
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 3,
                border: '2px dashed',
                borderColor: 'primary.light',
                bgcolor: 'primary.50',
                textAlign: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'primary.100',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                },
              }}
              onClick={() => handleAddBanner(bannerType)}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                <Avatar
                  sx={{
                    width: 56,
                    height: 56,
                    bgcolor: 'primary.main',
                    fontSize: '1.5rem',
                  }}
                >
                  <AddIcon />
                </Avatar>

                <Box>
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    color="primary.main"
                  >
                    Thêm banner mới
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Nhấn để thêm banner vào {title.toLowerCase()}
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </Box>
        </Collapse>
      </Card>
    );
  };

  return (
    <Box sx={{ width: '100%', position: 'relative' }}>
      {/* Header Controls */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}
      >
        <Box>
          <Typography variant="h5" fontWeight="bold" color="primary.main">
            🎨 Quản lý Banner & Hình ảnh
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Cấu hình banner cho trang chủ và mobile
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip
            title={
              previewMode ? 'Tắt chế độ xem trước' : 'Bật chế độ xem trước'
            }
          >
            <IconButton
              onClick={() => setPreviewMode(!previewMode)}
              color={previewMode ? 'primary' : 'default'}
              sx={{
                bgcolor: previewMode ? 'primary.light' : 'grey.100',
                '&:hover': {
                  bgcolor: previewMode ? 'primary.main' : 'grey.200',
                  color: previewMode ? 'white' : 'inherit',
                },
              }}
            >
              {previewMode ? <VisibilityIcon /> : <VisibilityOffIcon />}
            </IconButton>
          </Tooltip>

          <Button
            variant="outlined"
            size="medium"
            onClick={() => {
              setExpandedSections(new Set());
              setUploadingItems(new Set());
            }}
            sx={{
              borderWidth: 2,
              '&:hover': {
                borderWidth: 2,
              },
            }}
            startIcon={<span>🔄</span>}
          >
            Làm mới
          </Button>
        </Box>
      </Box>

      {/* Banner Sections */}
      {renderBannerSection('slideBrand', 'Thương hiệu')}
      {renderBannerSection('banner', 'Banner chính trang sản phẩm')}
      {renderBannerSection('bannerMobile', 'Banner mobile')}

      {/* Bottom Action Bar */}
      <Paper
        elevation={3}
        sx={{
          position: 'sticky',
          bottom: 0,
          p: 2,
          mt: 4,
          borderRadius: '16px 16px 0 0',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3 }}>
          <Button
            variant="outlined"
            size="large"
            onClick={() => window.location.reload()}
            sx={{
              borderColor: 'white',
              color: 'white',
              '&:hover': {
                borderColor: 'white',
                bgcolor: 'rgba(255,255,255,0.1)',
              },
            }}
            startIcon={<span>🔄</span>}
          >
            Làm mới trang
          </Button>

          <Button
            variant="contained"
            size="large"
            onClick={() => onSaveHomeSchema(homeSchema)}
            sx={{
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 'bold',
              bgcolor: 'white',
              color: 'primary.main',
              '&:hover': {
                bgcolor: 'grey.100',
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 20px rgba(255,255,255,0.3)',
              },
              transition: 'all 0.3s ease',
            }}
            startIcon={<span>💾</span>}
          >
            Lưu tất cả Banner
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default BannerManagement;
