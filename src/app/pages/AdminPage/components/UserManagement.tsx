import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Card,
  Typography,
  Dialog,
  DialogActions,
  DialogTitle,
  DialogContent,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { formatTime } from 'utils/moment';

interface UserManagementProps {
  listUser: any[];
  onDeleteUser: (userId: string) => void;
  onUpdateUserRole: (userId: string, currentRole: string) => void;
}

const UserManagement: React.FC<UserManagementProps> = ({
  listUser,
  onDeleteUser,
  onUpdateUserRole,
}) => {
  const [openEditUser, setOpenEditUser] = useState(false);
  const [userInfoDialog, setUserInfoDialog] = useState<any>({});

  const handleEditUser = () => {
    setOpenEditUser(false);
    onUpdateUserRole(userInfoDialog._id, userInfoDialog.role);
  };

  const handleDeleteUser = () => {
    onDeleteUser(userInfoDialog._id);
    setOpenEditUser(false);
  };

  const getRoleDisplay = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return { text: 'Admin', color: 'error' as const, icon: '👑' };
      case 'COLLAB':
        return { text: 'CTV', color: 'warning' as const, icon: '🤝' };
      case 'CUSTOMER':
        return { text: 'Khách hàng', color: 'info' as const, icon: '👤' };
      default:
        return {
          text: 'Không xác định',
          color: 'default' as const,
          icon: '❓',
        };
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Card
        sx={{
          p: 3,
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            👥 Danh sách người dùng
          </Typography>
          <Typography
            variant="body2"
            sx={{
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              px: 1.5,
              py: 0.5,
              borderRadius: 1,
              fontSize: '0.75rem',
            }}
          >
            {listUser?.length || 0} users
          </Typography>
        </Box>

        {/* Table Header */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: '60px 1fr 120px 150px 80px',
            gap: 2,
            p: 2,
            bgcolor: 'grey.100',
            borderRadius: 1,
            fontWeight: 'bold',
            mb: 1,
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            STT
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Tài khoản
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Vai trò
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Ngày tạo
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Thao tác
          </Typography>
        </Box>

        {/* User List */}
        <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
          {listUser?.map((item, index) => {
            const roleInfo = getRoleDisplay(item.role);
            const isAdmin = item.role === 'ADMIN';

            return (
              <Card
                key={index}
                sx={{
                  mb: 1,
                  p: 2,
                  cursor: isAdmin ? 'default' : 'pointer',
                  border: '1px solid #f0f0f0',
                  '&:hover': {
                    boxShadow: isAdmin ? 'none' : '0 2px 8px rgba(0,0,0,0.15)',
                    borderColor: isAdmin ? '#f0f0f0' : 'primary.light',
                  },
                  opacity: isAdmin ? 0.7 : 1,
                }}
                onClick={() => {
                  if (!isAdmin) {
                    setOpenEditUser(true);
                    setUserInfoDialog(item);
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: '60px 1fr 120px 150px 80px',
                    gap: 2,
                    alignItems: 'center',
                  }}
                >
                  <Typography variant="body2" fontWeight="500">
                    {index + 1}
                  </Typography>

                  <Box>
                    <Typography variant="body2" fontWeight="500">
                      {item.email}
                    </Typography>
                    {item.username && (
                      <Typography variant="caption" color="text.secondary">
                        {item.username}
                      </Typography>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2">{roleInfo.icon}</Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: `${roleInfo.color}.main`,
                        fontWeight: 500,
                      }}
                    >
                      {roleInfo.text}
                    </Typography>
                  </Box>

                  <Typography variant="body2" color="text.secondary">
                    {formatTime(item.createdAt)}
                  </Typography>

                  <Box>
                    {!isAdmin && (
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={e => {
                          e.stopPropagation();
                          setOpenEditUser(true);
                          setUserInfoDialog(item);
                        }}
                      >
                        ⚙️
                      </Button>
                    )}
                  </Box>
                </Box>
              </Card>
            );
          })}
        </Box>
      </Card>

      {/* Edit User Dialog */}
      <Dialog
        open={openEditUser}
        onClose={() => setOpenEditUser(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6" fontWeight="bold">
              ⚙️ Quản lý người dùng
            </Typography>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Box
              sx={{
                p: 3,
                bgcolor: 'grey.50',
                borderRadius: 2,
                border: '1px solid #e0e0e0',
              }}
            >
              <Typography variant="body1" fontWeight="500" gutterBottom>
                📧 Tài khoản: {userInfoDialog?.email}
              </Typography>

              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}
              >
                <Typography variant="body1" fontWeight="500">
                  Vai trò hiện tại:
                </Typography>
                {userInfoDialog?.role && (
                  <>
                    <Typography variant="body1">
                      {getRoleDisplay(userInfoDialog.role).icon}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: `${
                          getRoleDisplay(userInfoDialog.role).color
                        }.main`,
                        fontWeight: 'bold',
                      }}
                    >
                      {getRoleDisplay(userInfoDialog.role).text}
                    </Typography>
                  </>
                )}
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                📅 Ngày tạo: {formatTime(userInfoDialog?.createdAt)}
              </Typography>
            </Box>

            <Typography variant="body2" color="text.secondary">
              💡 Bạn có thể thay đổi vai trò hoặc xóa tài khoản này
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDeleteUser}
          >
            Xóa tài khoản
          </Button>

          <Box sx={{ flex: 1 }} />

          <Button variant="outlined" onClick={() => setOpenEditUser(false)}>
            Hủy
          </Button>

          <Button
            variant="contained"
            color={userInfoDialog.role === 'COLLAB' ? 'warning' : 'success'}
            onClick={handleEditUser}
          >
            {userInfoDialog.role === 'COLLAB'
              ? '⬇️ Hủy CTV'
              : '⬆️ Nâng cấp CTV'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
