import React, { useState } from 'react';
import {
  Box,
  Card,
  <PERSON>po<PERSON>,
  TextField,
  IconButton,
  Button,
  Chip,
  Avatar,
  Divider,
  Collapse,
  Tooltip,
  Alert,
  Grid,
  Paper,
  Badge,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ImageIcon from '@mui/icons-material/Image';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

interface CollectionManagementProps {
  homeSchema: any;
  onUpdateHomeSchema: (newHomeSchema: any) => void;
}

const CollectionManagement: React.FC<CollectionManagementProps> = ({
  homeSchema,
  onUpdateHomeSchema,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [editingItem, setEditingItem] = useState<number | null>(null);

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const handleCollectionFieldChange = (
    index: number,
    field: string,
    value: string,
  ) => {
    const newCollections = [...(homeSchema.collections || [])];
    newCollections[index] = {
      ...newCollections[index],
      [field]: value,
    };

    onUpdateHomeSchema({
      ...homeSchema,
      collections: newCollections,
    });
  };

  const handleAddCollection = () => {
    const newCollections = [...(homeSchema.collections || [])];
    newCollections.push({
      title: '',
      imageUrl: '',
      keyword: '',
      brandId: '',
      categoryId: '',
    });

    onUpdateHomeSchema({
      ...homeSchema,
      collections: newCollections,
    });
  };

  const handleRemoveCollection = (index: number) => {
    const newCollections = [...(homeSchema.collections || [])];
    newCollections.splice(index, 1);

    onUpdateHomeSchema({
      ...homeSchema,
      collections: newCollections,
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={2}
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              📚 Quản lý Bộ sưu tập
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, mt: 0.5 }}>
              Cấu hình các danh mục hiển thị tại trang sản phẩm
            </Typography>
          </Box>

          <Chip
            label={`${homeSchema?.collections?.length || 0} bộ sưu tập`}
            sx={{
              bgcolor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 'bold',
            }}
          />
        </Box>
      </Paper>

      {/* Collections List */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {homeSchema?.collections?.length === 0 && (
          <Alert severity="info" sx={{ borderRadius: 2 }}>
            <Typography>
              Chưa có bộ sưu tập nào. Nhấn nút "Thêm bộ sưu tập" để bắt đầu!
            </Typography>
          </Alert>
        )}

        {homeSchema?.collections?.map((item: any, index: number) => {
          const isExpanded = expandedItems.has(index);
          const isEditing = editingItem === index;

          return (
            <Card
              key={index}
              elevation={3}
              sx={{
                borderRadius: 3,
                overflow: 'hidden',
                border: isEditing ? '2px solid' : '1px solid',
                borderColor: isEditing ? 'primary.main' : 'divider',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  transform: 'translateY(-2px)',
                },
              }}
            >
              {/* Collection Header */}
              <Box
                sx={{
                  p: 2,
                  background: isEditing
                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Tooltip title="Kéo để sắp xếp">
                    <DragIndicatorIcon sx={{ cursor: 'grab', opacity: 0.7 }} />
                  </Tooltip>

                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      bgcolor: 'rgba(255,255,255,0.2)',
                      fontSize: '1.2rem',
                    }}
                  >
                    {index + 1}
                  </Avatar>

                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      {item.title || `Bộ sưu tập ${index + 1}`}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      {item.keyword
                        ? `Từ khóa: ${item.keyword}`
                        : 'Chưa có từ khóa'}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Tooltip title={isExpanded ? 'Thu gọn' : 'Mở rộng'}>
                    <IconButton
                      size="small"
                      onClick={() => toggleExpanded(index)}
                      sx={{ color: 'white' }}
                    >
                      {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                  </Tooltip>

                  <Tooltip title={isEditing ? 'Đang chỉnh sửa' : 'Chỉnh sửa'}>
                    <IconButton
                      size="small"
                      onClick={() => setEditingItem(isEditing ? null : index)}
                      sx={{ color: 'white' }}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Xóa bộ sưu tập">
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveCollection(index)}
                      sx={{ color: 'white' }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              {/* Collection Content */}
              <Collapse in={isExpanded} timeout="auto">
                <Box sx={{ p: 3 }}>
                  <Grid container spacing={3}>
                    {/* Image Preview */}
                    <Grid item xs={12} md={4}>
                      <Paper
                        elevation={2}
                        sx={{
                          p: 2,
                          borderRadius: 2,
                          textAlign: 'center',
                          bgcolor: '#fafafa',
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          gutterBottom
                          color="text.secondary"
                        >
                          Hình ảnh đại diện
                        </Typography>

                        <Box
                          sx={{
                            width: '100%',
                            height: 120,
                            borderRadius: 2,
                            overflow: 'hidden',
                            position: 'relative',
                            bgcolor: 'white',
                            border: '2px dashed #ddd',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {item.imageUrl ? (
                            <>
                              <img
                                src={item.imageUrl}
                                alt="Collection icon"
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'contain',
                                  background: 'white',
                                  padding: '4px',
                                }}
                                onError={e => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const errorDiv =
                                    target.parentElement?.querySelector(
                                      '.error-placeholder',
                                    ) as HTMLElement;
                                  if (errorDiv) errorDiv.style.display = 'flex';
                                }}
                              />
                              <Box
                                className="error-placeholder"
                                sx={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  display: 'none',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexDirection: 'column',
                                  bgcolor: 'error.light',
                                  color: 'error.contrastText',
                                }}
                              >
                                <ImageIcon sx={{ fontSize: '2rem', mb: 1 }} />
                                <Typography variant="caption">
                                  Lỗi tải hình
                                </Typography>
                              </Box>
                            </>
                          ) : (
                            <Box
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                color: 'text.secondary',
                              }}
                            >
                              <ImageIcon sx={{ fontSize: '2.5rem', mb: 1 }} />
                              <Typography variant="caption">
                                Chưa có hình ảnh
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Paper>
                    </Grid>

                    {/* Form Fields */}
                    <Grid item xs={12} md={8}>
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 2,
                        }}
                      >
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Tiêu đề bộ sưu tập"
                              value={item.title || ''}
                              onChange={e =>
                                handleCollectionFieldChange(
                                  index,
                                  'title',
                                  e.target.value,
                                )
                              }
                              variant="outlined"
                              InputProps={{
                                startAdornment: (
                                  <span style={{ marginRight: 8 }}>📝</span>
                                ),
                              }}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Từ khóa tìm kiếm"
                              value={item.keyword || ''}
                              onChange={e =>
                                handleCollectionFieldChange(
                                  index,
                                  'keyword',
                                  e.target.value,
                                )
                              }
                              variant="outlined"
                              InputProps={{
                                startAdornment: (
                                  <span style={{ marginRight: 8 }}>🔍</span>
                                ),
                              }}
                            />
                          </Grid>
                        </Grid>

                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="ID Thương hiệu"
                              value={item.brandId || ''}
                              onChange={e =>
                                handleCollectionFieldChange(
                                  index,
                                  'brandId',
                                  e.target.value,
                                )
                              }
                              variant="outlined"
                              InputProps={{
                                startAdornment: (
                                  <span style={{ marginRight: 8 }}>🏷️</span>
                                ),
                              }}
                            />
                          </Grid>

                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="ID Danh mục"
                              value={item.categoryId || ''}
                              onChange={e =>
                                handleCollectionFieldChange(
                                  index,
                                  'categoryId',
                                  e.target.value,
                                )
                              }
                              variant="outlined"
                              InputProps={{
                                startAdornment: (
                                  <span style={{ marginRight: 8 }}>📂</span>
                                ),
                              }}
                            />
                          </Grid>
                        </Grid>

                        <TextField
                          fullWidth
                          label="URL hình ảnh đại diện"
                          value={item.imageUrl || ''}
                          onChange={e =>
                            handleCollectionFieldChange(
                              index,
                              'imageUrl',
                              e.target.value,
                            )
                          }
                          variant="outlined"
                          multiline
                          rows={2}
                          InputProps={{
                            startAdornment: (
                              <span style={{ marginRight: 8 }}>🖼️</span>
                            ),
                          }}
                          helperText="Nhập URL hình ảnh để hiển thị icon cho bộ sưu tập"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Collapse>
            </Card>
          );
        })}
      </Box>

      {/* Add New Collection Button */}
      <Paper
        elevation={2}
        sx={{
          p: 3,
          borderRadius: 3,
          border: '2px dashed',
          borderColor: 'primary.light',
          bgcolor: 'primary.50',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'primary.main',
            bgcolor: 'primary.100',
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
          },
        }}
        onClick={handleAddCollection}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <Avatar
            sx={{
              width: 60,
              height: 60,
              bgcolor: 'primary.main',
              fontSize: '1.5rem',
            }}
          >
            <AddIcon />
          </Avatar>

          <Box>
            <Typography variant="h6" fontWeight="bold" color="primary.main">
              Thêm bộ sưu tập mới
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Nhấn để tạo bộ sưu tập sản phẩm mới
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, mt: 2 }}>
        <Button
          variant="outlined"
          size="large"
          onClick={() => {
            setExpandedItems(new Set());
            setEditingItem(null);
          }}
          sx={{
            borderWidth: 2,
            '&:hover': {
              borderWidth: 2,
            },
          }}
          startIcon={<span>🔄</span>}
        >
          Làm mới
        </Button>

        <Button
          variant="contained"
          size="large"
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #f093fb 30%, #f5576c 90%)',
            boxShadow: '0 4px 15px rgba(240, 147, 251, .4)',
            '&:hover': {
              background: 'linear-gradient(45deg, #e91e63 30%, #f44336 90%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 20px rgba(240, 147, 251, .6)',
            },
            transition: 'all 0.3s ease',
          }}
          startIcon={<span>💾</span>}
        >
          Lưu tất cả bộ sưu tập
        </Button>
      </Box>
    </Box>
  );
};

export default CollectionManagement;
