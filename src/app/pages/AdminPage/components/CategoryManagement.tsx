import React, { useState } from 'react';
import {
  Box,
  Card,
  Typo<PERSON>,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

interface CategoryManagementProps {
  brandList: any[];
  categoryList: any[];
  partnerList: any[];
  onRemoveBrand: (id: number) => void;
  onRemoveCategory: (id: number) => void;
  onRemovePartner: (id: number) => void;
  onAddBrand: (name: string) => void;
  onAddCategory: (name: string) => void;
  onAddPartner: (name: string) => void;
  onUpdatePartnerImage: (partnerId: string, imageUrl: string) => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  brandList,
  categoryList,
  partnerList,
  onRemoveBrand,
  onRemoveCategory,
  onRemovePartner,
  onAddBrand,
  onAddCategory,
  onAddPartner,
  onUpdatePartnerImage,
}) => {
  // State for hover effects
  const [categoryIndex, setCategoryIndex] = useState(-1);
  const [brandIndex, setBrandIndex] = useState(-1);
  const [partnerIndex, setPartnerIndex] = useState(-1);

  // State for dialogs
  const [isOpenAddNewBrand, setIsOpenAddNewBrand] = useState(false);
  const [isOpenAddNewCategory, setIsOpenAddNewCategory] = useState(false);
  const [isOpenAddNewPartner, setIsOpenAddNewPartner] = useState(false);
  const [isOpenAddImagePartner, setIsOpenAddImagePartner] = useState(false);

  // State for form inputs
  const [newBrand, setNewBrand] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newPartner, setNewPartner] = useState('');
  const [imagePartner, setImagePartner] = useState('');
  const [partnerId, setPartnerId] = useState('');

  // Handle add functions
  const handleAddNewBrand = () => {
    if (newBrand.trim()) {
      onAddBrand(newBrand.trim());
      setNewBrand('');
      setIsOpenAddNewBrand(false);
    }
  };

  const handleAddNewCategory = () => {
    if (newCategory.trim()) {
      onAddCategory(newCategory.trim());
      setNewCategory('');
      setIsOpenAddNewCategory(false);
    }
  };

  const handleAddNewPartner = () => {
    if (newPartner.trim()) {
      onAddPartner(newPartner.trim());
      setNewPartner('');
      setIsOpenAddNewPartner(false);
    }
  };

  const handleAddImagePartner = () => {
    if (imagePartner.trim() && partnerId) {
      onUpdatePartnerImage(partnerId, imagePartner.trim());
      setImagePartner('');
      setPartnerId('');
      setIsOpenAddImagePartner(false);
    }
  };

  const renderSection = (
    title: string,
    icon: string,
    items: any[],
    onAdd: () => void,
    onRemove: (id: number) => void,
    hoverIndex: number,
    setHoverIndex: (index: number) => void,
    isPartner = false,
  ) => (
    <Card
      sx={{
        p: 3,
        border: '1px solid #e0e0e0',
        borderRadius: 2,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        height: 'fit-content',
        maxHeight: 600,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 'bold',
            color: 'primary.main',
          }}
        >
          {icon} {title}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            bgcolor: 'primary.light',
            color: 'primary.contrastText',
            px: 1.5,
            py: 0.5,
            borderRadius: 1,
            fontSize: '0.75rem',
          }}
        >
          {items.length} items
        </Typography>
      </Box>

      <Box sx={{ flex: 1, overflowY: 'auto', mb: 2, maxHeight: 400 }}>
        {items.map((item, index) => (
          <Card
            key={index}
            sx={{
              mb: 1,
              p: 2,
              position: 'relative',
              cursor: isPartner ? 'pointer' : 'default',
              border: '1px solid #f0f0f0',
              '&:hover': {
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                borderColor: 'primary.light',
              },
            }}
            onMouseEnter={() => setHoverIndex(index)}
            onMouseLeave={() => setHoverIndex(-1)}
            onClick={
              isPartner
                ? () => {
                    setImagePartner(item.images?.[0]?.url || '');
                    setPartnerId(item.id);
                    setIsOpenAddImagePartner(true);
                  }
                : undefined
            }
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography
                sx={{
                  flex: 1,
                  textTransform: 'capitalize',
                  fontWeight: 500,
                }}
              >
                {item.name}
              </Typography>

              {hoverIndex === index && (
                <IconButton
                  size="small"
                  color="error"
                  onClick={e => {
                    e.stopPropagation();
                    onRemove(item.id);
                  }}
                  sx={{
                    '&:hover': {
                      bgcolor: 'error.light',
                      color: 'white',
                    },
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </Box>

            {isPartner && item.images?.[0]?.url && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <img
                  src={item.images[0].url}
                  alt={item.name}
                  style={{
                    maxWidth: '100%',
                    maxHeight: 80,
                    objectFit: 'contain',
                    borderRadius: 4,
                  }}
                />
              </Box>
            )}
          </Card>
        ))}
      </Box>

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={onAdd}
        sx={{
          borderStyle: 'dashed',
          borderWidth: 2,
          py: 1.5,
          '&:hover': {
            borderStyle: 'solid',
            bgcolor: 'primary.light',
            color: 'primary.contrastText',
          },
        }}
      >
        Thêm {title.toLowerCase()}
      </Button>
    </Card>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: 'repeat(3, 1fr)',
          },
          gap: 3,
          mb: 3,
        }}
      >
        {/* Categories */}
        {renderSection(
          'Danh mục',
          '🏷️',
          categoryList,
          () => setIsOpenAddNewCategory(true),
          onRemoveCategory,
          categoryIndex,
          setCategoryIndex,
        )}

        {/* Brands */}
        {renderSection(
          'Thương hiệu',
          '🏢',
          brandList,
          () => setIsOpenAddNewBrand(true),
          onRemoveBrand,
          brandIndex,
          setBrandIndex,
        )}

        {/* Partners */}
        {renderSection(
          'Đối tác',
          '🤝',
          partnerList,
          () => setIsOpenAddNewPartner(true),
          onRemovePartner,
          partnerIndex,
          setPartnerIndex,
          true,
        )}
      </Box>

      {/* Dialogs */}
      <Dialog
        open={isOpenAddNewCategory}
        onClose={() => setIsOpenAddNewCategory(false)}
      >
        <DialogTitle>Thêm danh mục mới</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tên danh mục"
            fullWidth
            variant="outlined"
            value={newCategory}
            onChange={e => setNewCategory(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsOpenAddNewCategory(false)}>Hủy</Button>
          <Button onClick={handleAddNewCategory} variant="contained">
            Thêm
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={isOpenAddNewBrand}
        onClose={() => setIsOpenAddNewBrand(false)}
      >
        <DialogTitle>Thêm thương hiệu mới</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tên thương hiệu"
            fullWidth
            variant="outlined"
            value={newBrand}
            onChange={e => setNewBrand(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsOpenAddNewBrand(false)}>Hủy</Button>
          <Button onClick={handleAddNewBrand} variant="contained">
            Thêm
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={isOpenAddNewPartner}
        onClose={() => setIsOpenAddNewPartner(false)}
      >
        <DialogTitle>Thêm đối tác mới</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tên đối tác"
            fullWidth
            variant="outlined"
            value={newPartner}
            onChange={e => setNewPartner(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsOpenAddNewPartner(false)}>Hủy</Button>
          <Button onClick={handleAddNewPartner} variant="contained">
            Thêm
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={isOpenAddImagePartner}
        onClose={() => setIsOpenAddImagePartner(false)}
      >
        <DialogTitle>Cập nhật ảnh đối tác</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="URL ảnh"
            fullWidth
            variant="outlined"
            value={imagePartner}
            onChange={e => setImagePartner(e.target.value)}
            placeholder="https://example.com/image.jpg"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsOpenAddImagePartner(false)}>Hủy</Button>
          <Button onClick={handleAddImagePartner} variant="contained">
            Cập nhật
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CategoryManagement;
