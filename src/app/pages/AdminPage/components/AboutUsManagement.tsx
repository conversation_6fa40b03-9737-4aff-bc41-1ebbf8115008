import React from 'react';
import {
  <PERSON>,
  Card,
  Typography,
  TextField,
  IconButton,
  Button,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

interface AboutUsManagementProps {
  homeSchema: any;
  onUpdateHomeSchema: (newHomeSchema: any) => void;
}

const AboutUsManagement: React.FC<AboutUsManagementProps> = ({
  homeSchema,
  onUpdateHomeSchema,
}) => {
  const handleAboutUsChange = (field: string, value: string) => {
    onUpdateHomeSchema({
      ...homeSchema,
      us: {
        ...homeSchema.us,
        aboutUs: {
          ...homeSchema.us?.aboutUs,
          [field]: value,
        },
      },
    });
  };

  const handleWhyUsChange = (index: number, field: string, value: string) => {
    const newWhyUs = [...(homeSchema.us?.whyUs || [])];
    newWhyUs[index] = {
      ...newWhyUs[index],
      [field]: value,
    };

    onUpdateHomeSchema({
      ...homeSchema,
      us: {
        ...homeSchema.us,
        whyUs: newWhyUs,
      },
    });
  };

  const handleAddWhyUs = () => {
    const newWhyUs = [...(homeSchema.us?.whyUs || [])];
    newWhyUs.push({
      title: '',
      content: '',
      imageUrl: '',
    });

    onUpdateHomeSchema({
      ...homeSchema,
      us: {
        ...homeSchema.us,
        whyUs: newWhyUs,
      },
    });
  };

  const handleRemoveWhyUs = (index: number) => {
    const newWhyUs = [...(homeSchema.us?.whyUs || [])];
    newWhyUs.splice(index, 1);

    onUpdateHomeSchema({
      ...homeSchema,
      us: {
        ...homeSchema.us,
        whyUs: newWhyUs,
      },
    });
  };

  const handleMiddleBannerChange = (field: string, value: string) => {
    onUpdateHomeSchema({
      ...homeSchema,
      middleBanner: {
        ...homeSchema.middleBanner,
        [field]: value,
      },
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* About Us Section */}
      <Card
        sx={{
          height: 'fit-content',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          p: 2,
        }}
      >
        <Typography
          sx={{
            p: 1,
            bgcolor: 'gray',
            borderRadius: 1,
          }}
        >
          Về chúng tôi
        </Typography>

        <TextField
          label="Nội dung về chúng tôi"
          multiline
          rows={4}
          value={homeSchema.us?.aboutUs?.content || ''}
          onChange={e => handleAboutUsChange('content', e.target.value)}
        />

        <TextField
          label="URL hình ảnh về chúng tôi"
          value={homeSchema.us?.aboutUs?.imageUrl || ''}
          onChange={e => handleAboutUsChange('imageUrl', e.target.value)}
        />
      </Card>

      {/* Middle Banner Section */}
      <Card
        sx={{
          height: 'fit-content',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          p: 2,
        }}
      >
        <Typography
          sx={{
            p: 1,
            bgcolor: 'gray',
            borderRadius: 1,
          }}
        >
          Banner giữa trang
        </Typography>

        <TextField
          label="Tiêu đề"
          value={homeSchema.middleBanner?.title || ''}
          onChange={e => handleMiddleBannerChange('title', e.target.value)}
        />

        <TextField
          label="Phụ đề"
          value={homeSchema.middleBanner?.subTitle || ''}
          onChange={e => handleMiddleBannerChange('subTitle', e.target.value)}
        />

        <TextField
          label="URL hình ảnh"
          value={homeSchema.middleBanner?.imageUrl || ''}
          onChange={e => handleMiddleBannerChange('imageUrl', e.target.value)}
        />
      </Card>

      {/* Why Us Section */}
      <Card
        sx={{
          height: 'fit-content',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          p: 2,
        }}
      >
        <Typography
          sx={{
            p: 1,
            bgcolor: 'gray',
            borderRadius: 1,
          }}
        >
          Tại sao chọn chúng tôi
        </Typography>

        {homeSchema.us?.whyUs?.map((item: any, index: number) => (
          <Card
            key={index}
            sx={{
              p: 1,
              position: 'relative',
            }}
          >
            <IconButton
              sx={{
                width: 'fit-content',
                position: 'absolute',
                right: 0,
                color: 'orangered',
              }}
              onClick={() => handleRemoveWhyUs(index)}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                pr: 5,
              }}
            >
              <TextField
                label="Tiêu đề"
                value={homeSchema.us?.whyUs?.[index]?.title || ''}
                onChange={e =>
                  handleWhyUsChange(index, 'title', e.target.value)
                }
              />

              <TextField
                label="Nội dung"
                multiline
                rows={3}
                value={homeSchema.us?.whyUs?.[index]?.content || ''}
                onChange={e =>
                  handleWhyUsChange(index, 'content', e.target.value)
                }
              />

              <TextField
                label="URL hình ảnh"
                value={homeSchema.us?.whyUs?.[index]?.imageUrl || ''}
                onChange={e =>
                  handleWhyUsChange(index, 'imageUrl', e.target.value)
                }
              />
            </Box>
          </Card>
        ))}

        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
          <IconButton
            sx={{
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              '&:hover': {
                bgcolor: 'primary.main',
              },
            }}
            onClick={handleAddWhyUs}
          >
            <AddIcon />
          </IconButton>
        </Box>
      </Card>

      {/* Save Button for About Us */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Button
          variant="contained"
          size="large"
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #4facfe 30%, #00f2fe 90%)',
            boxShadow: '0 3px 5px 2px rgba(79, 172, 254, .3)',
            '&:hover': {
              background: 'linear-gradient(45deg, #2196F3 30%, #00BCD4 90%)',
            },
          }}
          startIcon={<span>💾</span>}
        >
          Lưu Về chúng tôi
        </Button>
      </Box>
    </Box>
  );
};

export default AboutUsManagement;
