import Box from '@mui/material/Box';
import * as React from 'react';
import { keyframes } from '@emotion/react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import QueueIcon from '@mui/icons-material/Queue';
import Grid from '@mui/material/Unstable_Grid2';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import DeleteIcon from '@mui/icons-material/Delete';
import MenuItem from '@mui/material/MenuItem';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import { Card, useMediaQuery } from '@mui/material';

import { useDispatch, useSelector } from 'react-redux';
import { useUserSlice } from 'app/slice/user';
import { userInfoSelector } from 'app/slice/user/selectors';
import { useUtilsSlice } from 'app/slice/utils';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';

import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';

import { formatTime } from 'utils/moment';
import JoditEditor from 'jodit-react';
import { editorConfigJodit, createMarkup } from 'utils/jodit';
import YouTube, { YouTubeProps } from 'react-youtube';
import { request } from 'utils/request';
import { filterArrayByProperty } from 'utils/app';
import get from 'lodash/get';
import set from 'lodash/set';

import styled from 'styled-components';
import Recently from 'app/components/Recently/Recently';

import banner1 from 'assets/images/news/638463803756808331_group-2085661142.webp';
import { detailInit } from './detail';
import { uploadFiles } from 'utils/uploadImg';

const initNewsList = [
  {
    name: 'ƯU ĐÃI MỞ BÁN – MÁY HÚT ĐỆM SMAROCK TIẾT KIỆM 27%, GIÁ CHỈ TỪ 1.990.000 VNĐ',
    date: 'Ngày:20/03/2023 lúc 09:53AM',
    sortDes:
      'Smarock chính thức gia nhập thị trường máy hút đệm Việt Nam với nhiều tính năng đặc biệt như tiêu diệt vi khuẩn bằng sóng siêu âm, diệt ve rận bằng tia UV và hệ thống sưởi đa hướng có...',
    Description: '',
    banner: banner1,
  },
  {
    name: 'ƯU ĐÃI MỞ BÁN – MÁY HÚT ĐỆM SMAROCK TIẾT KIỆM 27%, GIÁ CHỈ TỪ 1.990.000 VNĐ',
    date: 'Ngày:20/03/2023 lúc 09:53AM',
    sortDes:
      'Smarock chính thức gia nhập thị trường máy hút đệm Việt Nam với nhiều tính năng đặc biệt như tiêu diệt vi khuẩn bằng sóng siêu âm, diệt ve rận bằng tia UV và hệ thống sưởi đa hướng có...',
    Description: '',
    banner: banner1,
  },
  {
    name: 'ƯU ĐÃI MỞ BÁN – MÁY HÚT ĐỆM SMAROCK TIẾT KIỆM 27%, GIÁ CHỈ TỪ 1.990.000 VNĐ',
    date: 'Ngày:20/03/2023 lúc 09:53AM',
    sortDes:
      'Smarock chính thức gia nhập thị trường máy hút đệm Việt Nam với nhiều tính năng đặc biệt như tiêu diệt vi khuẩn bằng sóng siêu âm, diệt ve rận bằng tia UV và hệ thống sưởi đa hướng có...',
    Description: '',
    banner: banner1,
  },
  {
    name: 'ƯU ĐÃI MỞ BÁN – MÁY HÚT ĐỆM SMAROCK TIẾT KIỆM 27%, GIÁ CHỈ TỪ 1.990.000 VNĐ',
    date: 'Ngày:20/03/2023 lúc 09:53AM',
    sortDes:
      'Smarock chính thức gia nhập thị trường máy hút đệm Việt Nam với nhiều tính năng đặc biệt như tiêu diệt vi khuẩn bằng sóng siêu âm, diệt ve rận bằng tia UV và hệ thống sưởi đa hướng có...',
    Description: '',
    banner: banner1,
  },
];

const labelOptions = [
  {
    value: 'promotion',
    label: 'Khuyến mãi',
  },
  {
    value: 'tips',
    label: 'Thủ thuật',
  },
  {
    value: 'video',
    label: 'Video',
  },
];

const upUp = keyframes`
  0% {
    transform: translateY(0px)
  }
  100% {
    transform: translateY(-10px)
  }
`;

export function NewsPage() {
  const [news, setNews] = React.useState<any[]>([]);
  const [promotions, setPromotion] = React.useState<any[]>([]);
  const [tips, setTips] = React.useState<any[]>([]);
  const [videos, setVideos] = React.useState<any[]>([]);
  const [noneVideos, setNoneVideos] = React.useState<any[]>([]);
  const [newsInfo, setNewsInfo] = React.useState<any>({});
  const [videoLead, setVideoLead] = React.useState<any>({});
  const [data, setData] = React.useState<any>('');
  const [name, setName] = React.useState<any>('');
  const [nameId, setNameId] = React.useState<any>('');
  const [imageUrl, setImageUrl] = React.useState<any>('');
  const [idUrl, setIdUrl] = React.useState<any>('');
  const [label, setLabel] = React.useState<any>('');
  const [limit, setLimit] = React.useState<number>(10);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [valueTab, setValueTab] = React.useState<any>('tin-moi');
  const [openNew, setOpenNew] = React.useState<boolean>(false);
  const [isAddDetail, setIsAddDetail] = React.useState<boolean>(false);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const navigate = useNavigate();
  React.useEffect(() => {
    setNews(initNewsList);
  }, []);
  const onClose = () => {
    setOpenNew(false);
  };

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: cartActions } = useCartSlice();
  const { actions: userActions } = useUserSlice();
  const cartInfo = useSelector(cartInfoSelector);
  const userInfo = useSelector(userInfoSelector);

  const params = useParams();
  const [searchParams] = useSearchParams();
  const { path } = params;
  const pathname = useLocation().pathname;

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValueTab(newValue);
    navigate(`/tin-tuc/${newValue}`);
    // console.log('event: ', event);
    // console.log('valueTab: ', newValue);
  };

  const vietnameseToAscii = inputStr => {
    // Chuyển đổi từ tiếng Việt có dấu sang không dấu
    var convertedStr = inputStr
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Thay đổi dấu cách thành '-'
    convertedStr = convertedStr.replace(/\s+/g, '-');

    return convertedStr;
  };

  const convertMongoCreatedAt = createdAt => {
    const now = new Date().getTime();
    const createdAtDate = new Date(createdAt).getTime();

    // Tính số mili giây chênh lệch giữa thời điểm hiện tại và thời điểm tạo
    const diffMilliseconds = now - createdAtDate;

    // Tính số phút chênh lệch
    const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));

    // Tính số giờ chênh lệch
    const diffHours = Math.floor(diffMilliseconds / (1000 * 60 * 60));

    // Tính số ngày chênh lệch
    const diffDays = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24));
    if (!createdAt) return '';
    if (diffHours < 1) {
      return `${diffMinutes} phút trước`;
    } else if (diffDays < 1) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 2) {
      return `${diffDays} ngày trước`;
    } else {
      return formatTime(createdAt); // Trả về thời gian tạo gốc nếu đã quá 2 ngày
    }
  };

  const handleEditProduct = async () => {
    const newsData = {
      detail: data,
    };
    const method = 'put';
    const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${newsInfo._id}`;
    try {
      const result = await request(method, url, newsData);
      setIsAddDetail(false);
      fetchNewsPage();
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };
  const handleDeleteNewsId = async id => {
    const method = 'delete';
    const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${id}`;
    try {
      const result = await request(method, url);
      setIsAddDetail(false);
      setData('');
      fetchNewsPage();
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const handleCreateNews = async () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'post',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news`,
          { name, id: nameId, imageUrl, label, idUrl },
        );
        if (result) {
          setOpenNew(false);
          setName('');
          setImageUrl('');
          setIdUrl('');
          setLabel('');
          fetchNewsPage();
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    apiHandler();
  };

  const filterObjects = (arr, key, value) => {
    return arr.filter(item => item[key] === value);
  };

  const fetchNewsPage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news`,
        );

        // console.log({ result });
        setNews(result);
        const newPromotion = filterObjects(result, 'label', 'promotion');
        setPromotion(newPromotion);
        const newTips = filterObjects(result, 'label', 'tips');
        setTips(newTips);
        const newVideos = filterObjects(result, 'label', 'video');
        setVideos(newVideos);
        setVideoLead(newVideos[0]);
        const newNoneVideos = () => {
          return result.filter(item => item.label !== 'video');
        };
        setNoneVideos(newNoneVideos);
        // console.log('noneVideo: ', newNoneVideos);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const handleScrollToBox = () => {
    const boxElement = document.getElementById('news-box');
    if (boxElement) {
      boxElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const onClickRef = id => {
    navigate(`/tin-tuc/${id}`);
  };

  const onPlayerReady: YouTubeProps['onReady'] = event => {
    // access to player in all event handlers via event.target
    event.target.pauseVideo();
  };
  const opts1: YouTubeProps['opts'] = {
    height: '200',
    width: '260',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };
  const opts4: YouTubeProps['opts'] = {
    height: '200',
    width: '300',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };

  const opts2: YouTubeProps['opts'] = {
    height: '250',
    width: '400',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };

  const opts3: YouTubeProps['opts'] = {
    height: '450',
    width: '800',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };

  React.useEffect(() => {
    fetchNewsPage();
    if (pathname.split('/')[1] !== valueTab)
      setValueTab(pathname.split('/')[2]);
    // console.log("pathname: ",pathname.split('/')[2])
  }, [valueTab]);

  const AddDetailDialog = () => {
    React.useEffect(() => {}, []);
    return (
      <MuiDialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={isAddDetail}
        onClose={() => setIsAddDetail(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignContent: 'center',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                fontWeight: 'bold',
              }}
            >
              Chỉnh sửa nội dung tin tức
            </Typography>
            <Box>
              <IconButton color="warning" onClick={handleEditProduct}>
                <SaveAsIcon />
              </IconButton>
              <IconButton
                color="warning"
                onClick={() => handleDeleteNewsId(newsInfo._id)}
              >
                <DeleteIcon />
              </IconButton>
              <IconButton color="warning" onClick={() => setIsAddDetail(false)}>
                <HighlightOffIcon />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              pb: 5,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <JoditEditor
              value={data}
              config={editorConfigJodit}
              onChange={value => setData(value)}
            />
          </Box>
        </DialogContent>
      </MuiDialog>
    );
  };

  const TruncatedTypography = styled(Typography)`
    padding: 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  `;

  return (
    <>
      <Helmet>
        <title>Tin tức</title>
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100%',
          maxWidth: '1200px',
          px: '2px',
          mx: 'auto',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <Box sx={{ width: '100%', typography: 'body1' }}>
          <TabContext value={valueTab}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', pl: 3 }}>
              <TabList
                onChange={handleChange}
                aria-label="lab API tabs example"
              >
                <Tab label="TIN MỚI" value="tin-moi" />
                <Tab label="KHUYẾN MÃI" value="khuyen-mai" />
                <Tab label="THỦ THUẬT" value="thu-thuat" />
                <Tab label="VIDEO" value="video" />
              </TabList>
            </Box>
            {/* <TabPanel value="1">Item One</TabPanel>
        <TabPanel value="2">Item Two</TabPanel>
        <TabPanel value="3">Item Three</TabPanel> */}
          </TabContext>
        </Box>
        <Box
          sx={{
            display: valueTab === 'tin-moi' ? 'flex' : 'none',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: '440px',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                  gap: 1,
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 2,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  cursor: 'pointer',
                }}
              >
                <Card
                  onClick={() => {
                    onClickRef(noneVideos[0]?.id);
                  }}
                  // onMouseEnter={() => (item.hover = true)}
                  // onMouseLeave={() => (item.hover = false)}
                  sx={{
                    width: '100%',
                    height: '100%',
                    background: '',
                    borderRadius: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'start',
                    textTransform: 'none',
                    color: 'black',
                    bgcolor: 'white',
                    position: 'relative',
                    [theme.breakpoints.down('md')]: {
                      // justifyContent: 'center',
                      // alignItems: 'center',
                      mx: 'auto',
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: '100%',
                    }}
                  >
                    <img
                      src={noneVideos[0]?.imageUrl}
                      alt=""
                      width={'100%'}
                      height={'auto'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      width: '100%',
                      textAlign: 'left',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      p: 1,
                    }}
                  >
                    {noneVideos[0]?.name}
                  </Typography>
                  <Typography
                    sx={{
                      width: '100%',
                      color: 'gray',
                      // fontStyle: 'italic',
                      textAlign: 'left',
                      fontSize: '12px',
                      px: 1,
                    }}
                  >
                    {noneVideos[0]?.description}
                  </Typography>
                  <Typography
                    sx={{
                      width: '100%',
                      color: 'gray',
                      fontStyle: 'italic',
                      textAlign: 'left',
                      fontSize: '12px',
                      px: 1,
                    }}
                  >
                    {convertMongoCreatedAt(noneVideos[0]?.createdAt)}
                  </Typography>
                </Card>
              </Box>
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                }}
              >
                {noneVideos?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 0 && index < 5 ? 'flex' : 'none',
                      width: '100%',
                      height: '100px',
                      cursor: 'pointer',
                      overflow: 'hidden',
                    }}
                    onClick={() => {
                      onClickRef(item.id);
                    }}
                  >
                    <Box
                      sx={{
                        width: '150px',
                        height: '100%',
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'150px'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Paper>
            <Paper
              sx={{
                flex: 1,
                height: '440px',
                display: 'flex',
                flexDirection: 'column',
                my: 1,
                overflow: 'hidden',
                borderRadius: 1,
                [theme.breakpoints.down('md')]: {
                  display: 'none',
                },
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  width: '100%',
                  bgcolor: '',
                  p: 2,
                  fontWeight: 'bold',
                  borderBottom: '0.5px #eeebeb solid',
                }}
              >
                Xem nhiều
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  p: 2,
                  overflowY: 'auto',
                }}
              >
                {noneVideos?.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: index < 5 ? 'flex' : 'none',
                      alignItems: 'center',
                      width: '100%',
                      height: '80px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                    }}
                    onClick={() => onClickRef(item.id)}
                  >
                    <Typography
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '40px',
                        height: '40px',
                        p: 2,
                        borderRadius: '100%',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                      }}
                    >
                      {index + 1}
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Box>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                // flex: 3,
                height: 'auto',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                  [theme.breakpoints.down('sm')]: {},
                }}
              >
                {noneVideos?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 4 && index < limit ? 'flex' : 'none',
                      width: '100%',
                      height: '200px',
                      cursor: 'pointer',
                      gap: 2,
                      overflow: 'hidden',
                      [theme.breakpoints.down('sm')]: {
                        flexDirection: 'column',
                        height: 'auto',
                      },
                    }}
                    onClick={() => onClickRef(item.id)}
                  >
                    <Box
                      sx={{
                        width: '300px',
                        height: '100%',
                        flexShrink: 0,
                        [theme.breakpoints.down('sm')]: {
                          width: '100%',
                          flexShrink: 1,
                        },
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'100%'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'green',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.label === 'tips'
                          ? 'Thủ thuật'
                          : item.label === 'promotion'
                          ? 'Khuyến mãi'
                          : item.label === 'video'
                          ? 'Video'
                          : ''}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                        }}
                      >
                        {item.name}
                      </Typography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          // fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.description}
                      </Typography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          mt: 'auto',
                        }}
                      >
                        {convertMongoCreatedAt(item.createdAt)}
                      </Typography>
                    </Box>
                  </Card>
                ))}
                <Button
                  variant="outlined"
                  color="success"
                  sx={{
                    display: noneVideos.length > 10 ? '' : 'none',
                    width: 'fit-content',
                    mx: 'auto',
                  }}
                  onClick={() => setLimit(limit + 5)}
                >
                  Xem thêm
                </Button>
              </Box>
            </Paper>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              <Paper
                sx={{
                  flex: 1,
                  height: 'auto',
                  display: 'flex',
                  flexDirection: 'column',
                  my: 1,
                  overflow: 'hidden',
                  borderRadius: 1,
                  [theme.breakpoints.down('md')]: {
                    display: 'none',
                  },
                  // boxShadow:
                  //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                <Typography
                  sx={{
                    width: '100%',
                    bgcolor: '',
                    p: 2,
                    fontWeight: 'bold',
                    borderBottom: '0.5px #eeebeb solid',
                  }}
                >
                  Khuyến mãi
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    p: 2,
                    overflowY: 'auto',
                  }}
                >
                  {promotions?.map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: 'flex',
                        width: '100%',
                        height: '80px',
                        overflow: 'hidden',
                        cursor: 'pointer',
                      }}
                      onClick={() => onClickRef(item.id)}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          p: 1,
                        }}
                      >
                        <TruncatedTypography>{item.name}</TruncatedTypography>
                        <Typography
                          sx={{
                            width: '100%',
                            color: 'gray',
                            fontStyle: 'italic',
                            fontSize: '12px',
                          }}
                        >
                          {item.date}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Paper>
              <Paper
                sx={{
                  flex: 1,
                  height: 'auto',
                  display: 'flex',
                  flexDirection: 'column',
                  my: 1,
                  overflow: 'hidden',
                  borderRadius: 1,
                  [theme.breakpoints.down('md')]: {
                    display: 'none',
                  },
                  // boxShadow:
                  //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                <Typography
                  sx={{
                    width: '100%',
                    bgcolor: '',
                    p: 2,
                    fontWeight: 'bold',
                    borderBottom: '0.5px #eeebeb solid',
                  }}
                >
                  Video
                </Typography>
                <Box
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-around',
                    gap: 2,
                    p: 2,
                    overflowY: 'auto',
                  }}
                >
                  {videos?.map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: index < 3 ? 'flex' : 'none',
                        width: '100%',
                        height: 'auto',
                        overflow: 'hidden',
                      }}
                      onClick={() => {
                        setValueTab('4');
                        setVideoLead(item);
                      }}
                    >
                      <YouTube
                        videoId={item.idUrl}
                        opts={opts1}
                        onReady={onPlayerReady}
                      />
                    </Box>
                  ))}
                  <Button
                    variant="outlined"
                    color="success"
                    onClick={() => {
                      navigate('/tin-tuc/video');
                      setValueTab('video');
                    }}
                  >
                    Xem Thêm
                  </Button>
                </Box>
              </Paper>
            </Box>
          </Box>
          <Card
            sx={{
              width: '100%',
              maxWidth: '1200px',
              display: 'flex',
              gap: 2,
              justifyContent: 'space-around',
              m: 3,
              [theme.breakpoints.down('sm')]: {
                display: 'none',
              },
              // mr: 'auto',
            }}
          >
            <YouTube
              videoId={videos[0]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
            <YouTube
              videoId={videos[1]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
            <YouTube
              videoId={videos[2]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
          </Card>
          <Card
            sx={{
              display: 'flex',
              m: 3,
              [theme.breakpoints.up('sm')]: {
                display: 'none',
              },
              // mr: 'auto',
            }}
          >
            <YouTube
              videoId={videos[0]?.idUrl}
              opts={opts4}
              onReady={onPlayerReady}
            />
          </Card>
        </Box>
        <Box
          sx={{
            display: valueTab === 'khuyen-mai' ? 'flex' : 'none',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: '440px',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 2,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  cursor: 'pointer',
                }}
              >
                <Card
                  onClick={() => {
                    onClickRef(promotions[0]?.id);
                  }}
                  // onMouseEnter={() => (item.hover = true)}
                  // onMouseLeave={() => (item.hover = false)}
                  sx={{
                    width: '100%',
                    height: '100%',
                    background: '',
                    borderRadius: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'start',
                    textTransform: 'none',
                    color: 'black',
                    bgcolor: 'white',
                    position: 'relative',
                    [theme.breakpoints.down('md')]: {
                      // justifyContent: 'center',
                      // alignItems: 'center',
                      mx: 'auto',
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: '100%',
                    }}
                  >
                    <img
                      src={promotions[0]?.imageUrl}
                      alt=""
                      width={'100%'}
                      height={'auto'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      width: '100%',
                      textAlign: 'left',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      p: 1,
                    }}
                  >
                    {promotions[0]?.name}
                  </Typography>
                  <Typography
                    sx={{
                      width: '100%',
                      color: 'gray',
                      fontStyle: 'italic',
                      textAlign: 'left',
                      fontSize: '12px',
                      p: 1,
                    }}
                  >
                    {convertMongoCreatedAt(promotions[0]?.createdAt)}
                  </Typography>
                </Card>
              </Box>
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                }}
              >
                {promotions?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 0 ? 'flex' : 'none',
                      width: '100%',
                      height: '100px',
                      cursor: 'pointer',
                      overflow: 'hidden',
                    }}
                    onClick={() => {
                      onClickRef(index);
                    }}
                  >
                    <Box
                      sx={{
                        width: '150px',
                        height: '100%',
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'150px'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Paper>
            <Paper
              sx={{
                flex: 1,
                height: '440px',
                display: 'flex',
                flexDirection: 'column',
                my: 1,
                overflow: 'hidden',
                borderRadius: 1,
                [theme.breakpoints.down('md')]: {
                  display: 'none',
                },
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  width: '100%',
                  bgcolor: '',
                  p: 2,
                  fontWeight: 'bold',
                  borderBottom: '0.5px #eeebeb solid',
                }}
              >
                Xem nhiều
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  p: 2,
                  overflowY: 'auto',
                }}
              >
                {promotions?.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: index < 5 ? 'flex' : 'none',
                      alignItems: 'center',
                      width: '100%',
                      height: '80px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                    }}
                    onClick={() => onClickRef(item.id)}
                  >
                    <Typography
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '40px',
                        height: '40px',
                        p: 2,
                        borderRadius: '100%',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                      }}
                    >
                      {index + 1}
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Box>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: 'auto',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                }}
              >
                {promotions?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 4 ? 'flex' : 'none',
                      width: '100%',
                      height: '200px',
                      cursor: 'pointer',
                      gap: 2,
                      overflow: 'hidden',
                      [theme.breakpoints.down('md')]: {
                        flexDirection: 'column',
                        height: 'auto',
                        gap: 1,
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: '300px',
                        height: '100%',
                        flexShrink: 0,
                        [theme.breakpoints.down('md')]: {
                          height: 'auto',
                          gap: 1,
                        },
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'100%'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'green',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.label === 'tips'
                          ? 'Thủ thuật'
                          : item.label === 'promotion'
                          ? 'Khuyến mãi'
                          : item.label === 'video'
                          ? 'Video'
                          : ''}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                        }}
                      >
                        {item.name}
                      </Typography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          mt: 'auto',
                        }}
                      >
                        {convertMongoCreatedAt(item.createdAt)}
                      </Typography>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Paper>
          </Box>
          {/* <Card
            sx={{
              display: 'flex',
              m: 3,
              // mr: 'auto',
            }}
          >
            <YouTube
              videoId={videos[0]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
          </Card> */}
        </Box>
        <Box
          sx={{
            display: valueTab === 'thu-thuat' ? 'flex' : 'none',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: '440px',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                  gap: 2,
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 2,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  cursor: 'pointer',
                }}
              >
                <Card
                  onClick={() => {
                    onClickRef(tips[0]?.id);
                  }}
                  // onMouseEnter={() => (item.hover = true)}
                  // onMouseLeave={() => (item.hover = false)}
                  sx={{
                    width: '100%',
                    height: '100%',
                    background: '',
                    borderRadius: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'start',
                    textTransform: 'none',
                    color: 'black',
                    bgcolor: 'white',
                    position: 'relative',
                    [theme.breakpoints.down('md')]: {
                      // justifyContent: 'center',
                      // alignItems: 'center',
                      mx: 'auto',
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: '100%',
                    }}
                  >
                    <img
                      src={tips[0]?.imageUrl}
                      alt=""
                      width={'100%'}
                      height={'auto'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      width: '100%',
                      textAlign: 'left',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      p: 1,
                    }}
                  >
                    {tips[0]?.name}
                  </Typography>
                  <Typography
                    sx={{
                      width: '100%',
                      color: 'gray',
                      // fontStyle: 'italic',
                      textAlign: 'left',
                      fontSize: '12px',
                      px: 1,
                    }}
                  >
                    {tips[0]?.description}
                  </Typography>
                  <Typography
                    sx={{
                      width: '100%',
                      color: 'gray',
                      fontStyle: 'italic',
                      textAlign: 'left',
                      fontSize: '12px',
                      px: 1,
                    }}
                  >
                    {convertMongoCreatedAt(tips[0]?.createdAt)}
                  </Typography>
                </Card>
              </Box>
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                }}
              >
                {tips?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 0 && index < 5 ? 'flex' : 'none',
                      width: '100%',
                      height: '100px',
                      cursor: 'pointer',
                      overflow: 'hidden',
                    }}
                    onClick={() => {
                      onClickRef(index);
                    }}
                  >
                    <Box
                      sx={{
                        width: '150px',
                        height: '100%',
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'150px'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Paper>
            <Paper
              sx={{
                flex: 1,
                height: '440px',
                display: 'flex',
                flexDirection: 'column',
                my: 1,
                overflow: 'hidden',
                borderRadius: 1,
                [theme.breakpoints.down('md')]: {
                  display: 'none',
                },
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  width: '100%',
                  bgcolor: '',
                  p: 2,
                  fontWeight: 'bold',
                  borderBottom: '0.5px #eeebeb solid',
                }}
              >
                Xem nhiều
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  p: 2,
                  overflowY: 'auto',
                }}
              >
                {tips?.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: index < 5 ? 'flex' : 'none',
                      alignItems: 'center',
                      width: '100%',
                      height: '80px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                    }}
                    onClick={() => onClickRef(item.id)}
                  >
                    <Typography
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '40px',
                        height: '40px',
                        p: 2,
                        borderRadius: '100%',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                      }}
                    >
                      {index + 1}
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Box>
          <Paper
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Box
              sx={{
                flex: 3,
                height: 'auto',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                }}
              >
                {tips?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: index > 4 ? 'flex' : 'none',
                      width: '100%',
                      height: '200px',
                      cursor: 'pointer',
                      gap: 2,
                      overflow: 'hidden',
                      [theme.breakpoints.down('md')]: {
                        flexDirection: 'column',
                        height: 'auto',
                        gap: 1,
                      },
                    }}
                  >
                    <Box
                      sx={{
                        width: '300px',
                        height: '100%',
                        flexShrink: 0,
                        [theme.breakpoints.down('md')]: {
                          width: '100%',
                          height: 'auto',
                          gap: 1,
                        },
                      }}
                    >
                      <img
                        src={item.imageUrl}
                        alt=""
                        width={'100%'}
                        height={'100%'}
                        loading="lazy"
                      />
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'green',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.label === 'tips'
                          ? 'Thủ thuật'
                          : item.label === 'promotion'
                          ? 'Khuyến mãi'
                          : item.label === 'video'
                          ? 'Video'
                          : ''}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                        }}
                      >
                        {item.name}
                      </Typography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          mt: 'auto',
                        }}
                      >
                        {convertMongoCreatedAt(item.createdAt)}
                      </Typography>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Box>
          </Paper>
          {/* <Card
            sx={{
              display: 'flex',
              m: 3,
              // mr: 'auto',
            }}
          >
            <YouTube
              videoId={videos[0]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
          </Card> */}
        </Box>
        <Box
          sx={{
            display: valueTab === 'video' ? 'flex' : 'none',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            height: '100%',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: 'auto',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                  display: 'none',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 2,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  cursor: 'pointer',
                }}
              >
                <Card
                  sx={{
                    display: 'flex',
                    m: 3,
                    [theme.breakpoints.down('sm')]: {
                      flexDirection: 'column',
                      height: 'auto',
                      gap: 1,
                    },
                    // mr: 'auto',
                  }}
                >
                  <YouTube
                    videoId={videoLead.idUrl}
                    opts={opts3}
                    onReady={onPlayerReady}
                  />
                </Card>
              </Box>
            </Paper>
            <Paper
              sx={{
                flex: 1,
                height: '540px',
                display: 'flex',
                flexDirection: 'column',
                my: 1,
                overflow: 'hidden',
                borderRadius: 1,
                [theme.breakpoints.down('md')]: {
                  display: 'none',
                },
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  width: '100%',
                  bgcolor: '',
                  p: 2,
                  fontWeight: 'bold',
                  borderBottom: '0.5px #eeebeb solid',
                }}
              >
                Xem nhiều
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  p: 2,
                  overflowY: 'auto',
                }}
              >
                {videos?.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: index < 5 ? 'flex' : 'none',
                      alignItems: 'center',
                      width: '100%',
                      height: '80px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                    }}
                    onClick={() => onClickRef(item.id)}
                  >
                    <Typography
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '40px',
                        height: '40px',
                        p: 2,
                        borderRadius: '100%',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                      }}
                    >
                      {index + 1}
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <TruncatedTypography>{item.name}</TruncatedTypography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.date}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Box>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              height: '100%',
              gap: 1,
            }}
          >
            <Paper
              sx={{
                flex: 3,
                height: 'auto',
                display: 'flex',
                flexDirection: 'row',
                [theme.breakpoints.down('md')]: {
                  flexDirection: 'column',
                  height: 'auto',
                },
                my: 1,
                p: 2,
                bgcolor: '',
                borderRadius: 1,
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.1), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  flex: 1.5,
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  bgcolor: '',
                  pl: 2,
                  gap: 2,
                  borderRadius: 1,
                  [theme.breakpoints.down('md')]: {
                    pl: 0,
                  },
                }}
              >
                {videos?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: 'flex',
                      width: '100%',
                      height: '200px',
                      cursor: 'pointer',
                      gap: 2,
                      overflow: 'hidden',
                      position: 'relative',
                      [theme.breakpoints.down('md')]: {
                        flexDirection: 'column',
                        height: 'auto',
                        gap: 1,
                      },
                    }}
                    onClick={() => setVideoLead(item)}
                  >
                    <YouTube
                      videoId={item.idUrl}
                      opts={opts4}
                      onReady={onPlayerReady}
                    />
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        p: 1,
                      }}
                    >
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'green',
                          fontStyle: 'italic',
                          fontSize: '12px',
                        }}
                      >
                        {item.label === 'tips'
                          ? 'Thủ thuật'
                          : item.label === 'promotion'
                          ? 'Khuyến mãi'
                          : item.label === 'video'
                          ? 'Video'
                          : ''}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                        }}
                      >
                        {item.name}
                      </Typography>
                      <Typography
                        sx={{
                          width: '100%',
                          color: 'gray',
                          fontStyle: 'italic',
                          fontSize: '12px',
                          mt: 'auto',
                        }}
                      >
                        {convertMongoCreatedAt(item.createdAt)}
                      </Typography>
                      <IconButton
                        color="warning"
                        sx={{
                          display:
                            userInfo?.role === 'ADMIN' ||
                            userInfo?.role === 'COLLAB'
                              ? ''
                              : 'none',
                          width: 'fit-content',
                          position: 'absolute',
                          right: 2,
                        }}
                        onClick={() => handleDeleteNewsId(item._id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Paper>
          </Box>
          {/* <Card
            sx={{
              display: 'flex',
              m: 3,
              // mr: 'auto',
            }}
          >
            <YouTube
              videoId={videos[0]?.idUrl}
              opts={opts2}
              onReady={onPlayerReady}
            />
          </Card> */}
        </Box>
        {/* <Recently /> */}
        <Box
          sx={{
            width: 'fit-content',
            display: 'flex',
            flexDirection: 'column',
            position: 'fixed',
            // bgcolor: 'green',
            mx: 'auto',
            bottom: '10px',
            p: 5,
            [theme.breakpoints.down('md')]: {
              p: 2,
            },
          }}
        >
          <Box
            sx={{
              display:
                userInfo?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                  ? 'flex'
                  : 'none',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <IconButton
              sx={{
                width: '40px',
                height: '40px',
                bgcolor: '#85d34e',
                animation: `${upUp} 1s ease-in-out infinite alternate`,
              }}
              onClick={() => {
                setOpenNew(true);
              }}
            >
              {/* <img src={zalo} alt="" width={'100%'} height={'100%'} /> */}
              <AddIcon
                sx={{
                  fontSize: '25px',
                  color: 'orangered',
                }}
              />
            </IconButton>
          </Box>
        </Box>
        <MuiDialog
          disableEnforceFocus
          fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
          fullWidth={true}
          maxWidth="sm"
          open={openNew}
          onClose={onClose}
          PaperProps={{
            sx: {
              borderRadius: 2,
              backgroundColor: '#efeff1',
              margin: 0,
              height: '60vh',
              [theme.breakpoints.down('sm')]: {
                height: '100vh',
              },
              overflow: 'hidden',
            },
          }}
        >
          <DialogContent
            sx={{
              padding: 1,
              pt: 3,
              overflow: 'hidden',
            }}
          >
            <IconButton
              className="rotateZ"
              sx={{
                p: 1,
                position: 'absolute',
                right: '32px',
                top: '20px',
                color: 'white',
                backgroundColor: '#63636735',
              }}
              aria-label="toggle password visibility"
              onClick={onClose}
              onMouseDown={() => {}}
              edge="end"
            >
              <CloseIcon fontSize="small" />
            </IconButton>
            <Typography
              sx={{
                width: '100%',
                textAlign: 'center',
                color: 'orangered',
                mx: 'auto',
              }}
            >
              Thêm bài viết mới
            </Typography>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                py: 2,
                overflow: 'auto',
              }}
            >
              <TextField
                size="small"
                label="Tiêu đề"
                value={name}
                onChange={e => {
                  setName(e.target.value);
                  setNameId(vietnameseToAscii(e.target.value));
                }}
              />
              <TextField
                id="standard-select-currency"
                select
                label="Nhãn"
                helperText="Vui lòng chọn nhãn"
                variant="standard"
                onChange={e => setLabel(e.target.value)}
              >
                {labelOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
              <TextField
                size="small"
                label="ID Youtube"
                value={idUrl}
                onChange={e => setIdUrl(e.target.value)}
                sx={{
                  display: label === 'video' ? 'flex' : 'none',
                }}
              />
              <Box>
                <input
                  id="lead-banner"
                  name="file"
                  type="file"
                  onChange={event => {
                    const image = {
                      name: '',
                      file: undefined,
                      url: '',
                    };

                    image.file = get(event, 'currentTarget.files[0]');
                    if (image.file) {
                      const reader = new FileReader();
                      reader.readAsDataURL(image.file);
                      reader.onload = async () => {
                        set(image, 'url', reader.result);
                        set(image, 'name', get(image, 'file.name', ''));
                        // const uploadInfo = await s3Client.uploadFile(
                        //   `phuonglinh/productImage/${Date.now()}-${image.name}`,
                        //   image.file,
                        // );
                        // image.url = get(uploadInfo, 'Location');
                        image.url = await uploadFiles(
                          [image.file],
                          'product',
                          '',
                        );
                        // const upload = UploadImg(image);
                        // console.log('upload: ', uploadInfo);
                        setImageUrl(image.url);
                      };
                    }
                    event.preventDefault();
                  }}
                  className="form-control"
                />
              </Box>
              <Box>
                <img
                  src={imageUrl}
                  alt="banner"
                  width={'100%'}
                  height={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  width: 'fit-content',
                  display: 'flex',
                  gap: 2,
                  p: 1,
                  ml: 'auto',
                }}
              >
                <Button variant="contained" onClick={() => handleCreateNews()}>
                  OK
                </Button>
                <Button variant="contained" color="warning" onClick={onClose}>
                  Huỷ
                </Button>
              </Box>
            </Box>
          </DialogContent>
        </MuiDialog>
        {AddDetailDialog()}
      </Paper>
    </>
  );
}
