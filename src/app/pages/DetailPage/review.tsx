import { <PERSON>, Button, Paper, Typography } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import ImageListItem from '@mui/material/ImageListItem';
import StarIcon from '@mui/icons-material/Star';

interface Props {
  style?: React.CSSProperties;
}

const Review: React.FC<Props> = ({ style = {} }) => {
  const [display, setDisplay] = React.useState<number>(24);
  const [rateNumber, setRateNumber] = React.useState<number>(5);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        width: '95%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        justifyContent: 'start',
        p: 1,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          p: 5,
          gap: 1,
          borderRadius: '10px',
          boxShadow:
            '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
        }}
      >
        <Typography variant="h3" fontWeight={'bold'}>
          {rateNumber.toFixed(1)}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <StarIcon
            fontSize="small"
            sx={{
              color: 'gold',
            }}
          />
          <StarIcon
            fontSize="small"
            sx={{
              color: 'gold',
            }}
          />
          <StarIcon
            fontSize="small"
            sx={{
              color: 'gold',
            }}
          />
          <StarIcon
            fontSize="small"
            sx={{
              color: 'gold',
            }}
          />
          <StarIcon
            fontSize="small"
            sx={{
              color: 'gold',
            }}
          />
        </Box>
        <Typography>{'(9 review)'}</Typography>
        <Button
          sx={{
            width: '100%',
            textTransform: 'none',
            bgcolor: 'gold',
            color: 'black',
          }}
        >
          Đánh giá
        </Button>
      </Box>
    </Box>
  );
};

export default Review;
