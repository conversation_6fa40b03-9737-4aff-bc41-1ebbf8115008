import {
  Box,
  Button,
  IconButton,
  Paper,
  <PERSON>Field,
  <PERSON>po<PERSON>,
  Card,
} from '@mui/material';

import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import ImageListItem from '@mui/material/ImageListItem';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import NewspaperIcon from '@mui/icons-material/Newspaper';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import QueueIcon from '@mui/icons-material/Queue';
import CreateIcon from '@mui/icons-material/Create';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import { useMediaQuery } from '@mui/material';
import SlideProductsList from './SlideProductsList';

import pic1 from 'assets/images/product/new/new_01.webp';
import ZoomImage from 'app/components/ZoomImg/ZoomImage';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import get from 'lodash/get';
import { request } from 'utils/request';
import { useDispatch, useSelector } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';
import JoditEditor from 'jodit-react';
import YouTube, { YouTubeProps } from 'react-youtube';
import {
  editorConfigJodit,
  createMarkup,
  updateFiguresForImages,
} from 'utils/jodit';
import { isNil, omitBy, set } from 'lodash';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { keyframes } from '@emotion/react';

const upUp = keyframes`
  0% {
    transform: translateY(0px)
  }
  100% {
    transform: translateY(-10px)
  }
`;

const formatCurrency = (number: number) => {
  return number?.toLocaleString('vi-VN', {
    style: 'currency',
    currency: 'VND',
  });
};

const initVideoList = [
  {
    title: 'video-1',
    url: 'https://www.youtube.com/',
    videoThumbnail: pic1,
  },
  {
    title: 'video-2',
    url: 'https://www.youtube.com/',
    videoThumbnail: pic1,
  },
  {
    title: 'video-3',
    url: 'https://www.youtube.com/',
    videoThumbnail: pic1,
  },
];

interface ImageObject {
  url: string;
  _id: string;
  [key: string]: any; // Cho phép các thuộc tính khác
}

export function DetailPage() {
  const params = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { path } = params;
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [data, setData] = React.useState<string>('');
  const [sameCategory, setSameCategory] = React.useState<any[]>([]);
  const [detailHeight, setDetailHeight] = React.useState<string>('1000px');
  const [imageHeight, setImageHeight] = React.useState<number>(0);
  const [image, setImage] = React.useState<any>('');
  const [quantity, setQuantity] = React.useState<number>(1);
  const [model, setModel] = React.useState<string>('');
  const [color, setColor] = React.useState<string>('');
  const [models, setModels] = React.useState<any[]>([]);
  const [colors, setColors] = React.useState<any[]>([]);
  const [size, setSize] = React.useState<string>('');
  const [price, setPrice] = React.useState<number>(0);
  const [product, setProduct] = React.useState<any>({});
  const [addSpecification, setAddSpecification] = React.useState<any>({});
  const [addNews, setAddNews] = React.useState<any>({});
  const [addVideos, setAddVideos] = React.useState<any>({});
  const [variant, setVariant] = React.useState<any>({});

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);
  const [user, setUser] = React.useState<any>({
    email: '',
    emailVerification: false,
    error: '',
    loading: '',
    phone: '',
    phoneVerification: false,
    role: '',
    token: '',
    userId: '',
    username: '',
  });

  const [isAddSpecifications, setIsAddSpecifications] =
    React.useState<boolean>(false);
  const [isEditDescription, setIsEditDecripstion] =
    React.useState<boolean>(false);
  const [isAddCart, setIsAddCart] = React.useState<boolean>(false);
  const [isAddDetail, setIsAddDetail] = React.useState<boolean>(false);
  const [isAddNews, setIsAddNews] = React.useState<boolean>(false);
  const [isAddVideo, setIsAddVideo] = React.useState<boolean>(false);
  const [isLoadingRef, setIsLoadingRef] = React.useState<boolean>(false);
  const [rerenderKey, setRerenderKey] = React.useState(0);

  const imageRef = React.useRef<any>();
  const { width } = useWindowDimensions();

  useEffect(() => {
    if (imageRef.current) {
      setImageHeight(imageRef.current.offsetWidth);
    }
  }, [width]);

  const handleRerenderDialog = () => {
    setRerenderKey(prevKey => prevKey + 1);
  };

  // TODO: get list specification from api
  const [specifications, setSpecification] = React.useState<any[]>([]);

  // TODO: get list video from api
  const [videos, setVideos] = React.useState<any[]>(initVideoList);

  // TODO: get list news from api
  const [news, setNews] = React.useState<any[]>([]);

  // TODO: get list detail from api
  const [description, setDescription] = React.useState<string>('');
  const [id, setId] = React.useState<string>('');
  const [canonical, setCanonical] = React.useState<string>('');
  const [descriptionSEO, setDescriptionSEO] = React.useState<string>('');
  const [mainKeyword, setMainKeyword] = React.useState<string>('');
  const [keyword, setKeyword] = React.useState<string>('');
  const [listImages, setListImages] = React.useState<any[]>([]);
  const [newsLeads, setNewsLeads] = React.useState<any[]>([]);

  const handleEditProduct = async () => {
    const finalData = updateFiguresForImages(data);
    setData(finalData);
    const productData = {
      id,
      description,
      detail: finalData,
      specifications,
      videos,
      news,
      canonical,
      descriptionSEO,
      mainKeyword,
      keyword,
    };
    const method = 'put';
    const url = `${process.env.REACT_APP_BACKEND_URL}/api/v1/products/${product._id}`;
    try {
      const result = await request(method, url, productData);
      setIsAddDetail(false);
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: 'saved',
      //     variant: 'success',
      //   }),
      // );
    } catch (error) {
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: get(error, 'message', 'Error Undefined'),
      //     variant: 'error',
      //   }),
      // );
    }
  };

  const handleGetPriceByVariant = (color, size, model) => {
    product.variants.map(item => {
      if (
        (!item.color ? true : item.color === color ? true : false) &&
        (!item.size ? true : item.size === size ? true : false) &&
        (!item.model ? true : item.model === model ? true : false)
      ) {
        setPrice(item.price);
        // setIsAddCart(true);
      }
    });
  };

  const filterUniqueImages = (
    imageArray: ImageObject[],
    property: 'url' | '_id' = 'url',
  ): ImageObject[] => {
    if (!Array.isArray(imageArray)) {
      throw new Error('Tham số phải là một mảng');
    }

    const uniqueMap = new Map<string, ImageObject>();

    // Lọc các phần tử và giữ lại phần tử đầu tiên của mỗi giá trị property
    imageArray.forEach(image => {
      const key = image[property];
      if (key && !uniqueMap.has(key)) {
        uniqueMap.set(key, image);
      }
    });

    // Chuyển Map values thành mảng
    return Array.from(uniqueMap.values());
  };

  const getArrayIntersection = (
    array1: ImageObject[],
    array2: ImageObject[],
    property = 'url',
  ): ImageObject[] => {
    if (!Array.isArray(array1) || !Array.isArray(array2)) {
      throw new Error('Cả hai tham số đều phải là mảng');
    }

    // Tạo một Set chứa các giá trị property từ array2 để tìm kiếm nhanh
    const propertySet = new Set<string>();

    // Thêm tất cả giá trị property từ array2 vào Set
    array2.forEach(item => {
      const value = item[property];
      if (value) {
        propertySet.add(value);
      }
    });

    // Lọc array1 để chỉ giữ lại các phần tử có property nằm trong propertySet
    return array1.filter(item => {
      const value = item[property];
      return value && propertySet.has(value);
    });
  };

  const onPlayerReady: YouTubeProps['onReady'] = event => {
    // access to player in all event handlers via event.target
    event.target.pauseVideo();
  };
  const opts1: YouTubeProps['opts'] = {
    height: '150',
    width: '300',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };

  React.useEffect(() => {
    // Fetch API get Product detail;
    path && fetchProductDetail(path);
    const product: any = get(location, 'state.product');
    if (product) {
      setProduct(product);
      setImage(get(product, 'images[0].url', ''));
      setSpecification(get(product, 'specification', []));
      const filteredModels = product.variants?.filter(item => item.model);
      const filteredColors = product.variants?.filter(item => item.color);
      setModels(filteredModels);
      setColors(filteredColors);
      console.log('filteredModels: ', filteredModels);
      console.log('filteredColors: ', filteredColors);
      console.log('product: ', product);
    }
  }, [path]);

  React.useEffect(() => {
    // Fetch API get same News detail;
    if (product.keyword) fetchNewsPage();
  }, [product]);

  React.useEffect(() => {
    const dataColor = colors?.find(item => item.color === color);
    const dataModel = models?.find(item => item.model === model);

    if (color && !model) {
      setListImages(dataColor.images);
    } else if (model && !color) {
      setListImages(dataModel.images);
    } else if (color && model) {
      const dataIntersection = getArrayIntersection(
        dataColor.images,
        dataModel.images,
      );
      setListImages(dataIntersection);
      // console.log('dataIntersection: ', dataIntersection);
    } else {
      setListImages(product.images);
    }
  }, [color, model]);

  // const fetchDataSame = async (searchText: string) => {
  //   // Gọi API tìm kiếm dựa trên searchText
  //   const apiFetch = async () => {
  //     try {
  //       const data: any = await request(
  //         'get',
  //         `${process.env.REACT_APP_BACKEND_URL}/api/v1/products?keyword=${searchText}`,
  //       );
  //       setSameCategory(data);
  //       // console.log('dataSearch: ', data);
  //     } catch (error) {}
  //   };
  //   apiFetch();
  // };

  const fetchDataSame = categoryId => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/products`,
          path ? omitBy({ categoryId }, isNil) : '',
        );

        setSameCategory(result);
        // console.log('productsSame: ', result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  const filterObjects = (arr, key, value) => {
    return arr.filter(item => item[key] === value);
  };

  const fetchNewsPage = async () => {
    const extractKeywords = (productName: string): string[] => {
      let words = productName.split(' ');
      const keywords: string[] = [];
      let newWords: any = [];
      function isNumeric(value) {
        return !isNaN(value) && !isNaN(parseFloat(value));
      }
      if (isNumeric(words[words.length - 1])) {
        newWords = words.pop();
      }
      // if (words.length) keywords.push(words[words.length - 1]);

      for (let i = words.length; i > 0; i--) {
        keywords.push(words.slice(i - 1, words.length).join(' '));
      }
      // console.log('keywords: ', keywords)
      return keywords;
    };

    const apiHandler = async (keyword: string) => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news?keyword=${keyword}`,
        );

        // console.log({ result });
        // setNews(result);
        // const newList = filterObjects(result, 'label', 'promotion' || 'tips');
        // setNews(newList);
        // console.log('newList: ', newList)
        // const newTips = filterObjects(result, 'label', 'tips');
        // setTips(newTips);
        // const newVideos = filterObjects(result, 'label', 'video');
        // setVideos(newVideos);
        // setVideoLead(newVideos[0]);
        const newNoneVideos = () => {
          return result.filter(
            item => item.label === 'tips' || item.label === 'promotion',
          );
        };
        const newList = newNoneVideos();
        console.log('newList: ', newList);
        setNews(newList);
        // console.log('setNews: ', newList)
        // console.log('noneVideo: ', newNoneVideos);
        return result;
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    const keywords = extractKeywords(product.mainKeyword);

    for (const keyword of keywords) {
      const result = await apiHandler(keyword);
      if (result.length > 0) {
        return;
      }
    }
  };

  const fetchProductDetail = (productId: string) => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/products/${productId}`,
        );

        setProduct(result);
        setPrice(get(result, 'price', 0));
        setSpecification(get(result, 'specifications', []));
        setVideos(get(result, 'videos', []));
        // setNews(get(result, 'news', []));
        setId(get(result, 'id', ''));
        setCanonical(get(result, 'canonical', ''));
        setDescriptionSEO(get(result, 'descriptionSEO', ''));
        setMainKeyword(get(result, 'mainKeyword', ''));
        setKeyword(get(result, 'keyword', ''));
        const detail = get(result, 'detail', '');
        detail && setData(detail || data);
        setDescription(result?.description);
        setImage(result.images[0].url);
        setListImages(result.images);
        fetchDataSame(result?.collections?.[1]?.id);
        // console.log('product: ', result);
      } catch (error) {
        // return dispatch(
        //   utilsAction.showSnackbar({
        //     message: get(error, 'message', 'Error Undefined'),
        //     variant: 'error',
        //   }),
        // );
      }
    };

    apiHandler();
  };

  const lastLineRef = React.useRef<HTMLParagraphElement | null>(null);

  const AddDetailDialog = () => {
    useEffect(() => {
      if (lastLineRef.current) {
        lastLineRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        });
        lastLineRef.current.focus();
      }
    }, []);
    return (
      <MuiDialog
        key={rerenderKey}
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={isAddDetail}
        onClose={() => setIsAddDetail(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignContent: 'center',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                fontWeight: 'bold',
              }}
            >
              Chỉnh sửa nội dung giới thiệu sản phẩm
            </Typography>
            <Box>
              <IconButton color="warning" onClick={handleEditProduct}>
                <SaveAsIcon />
              </IconButton>
              <IconButton color="warning" onClick={() => setIsAddDetail(false)}>
                <HighlightOffIcon />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              pb: 5,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <TextField
              size="small"
              label="Customer URL"
              value={id}
              sx={{
                mt: 2,
              }}
              onChange={e => {
                setId(e.target.value);
              }}
            />
            <TextField
              size="small"
              label="Link canonical"
              value={canonical}
              onChange={e => {
                setCanonical(e.target.value);
              }}
            />
            <TextField
              size="small"
              label="mô tả"
              value={descriptionSEO}
              onChange={e => {
                setDescriptionSEO(e.target.value);
              }}
            />
            <TextField
              size="small"
              label="main-keyword"
              value={mainKeyword}
              onChange={e => {
                setMainKeyword(e.target.value);
              }}
            />
            <TextField
              size="small"
              label="second-keyword"
              value={keyword}
              onChange={e => {
                setKeyword(e.target.value);
              }}
            />
            <JoditEditor
              value={data}
              config={editorConfigJodit}
              onChange={value => {
                // console.log('data: ', value);
                setData(value);
              }}
            />
          </Box>
        </DialogContent>
      </MuiDialog>
    );
  };

  useEffect(() => {
    setUser(userInfo);
  }, [userInfo]);
  // useEffect(() => {
  //   console.log('dataDetail: ', data);
  // }, [data]);
  return (
    <>
      <Helmet>
        <title>{product?.name || 'Thông tin sản phẩm'}</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          px: '100px',
          [theme.breakpoints.down('lg')]: {
            px: '0px',
          },
        }}
      >
        <Box
          sx={{
            width: '100%',
            overflow: 'auto',
            display: 'flex',
            justifyContent: 'start',
          }}
        >
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'center',
              gap: 1,
              p: 1,
              px: 4,
              top: 0,
              fontSize: '12px',
              textTransform: 'capitalize',
              textWrap: 'nowrap',
              boxShadow:
                '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
              overflow: 'auto',
            }}
          >
            <Typography
              sx={{
                fontSize: '12px',
                fontFamily: 'inherit',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                fontWeight: 'bold',
                '&:hover': {
                  color: 'cyan',
                },
              }}
              onClick={() => {
                navigate('/');
              }}
            >
              Trang chủ
            </Typography>
            ›
            <Typography
              sx={{
                fontSize: '12px',
                fontFamily: 'inherit',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                fontWeight: 'bold',
                '&:hover': {
                  color: 'cyan',
                },
              }}
              onClick={() => {
                navigate('/bo-suu-tap');
              }}
            >
              Sản phẩm
            </Typography>
            ›
            <Typography
              sx={{
                fontSize: '12px',
                fontFamily: 'inherit',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                fontWeight: 'bold',
                '&:hover': {
                  color: 'cyan',
                },
              }}
              onClick={() => {
                navigate(`/bo-suu-tap/${product?.brands?.[0].id}`);
              }}
            >
              {product?.brands?.[0]?.name}
            </Typography>
            ›
            <Typography
              sx={{
                fontSize: '12px',
                fontFamily: 'inherit',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                fontWeight: 'bold',
                '&:hover': {
                  color: 'cyan',
                },
              }}
              // onClick={() => {
              //   navigate('/');
              // }}
            >
              {product?.name}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            background: 'white',
            gap: 1,
            p: 1,
            px: 4,
            top: 0,
            zIndex: 100,
            fontSize: '12px',
            position: 'fixed',
            textTransform: 'capitalize',
            boxShadow:
              '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
          }}
        >
          <Typography
            sx={{
              fontSize: '12px',
              fontFamily: 'inherit',
              whiteSpace: 'nowrap',
              cursor: 'pointer',
              '&:hover': {
                color: 'gold',
              },
            }}
            onClick={() => {
              navigate('/');
            }}
          >
            Trang chủ
          </Typography>
          ›
          <Typography
            sx={{
              fontSize: '12px',
              fontFamily: 'inherit',
              whiteSpace: 'nowrap',
              cursor: 'pointer',
              '&:hover': {
                color: 'gold',
              },
            }}
            onClick={() => {
              navigate('/bo-suu-tap');
            }}
          >
            Sản phẩm
          </Typography>
          ›
          <Typography
            sx={{
              fontSize: '12px',
              fontFamily: 'inherit',
              whiteSpace: 'nowrap',
              cursor: 'pointer',
              '&:hover': {
                color: 'gold',
              },
            }}
            onClick={() => {
              navigate(`/bo-suu-tap/${product?.brands?.[0].id}`);
            }}
          >
            {product?.brands?.[0]?.name}
          </Typography>
          ›
          <Typography
            sx={{
              fontSize: '12px',
              fontFamily: 'inherit',
              whiteSpace: 'nowrap',
              cursor: 'pointer',
              '&:hover': {
                color: 'gold',
              },
            }}
            // onClick={() => {
            //   navigate('/');
            // }}
          >
            {product?.name}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            bgcolor: '',
            gap: 1,
            [theme.breakpoints.down('lg')]: {
              flexDirection: 'column',
            },
          }}
        >
          <Box
            sx={{
              flex: 6,
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              bgcolor: '',
              gap: 1,
            }}
          >
            <Box
              sx={{
                flex: 9,
                display: 'flex',
                justifyContent: 'space-between',
                height: '100%',
                p: 4,
                [theme.breakpoints.down('lg')]: {
                  flexDirection: 'column-reverse',
                },
              }}
            >
              <Box
                className="scrollbar-hidden"
                sx={{
                  // bgcolor: 'orange',
                  display: 'flex',
                  // width: '100%',
                  height: '100%',
                  maxHeight: '500px',
                  flex: 1,
                  gap: 2,
                  overflowX: 'auto',
                  [theme.breakpoints.up('lg')]: {
                    flexDirection: 'column',
                    overflowY: 'auto',
                  },
                }}
              >
                {listImages?.map(item => (
                  <Button
                    key={item.url}
                    sx={{
                      flexShrink: 0,
                      display: 'flex',
                      width: '100px',
                      height: '100px',
                      p: 0,
                      border: image === item.url ? '0.5px solid black' : '',
                      borderRadius: '10px',
                      overflow: 'hidden',
                    }}
                    onMouseEnter={e => {
                      setImage(item.url);
                    }}
                  >
                    <ImageListItem>
                      <img src={item.url} alt={product?.name} loading="lazy" />
                    </ImageListItem>
                  </Button>
                ))}
              </Box>
              <Box
                ref={imageRef}
                sx={{
                  flexShrink: 0,
                  width: '70%',
                  height: imageHeight || '90%',
                  display: 'flex',
                  justifyContent: 'center',
                  mx: 'auto',
                  flex: 3,
                  pl: 1,
                  [theme.breakpoints.down('lg')]: {
                    maxWidth: '500px',
                    minWidth: '300px',
                    minHeight: '400px',
                    width: '100%',
                    // height: '75%',
                    pb: 1,
                  },
                  // bgcolor: 'violet',
                }}
              >
                <ZoomImage src={image} alt="" zoomFactor={1.5} />
              </Box>
            </Box>
            <Typography
              sx={{
                flex: 1,
                fontWeight: 'bold',
                p: 1,
              }}
            >
              Thông tin chi tiết về sản phẩm
            </Typography>
          </Box>
          <Box
            sx={{
              // bgcolor: 'red',
              flex: 4,
              display: 'flex',
              // width: '50%',
              mb: 2,
              flexDirection: 'column',
              gap: 1,
              [theme.breakpoints.down('md')]: {
                px: 3,
                width: '100%',
              },
            }}
          >
            <Typography
              sx={{
                fontSize: '24px',
                fontWeight: 'bold',
                fontFamily: 'inherit',
              }}
            >
              {product.name}
            </Typography>
            {/* <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <StarIcon
                fontSize="small"
                sx={{
                  color: 'gold',
                }}
              />
              <StarIcon
                fontSize="small"
                sx={{
                  color: 'gold',
                }}
              />
              <StarIcon
                fontSize="small"
                sx={{
                  color: 'gold',
                }}
              />
              <StarIcon
                fontSize="small"
                sx={{
                  color: 'gold',
                }}
              />
              <StarIcon
                fontSize="small"
                sx={{
                  color: 'gold',
                }}
              />
              <Typography>{'(9 review)'}</Typography>
            </Box> */}
            {/* <Typography
              sx={{
                display:
                  product.priceOrigin === product.price ? 'none' : 'flex',
                color: 'white',
                bgcolor: 'red',
                borderRadius: '2px',
                mr: 'auto',
                px: 1,
                fontSize: '',
                fontWeight: '',
                // position: 'absolute',
                // translate: '10px 0px',
              }}
            >
              - {formatCurrency(product.priceOrigin - product.price)}
            </Typography> */}
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                bgcolor: 'white',
                p: 3,
                borderRadius: 1,
                border: '0.5px solid #ece9e9',
                position: 'relative',
                // boxShadow:
                // '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  width: '100%',
                  display: isEditDescription ? 'flex' : 'none',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  bgcolor: 'white',
                }}
              >
                <TextField
                  id="standard-multiline-static"
                  label="Mô tả ngắn"
                  multiline
                  rows={4}
                  value={description}
                  variant="standard"
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    setDescription(event.target.value);
                  }}
                  sx={{
                    width: '100%',
                  }}
                />
              </Box>
              <IconButton
                sx={{
                  display:
                    userInfo?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                      ? ''
                      : 'none',
                  position: 'absolute',
                  top: 1,
                  right: 1,
                }}
                onClick={() => {
                  if (isEditDescription) {
                    handleEditProduct();
                    setIsEditDecripstion(false);
                  } else {
                    setIsEditDecripstion(true);
                  }
                }}
              >
                {isEditDescription ? <SaveAsIcon /> : <CreateIcon />}
              </IconButton>
              <Typography
                variant="h6"
                sx={{
                  width: '100%',
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                }}
              >
                Mô tả ngắn:
              </Typography>
              <Typography
                sx={{
                  display: !isEditDescription ? '' : 'none',
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'inherit',
                }}
              >
                {description}
              </Typography>
            </Box>
            <Box>
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                }}
              >
                Màu sắc:{' '}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                }}
              >
                {colors?.map((item, index) => (
                  <Button
                    key={index}
                    sx={{
                      px: 2,
                      borderRadius: 1,
                      color: 'black',
                      border: '0.5px solid red',
                      display: item.color ? 'block' : 'none',
                    }}
                    onClick={() => {
                      if (item.color === color) {
                        setColor('');
                      } else {
                        setColor(item.color);
                      }
                      handleGetPriceByVariant(item.color, size, model);
                    }}
                  >
                    <CheckCircleIcon
                      fontSize="small"
                      color="success"
                      sx={{
                        display: color === item.color ? 'flex' : 'none',
                        position: 'absolute',
                        top: 0,
                        right: 0,
                      }}
                    />
                    <Box
                      sx={{
                        width: '30px',
                        height: '30px',
                        background: `url("${item.images?.[0]?.url}")`,
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                        borderRadius: 2,
                      }}
                    ></Box>
                    <Typography
                      sx={{
                        px: 1,
                        fontSize: '10px',
                        fontFamily: 'inherit',
                      }}
                    >
                      {item.color}
                    </Typography>
                  </Button>
                ))}
              </Box>
            </Box>
            {/* <Box
              sx={{
                display: product?.variants?.find(item => item.size)
                  ? ''
                  : 'none',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                }}
              >
                Size:{' '}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                }}
              >
                {product?.variants?.map((item, index) => (
                  <Button
                    key={index}
                    sx={{
                      px: 2,
                      borderRadius: 1,
                      color: 'black',
                      border: '0.5px solid red',
                      display: item.size ? 'block' : 'none',
                    }}
                    onClick={() => {
                      setSize(item.size);
                      setListImages(item.images);
                      handleGetPriceByVariant(color, item.size, model);
                    }}
                  >
                    <CheckCircleIcon
                      fontSize="small"
                      color="success"
                      sx={{
                        display: size === item.size ? 'flex' : 'none',
                        position: 'absolute',
                        top: 0,
                        right: 0,
                      }}
                    />
                    <Box
                      sx={{
                        width: '30px',
                        height: '30px',
                        background: `url("${item.images?.[0]?.url}")`,
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                        borderRadius: 2,
                      }}
                    ></Box>
                    <Typography
                      sx={{
                        px: 1,
                        fontSize: '10px',
                        fontFamily: 'inherit',
                      }}
                    >
                      {item.size}
                    </Typography>
                  </Button>
                ))}
              </Box>
            </Box> */}
            <Box
              sx={{
                display: product?.variants?.find(item => item.model)
                  ? ''
                  : 'none',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                }}
              >
                Model:{' '}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  gap: 1,
                }}
              >
                {models?.map((item, index) => (
                  <Button
                    key={index}
                    sx={{
                      px: 2,
                      borderRadius: 1,
                      color: 'black',
                      border: '0.5px solid red',
                      display: item.model ? 'block' : 'none',
                    }}
                    onClick={() => {
                      if (item.model === model) {
                        setModel('');
                      } else {
                        setModel(item.model);
                      }
                      handleGetPriceByVariant(color, size, item.model);
                    }}
                  >
                    <CheckCircleIcon
                      fontSize="small"
                      color="success"
                      sx={{
                        display: model === item.model ? 'flex' : 'none',
                        position: 'absolute',
                        top: 0,
                        right: 0,
                      }}
                    />
                    <Box
                      sx={{
                        width: '30px',
                        height: '30px',
                        background: `url("${item.images?.[0]?.url}")`,
                        backgroundPosition: 'center',
                        backgroundSize: 'cover',
                        borderRadius: 2,
                      }}
                    ></Box>
                    <Typography
                      sx={{
                        px: 1,
                        fontSize: '10px',
                      }}
                    >
                      {item.model}
                    </Typography>
                  </Button>
                ))}
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mt: 1,
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit',
                }}
              >
                Giá:{' '}
              </Typography>
              {product.priceOrigin === 0 && price === 0 ? (
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: 'red',
                  }}
                >
                  Liên hệ
                </Typography>
              ) : (
                <>
                  <Typography
                    sx={{
                      fontSize: '24px',
                      color: 'red',
                    }}
                  >
                    {formatCurrency(price)}
                  </Typography>
                  <Typography
                    sx={{
                      display:
                        product.priceOrigin === product.price ? 'none' : 'flex',
                      textDecoration: 'line-through',
                    }}
                  >
                    {formatCurrency(product.priceOrigin)}
                  </Typography>
                </>
              )}
            </Box>
            {/* <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mt: 1,
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontFamily: 'inherit'
                }}
              >
                Số lượng:{' '}
              </Typography>
              <Button
                sx={{
                  color: 'black',
                }}
                onClick={() => {
                  quantity > 0 && setQuantity(quantity - 1);
                }}
              >
                <RemoveIcon fontSize="small" />{' '}
              </Button>
              <Typography>{quantity}</Typography>
              <Button
                sx={{
                  color: 'black',
                }}
                onClick={() => setQuantity(quantity + 1)}
              >
                <AddIcon fontSize="small" />{' '}
              </Button>
            </Box> */}
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                gap: 1,
                mt: 2,
                // bgcolor: 'red',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 3,
                }}
              >
                <Typography
                  sx={{
                    fontSize: '24px',
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    textWrap: 'nowrap',
                  }}
                >
                  Được bán tại
                </Typography>
                <Typography
                  sx={{
                    minWidth: '70%',
                    borderBottom: '1px solid black',
                    mb: -2,
                  }}
                ></Typography>
              </Box>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  gap: 2,
                  flexWrap: 'wrap',
                  alignItems: 'center',
                  justifyContent: 'start',
                }}
              >
                {product?.partners?.map((item, index) => (
                  <Card
                    key={index}
                    sx={{
                      display: item.images?.[0]?.url ? 'flex' : 'none',
                      width: '185px',
                      height: 'auto',
                      mt: 3,
                    }}
                  >
                    <img src={item.images?.[0]?.url} alt="logo" width="100%" />
                  </Card>
                ))}
              </Box>
              {/* <AddToCart
                productId={isAddCart ? product?._id : null}
                quantity={quantity}
                variant={{ color, size, model, price, images: [] }}
              >
                <Button
                  sx={{
                    display: 'flex',
                    gap: 1,
                    mt: 2,
                    px: 2,
                    color: 'black',
                    bgcolor: 'gold',
                    borderRadius: '2px',
                    textTransform: 'none',
                    // fontSize: '10px',
                    // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                  }}
                >
                  Thêm vào giỏ
                  <AddShoppingCartIcon fontSize="large" />
                </Button>
              </AddToCart>
              <Button
                sx={{
                  // width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  px: 2,
                  mt: 2,
                  color: 'white',
                  bgcolor: 'black',
                  borderRadius: '2px',
                  textTransform: 'none',
                  // fontSize: '10px',
                  // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                  '&:hover': { bgcolor: 'red' },
                }}
              >
                Thanh Toán <PaymentIcon fontSize="large" />
              </Button> */}
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            width: '100%',
            height: '100%',
            my: 2,
            px: 1,
            [theme.breakpoints.down('md')]: {
              flexDirection: 'column',
            },
          }}
        >
          <Paper
            sx={{
              flex: 7,
              width: '100%',
              height: 'fit-content',
              maxHeight: `${detailHeight}`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              p: 2,
              gap: 2,
              borderRadius: '10px',
              overflow: 'hidden',
              position: 'relative',
            }}
          >
            {/* {detail?.map((item, index) =>
              item.type === 'TITLE' ? (
                <Typography key={index} sx={{ fontWeight: 'bold' }}>
                  {item.value}
                </Typography>
              ) : item.type === 'BANNER' ? (
                <Box key={index}>
                  <img src={item.value} alt="" width={'100%'} height={'100%'} />
                </Box>
              ) : (
                <Typography key={index} sx={{ whiteSpace: 'pre-wrap' }}>
                  {item.value}
                </Typography>
              ),
            )} */}
            <Box
              sx={{
                display: data.length > 0 ? 'flex' : 'none',
                flexDirection: 'column',
                [theme.breakpoints.down('sm')]: {},
                width: '100%',
                height: '100%',
                overflowY: 'hidden',
              }}
              dangerouslySetInnerHTML={createMarkup(data)}
            ></Box>
            <Typography
              sx={{
                // fontWeight: 'bold',
                minHeight: '300px',
                display: data.length > 0 ? 'none' : 'flex',
              }}
            >
              Thông tin chi tiết về sản phẩm đang được cập nhật ...
            </Typography>
            <Box
              sx={{
                display:
                  userInfo?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                    ? 'flex'
                    : 'none',
                flexDirection: 'column',
                alignItems: 'center',
                position: '',
                width: '100%',
                bottom: 0,
                background: '',
              }}
            >
              <Button
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textTransform: 'none',
                  background: 'white',
                  color: 'gray',
                  boxShadow:
                    '0px 0px 20px 2px rgba(0, 0, 0, 0.05), inset 0px 0px 2px 2px rgba(0, 0, 0, 0.05)',
                }}
                onClick={() => {
                  setIsAddDetail(true);
                }}
              >
                <QueueIcon fontSize="small" />{' '}
              </Button>
            </Box>
            <Box
              sx={{
                display: `${
                  data.length > 0 && detailHeight !== '100%' ? 'flex' : 'none'
                }`,
                flexDirection: 'column',
                alignItems: 'center',
                position: 'absolute',
                width: '100%',
                bottom: 0,
                background: '#fffffff0',
              }}
            >
              <Button
                sx={{
                  display: 'flex',
                  // flexDirection: 'column',
                  alignItems: 'center',
                  textTransform: 'none',
                  background: '#ff7c7c15',
                  borderRadius: 8,
                  px: 5,
                  my: 1,
                  color: 'red',
                  border: '0.5px solid orangered',
                  animation: `${upUp} 1s ease-in-out infinite alternate`,
                  boxShadow:
                    '0px 0px 20px 2px rgba(247, 68, 68, 0.05), inset 0px 0px 2px 2px rgba(247, 44, 44, 0.05)',
                }}
                onClick={() => {
                  setDetailHeight('100%');
                }}
              >
                Xem thêm <KeyboardArrowDownIcon fontSize="small" />
              </Button>
            </Box>
          </Paper>
          <Box
            sx={{
              flex: 3,
              // bgcolor: 'green',
              // width: '30%',
              display: 'flex',
              flexDirection: 'column',
              gap: 2.5,
            }}
          >
            <Paper
              sx={{
                width: '100%',
                display:
                  specifications?.length > 0 ||
                  user?.role === 'ADMIN' ||
                  userInfo?.role === 'COLLAB'
                    ? 'flex'
                    : 'none',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                borderRadius: '10px',
                position: 'relative',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography sx={{ fontWeight: 'bold' }}>
                <AutoAwesomeIcon sx={{ color: 'red', mr: 2 }} />
                Thông số kỹ thuật
              </Typography>
              <IconButton
                sx={{
                  display:
                    user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                      ? ''
                      : 'none',
                  position: 'absolute',
                  right: 0,
                  top: 0,
                }}
                onClick={() => {
                  if (isAddSpecifications) {
                    handleEditProduct();
                    setIsAddSpecifications(false);
                  } else {
                    setIsAddSpecifications(true);
                  }
                }}
              >
                {isAddSpecifications ? <SaveAsIcon /> : <CreateIcon />}
              </IconButton>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  // gap: 2,
                  borderRadius: '10px',
                  overflow: 'hidden',
                  boxShadow:
                    '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                {specifications?.length > 0 &&
                  specifications?.map(({ title, value }, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: '100%',
                        p: 1,
                        bgcolor: index % 2 === 0 ? '#f6f3f3' : '',
                      }}
                    >
                      <Box
                        sx={{
                          width: '100%',
                          display: !isAddSpecifications ? 'flex' : 'none',
                          gap: 1,
                          fontSize: 14,
                        }}
                      >
                        <Typography
                          sx={{
                            width: '50%',
                            fontSize: 'inherit',
                            fontFamily: 'inherit',
                          }}
                        >
                          {title}
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 'inherit',
                          }}
                        >
                          {value}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          width: '100%',
                          display: isAddSpecifications ? 'flex' : 'none',
                        }}
                      >
                        <TextField
                          label="title"
                          size="small"
                          value={title}
                          onChange={e => {
                            const newSpecifications = [...specifications];
                            newSpecifications[index].title = e.target.value;
                            setSpecification(newSpecifications);
                          }}
                        />
                        <TextField
                          label="value"
                          size="small"
                          value={value}
                          onChange={e => {
                            const newSpecifications = [...specifications];
                            newSpecifications[index].value = e.target.value;
                            setSpecification(newSpecifications);
                          }}
                        />
                        <IconButton
                          onClick={() => {
                            const newSpecifications = [...specifications];
                            newSpecifications.splice(index, 1);
                            setSpecification(newSpecifications);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  ))}
                <Box
                  sx={{
                    width: '100%',
                    display: isAddSpecifications ? 'flex' : 'none',
                    justifyContent: 'center',
                    gap: 1,
                  }}
                >
                  <IconButton
                    onClick={() => {
                      const newSpecifications = [...specifications];
                      newSpecifications.push({ title: '', value: '' });
                      setSpecification(newSpecifications);
                    }}
                    sx={{
                      display:
                        user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                          ? ''
                          : 'none',
                      height: '100%',
                      color: 'black',
                    }}
                  >
                    <QueueIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Paper>
            <Paper
              sx={{
                width: '100%',
                display:
                  news?.length > 0 ||
                  user?.role === 'ADMIN' ||
                  userInfo?.role === 'COLLAB'
                    ? 'flex'
                    : 'none',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                position: 'relative',
                borderRadius: '10px',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography sx={{ fontWeight: 'bold' }}>
                <NewspaperIcon sx={{ color: 'red', mr: 2 }} />
                Tin tức liên quan sản phẩm
              </Typography>
              <IconButton
                sx={{
                  display:
                    user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                      ? ''
                      : 'none',
                  position: 'absolute',
                  right: 0,
                  top: 0,
                }}
                onClick={() => {
                  if (isAddNews) {
                    handleEditProduct();
                    setIsAddNews(false);
                  } else {
                    setIsAddNews(true);
                  }
                }}
              >
                {isAddNews ? <SaveAsIcon /> : <CreateIcon />}
              </IconButton>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  // gap: 2,
                  borderRadius: '10px',
                  overflow: 'hidden',
                  boxShadow:
                    '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                {news?.length > 0 &&
                  news?.map(({ name, id, imageUrl }, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: index < 3 ? '' : 'none',
                        width: '100%',
                        p: 1,
                        bgcolor: index % 2 === 0 ? '#f6f3f3' : '',
                      }}
                    >
                      {/* <Box
                        sx={{
                          width: '100%',
                          display: !isAddNews ? 'flex' : 'none',
                          gap: 1,
                          justifyContent: 'center',
                        }}
                      >
                        <NewsLabel link={link} />

                      </Box> */}
                      {/* <Box
                        sx={{
                          width: '100%',
                          display: isAddNews ? 'flex' : 'none',
                        }}
                      >
                        <TextField
                          size="small"
                          label="ID Tin tức"
                          value={link}
                          onChange={e => {
                            const newNews = [...news];
                            newNews[index].link = e.target.value;
                            setNews(newNews);
                          }}
                        />
                        <IconButton
                          onClick={() => {
                            const newNews = [...news];
                            newNews.splice(index, 1);
                            setNews(newNews);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box> */}
                      <Box
                        sx={{
                          maxWidth: '320px',
                          display: 'flex',
                          gap: 1,
                        }}
                      >
                        <Box
                          sx={{
                            width: 80,
                            height: 50,
                            minWidth: 80,
                            minHeight: 50,
                          }}
                        >
                          <img
                            src={imageUrl}
                            alt={name}
                            width={'100%'}
                            height={'100%'}
                          />
                        </Box>
                        <Typography
                          sx={{
                            fontSize: 14,
                            cursor: 'pointer',
                            '&: hover': {
                              textDecoration: 'underline',
                            },
                          }}
                          onClick={() => navigate(`/tin-tuc/${id}`)}
                        >
                          {name}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                {/* <Box
                  sx={{
                    width: '100%',
                    display: isAddNews ? 'flex' : 'none',
                    justifyContent: 'center',
                    gap: 1,
                  }}
                >
                  <IconButton
                    onClick={() => {
                      const newNews = [...news];
                      newNews.push({ title: '', link: '' });
                      setNews(newNews);
                    }}
                    sx={{
                      display:
                        user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                          ? ''
                          : 'none',
                      height: '100%',
                      color: 'black',
                    }}
                  >
                    <QueueIcon fontSize="small" />
                  </IconButton>
                </Box> */}
              </Box>
            </Paper>
            <Paper
              sx={{
                width: '100%',
                display:
                  videos?.length > 0 ||
                  user?.role === 'ADMIN' ||
                  userInfo?.role === 'COLLAB'
                    ? 'flex'
                    : 'none',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                borderRadius: '10px',
                position: 'relative',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography sx={{ fontWeight: 'bold' }}>
                <VideoLibraryIcon sx={{ color: 'red', mr: 2 }} />
                Video liên quan
              </Typography>
              <IconButton
                sx={{
                  display:
                    user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                      ? ''
                      : 'none',
                  position: 'absolute',
                  right: 0,
                  top: 0,
                }}
                onClick={() => {
                  if (isAddVideo) {
                    handleEditProduct();
                    setIsAddVideo(false);
                  } else {
                    setIsAddVideo(true);
                  }
                }}
              >
                {isAddVideo ? <SaveAsIcon /> : <CreateIcon />}
              </IconButton>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  // gap: 2,
                  borderRadius: '10px',
                  overflow: 'hidden',
                  boxShadow:
                    '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                }}
              >
                {videos?.length > 0 &&
                  videos?.map(({ link }, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: '100%',
                        p: 1,
                        bgcolor: index % 2 === 0 ? '#f6f3f3' : '',
                      }}
                    >
                      <Box
                        sx={{
                          width: '100%',
                          display: !isAddVideo ? 'flex' : 'none',
                          gap: 1,
                          justifyContent: 'center',
                        }}
                      >
                        <YouTube
                          videoId={link}
                          opts={opts1}
                          onReady={onPlayerReady}
                        />
                      </Box>
                      <Box
                        sx={{
                          width: '100%',
                          display: isAddVideo ? 'flex' : 'none',
                        }}
                      >
                        <TextField
                          size="small"
                          label="ID Youtube"
                          value={link}
                          onChange={e => {
                            const newVideos = [...videos];
                            newVideos[index].link = e.target.value;
                            setVideos(newVideos);
                          }}
                        />
                        <IconButton
                          onClick={() => {
                            const newVideos = [...videos];
                            newVideos.splice(index, 1);
                            setVideos(newVideos);
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  ))}
                <Box
                  sx={{
                    width: '100%',
                    display: isAddVideo ? 'flex' : 'none',
                    justifyContent: 'center',
                    gap: 1,
                  }}
                >
                  <IconButton
                    onClick={() => {
                      const newVideos = [...videos];
                      newVideos.push({ title: '', link: '' });
                      setVideos(newVideos);
                    }}
                    sx={{
                      display:
                        user?.role === 'ADMIN' || userInfo?.role === 'COLLAB'
                          ? ''
                          : 'none',
                      height: '100%',
                      color: 'black',
                    }}
                  >
                    <QueueIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Paper>
          </Box>
          {AddDetailDialog()}
        </Box>
        <SlideProductsList list={sameCategory} />
        {/* <Partners /> */}
        {/* <Recently /> */}
        {/* <Review /> */}
      </Paper>
    </>
  );
}
