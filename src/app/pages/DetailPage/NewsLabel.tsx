import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

interface Props {
  link?: string;
}

const NewsLabel: React.FC<Props> = ({ link }) => {
  const navigate = useNavigate();

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [news, setNews] = React.useState<any>({});

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const fetchNewsPage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news/${link}`,
          // { id: path },
        );

        // console.log({ result });
        if (result) {
          setNews(result);
          // console.log('news:  ', result);
        }
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  useEffect(() => {
    fetchNewsPage();
  }, [link]);

  return (
    <Box
      sx={{
        maxWidth: '320px',
        display: 'flex',
        gap: 1,
      }}
    >
      <Box
        sx={{
          width: 80,
          height: 50,
          minWidth: 80,
          minHeight: 50,
        }}
      >
        <img
          src={news?.imageUrl}
          alt={news?.name}
          width={'100%'}
          height={'100%'}
        />
      </Box>
      <Typography
        sx={{
          fontSize: 14,
          cursor: 'pointer',
          '&: hover': {
            textDecoration: 'underline',
          },
        }}
        onClick={() => navigate(`/tin-tuc/${link}`)}
      >
        {news?.name}
      </Typography>
    </Box>
  );
};

export default NewsLabel;
