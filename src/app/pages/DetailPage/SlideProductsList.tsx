import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import AddToCart from 'app/components/AppWrapper/AddToCart';

import new1 from 'assets/images/product/new/new_01.webp';
import new2 from 'assets/images/product/new/new_02.webp';
import new3 from 'assets/images/product/new/new_03.webp';
import new4 from 'assets/images/product/new/new_04.webp';

// const InitListNewProduct = [
//   {
//     background1: new1,
//     background2: new2,
//     href: 'bestSale-1',
//     title: 'Dock Sạc Không Dây',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
//   {
//     background1: new3,
//     background2: new4,
//     href: 'bestSale-1',
//     title: 'Cáp Sạc đa năng',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
//   {
//     background1: new1,
//     background2: new2,
//     href: 'bestSale-1',
//     title: 'Dock Sạc Không Dây',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
//   {
//     background1: new3,
//     background2: new4,
//     href: 'bestSale-1',
//     title: 'Cáp Sạc đa năng',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
//   {
//     background1: new1,
//     background2: new2,
//     href: 'bestSale-1',
//     title: 'Dock Sạc Không Dây',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
//   {
//     background1: new3,
//     background2: new4,
//     href: 'bestSale-1',
//     title: 'Cáp Sạc đa năng',
//     bestSaleId: '',
//     price: 299999,
//     save: 259999,
//   },
// ];

interface Props {
  list?: any[];
}

const SlideProductsList: React.FC<Props> = ({ list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [products, setProducts] = React.useState<any[]>(list || []);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (product: string) => {
    if (product) {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Product not exist',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  useEffect(() => {
    if (list?.length) {
      setProducts(list);
    }
  }, [list]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        my: 5,
        // px: 3,
        py: 2,
        borderRadius: 2,
        // boxShadow:
        //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          // bgcolor: 'red'
        }}
      >
        <Typography
          sx={{
            fontWeight: 'bold',
            textTransform: 'uppercase',
          }}
        >
          Sản phẩm cùng danh mục
        </Typography>
        {/* <Button>Xem tất cả</Button> */}
      </Box>
      <Box
        onMouseEnter={() => {
          setIsNavigate(true);
        }}
        onMouseLeave={() => {
          setIsNavigate(false);
        }}
        ref={containerRef}
        sx={{
          // width: '95%',
          // my: 2,
          px: '20px',
        }}
      >
        <Swiper
          style={{
            zIndex: 0,
            borderRadius: 5,
            paddingBottom: '20px',
            paddingTop: '10px',
            padding: '10px',
          }}
          spaceBetween={60}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: 5000, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          navigation={isNavigate}
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {products.map(
            (
              { _id, images, name, price, priceOrigin, id, collectionInfos },
              index: any,
            ) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: {
                      _id,
                      images,
                      name,
                      price,
                      priceOrigin,
                      collectionInfos,
                    },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: '300px',
                  height: '460px',
                  minWidth: '300px',
                  background: '',
                  borderRadius: 20,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  // overflow: 'hidden',
                  position: 'relative',
                  // transform: `${bestIndex === index ? 'scale(1.05)' : ''}`,
                  // transition:
                  //   'opacity .2s ease-in-out, transform .5s ease-in-out',
                  boxShadow:
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 0.5px rgba(0,0,0,0.05), 0px 0px 1px 0.5px rgba(0,0,0,0.05)',
                }}
              >
                <FavoriteBorderIcon
                  sx={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}
                />
                <Typography
                  sx={{
                    mr: 'auto',
                    p: 2,
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: 'gray',
                    fontFamily: 'inherit',
                    textTransform: 'capitalize',
                  }}
                >
                  {collectionInfos?.find(el => el.type === 'CATEGORY')?.name ||
                    ''}
                </Typography>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    borderRadius: 3,
                    '&:hover': {},
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'250px'}
                    height={'250px'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography
                      sx={{
                        width: 'fit-content',
                        ml: 2,
                        py: 0.5,
                        px: 1,
                        fontFamily: 'inherit',
                        fontSize: '12px',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        borderRadius: 2,
                        color: 'white',
                      }}
                    >
                      Sản phẩm mới
                    </Typography>
                    <Typography
                      className="two-line-typography"
                      sx={{
                        py: 1,
                        pb: 0.5,
                        px: 2,
                        fontFamily: 'inherit',
                        fontSize: '16px',
                        fontWeight: 'bold',
                        color: bestIndex === index ? 'red' : '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'red',
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                        py: 1,
                        px: 2,
                        // fontSize: '20px',
                      }}
                    >
                      {priceOrigin === 0
                        ? 'Liên hệ'
                        : formatCurrency(priceOrigin)}
                    </Typography>
                  </Box>
                </Box>
              </SwiperSlide>
            ),
          )}
        </Swiper>
      </Box>
    </Box>
  );
};

export default SlideProductsList;
