import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import Avatar from '@mui/material/Avatar';
import { red } from '@mui/material/colors';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';

interface ExpandMoreProps extends IconButtonProps {
  expand: boolean;
}
interface RecipeReviewCardProps {
  img?: string;
  title?: string;
  time?: any;
  isFavorite: boolean;
  isPublic: boolean;
  isRecycle: boolean;
  toggle: boolean;
  content?: string;
  group?: string;
  customer?: string;
  openImg: () => void;
  handleFavorite: () => void;
  handlePublic: () => void;
  hiddenImg: () => void;
  removeImg: () => void;
}

const ExpandMore = styled((props: ExpandMoreProps) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

export default function RecipeReviewCard({
  img,
  title,
  time,
  content,
  group,
  customer,
  isFavorite,
  isPublic,
  isRecycle,
  toggle,
  openImg,
  hiddenImg,
  removeImg,
  handleFavorite,
  handlePublic,
}: RecipeReviewCardProps) {
  const [expanded, setExpanded] = React.useState(false);

  const handleFileClick = () => {
    window.open(img, '_blank');
  };

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  function BasicMenu() {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };

    React.useEffect(() => {
      handleClose();
    }, [toggle]);

    return (
      <div>
        <IconButton
          id="basic-button"
          aria-controls={open ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          <MenuItem
            sx={{
              display: !isPublic ? 'none' : 'block',
            }}
            onClick={hiddenImg}
          >
            <DeleteForeverIcon color="warning" /> Xoá file
          </MenuItem>
          <MenuItem
            sx={{
              display: isPublic ? 'none' : 'block',
            }}
            onClick={removeImg}
          >
            <DeleteForeverIcon color="error" /> Xoá file vĩnh viễn
          </MenuItem>
        </Menu>
      </div>
    );
  }

  return (
    <Card
      sx={{
        maxWidth: 300,
        height: 'fit-content',
        opacity: isPublic ? 1 : 0.5,
        display: isPublic !== isRecycle ? 'block' : 'none',
      }}
    >
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
            {title?.split('')?.[0].toUpperCase() || 'A'}
          </Avatar>
        }
        action={BasicMenu()}
        title={title}
        subheader={new Date(time).toString()}
        titleTypographyProps={{
          width: 180,
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          fontStyle: 'italic',
          fontSize: 12,
        }}
        subheaderTypographyProps={{
          fontSize: 10,
        }}
      />
      <CardMedia
        component="img"
        width="300"
        height="auto"
        loading="lazy"
        image={img}
        // crossOrigin="anonymous"
        alt={title}
        onClick={handleFileClick}
        sx={{
          cursor: 'pointer',
          mx: 'auto',
          px: 1,
        }}
      />
      <CardContent></CardContent>
      <CardActions disableSpacing>
        <IconButton aria-label="add to favorites" onClick={handleFavorite}>
          <FavoriteIcon
            sx={{
              color: isFavorite ? 'red' : 'inherit',
            }}
          />
        </IconButton>
        <IconButton aria-label="share">
          <ShareIcon />
        </IconButton>
        <ExpandMore
          expand={expanded}
          onClick={handleExpandClick}
          aria-expanded={expanded}
          aria-label="show more"
        >
          <ExpandMoreIcon />
        </ExpandMore>
      </CardActions>
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent>
          <Typography paragraph>Thông tin thêm:</Typography>
          <Typography paragraph whiteSpace={'pre-wrap'}>
            {content}
          </Typography>
        </CardContent>
      </Collapse>
    </Card>
  );
}
