import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { useState, useEffect } from 'react';
import axios from 'axios';
import { saveItem } from 'utils/LocalStorage';
import { useNavigate, useLocation } from 'react-router-dom';
import Grid from '@mui/material/Unstable_Grid2';
import Box from '@mui/material/Box';
import Paper, { PaperProps } from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import AddIcon from '@mui/icons-material/Add';

import Breadcrumbs from '@mui/material/Breadcrumbs';
import Dialog from '@mui/material/Dialog';
import DeleteIcon from '@mui/icons-material/Delete';
import ReplyIcon from '@mui/icons-material/Reply';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import {
  Typography,
  Backdrop,
  CircularProgress,
  App<PERSON><PERSON>,
  Too<PERSON>bar,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Fab,
  Tooltip,
  Avatar,
  Badge,
  Divider,
  Container,
  Stack,
  Alert,
} from '@mui/material';
import { createTheme, useTheme, ThemeProvider } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import FolderIcon from '@mui/icons-material/Folder';
import ImageIcon from '@mui/icons-material/Image';
import HomeIcon from '@mui/icons-material/Home';
import RefreshIcon from '@mui/icons-material/Refresh';

import Button from '@mui/material/Button';
import FileUpload from './FileUpload';
import RecipeReviewCard from './CardImage';
import FolderBox from './CardFolder';
import { useDispatch } from 'react-redux';

const IMG_URL = process.env.REACT_APP_IMG_URL;

export function FileManagerPage() {
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const [list, setList] = useState<any>([]);
  const [name, setName] = useState<any>('');
  const [view, setView] = useState<string>('list');
  const [pageNavigate, setPageNavigate] = useState<any[]>([]);
  const [listFile, setListFile] = useState<any>([]);
  const [listGroupFile, setListGroupFile] = useState<any>([]);
  const [openBackdrop, setOpenBackdrop] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isRecycle, setIsRecycle] = React.useState(
    localStorage.getItem('isRecycle') === 'true' ? true : false,
  );
  const [isOpenCreateGroupFile, setIsOpenCreateGroupFile] =
    React.useState(false);
  const [params, setParams] = React.useState<any>({});

  const handleCloseBackdrop = () => {
    setOpenBackdrop(false);
  };
  const handleOpenBackdrop = () => {
    setOpenBackdrop(true);
  };

  // const classes = useStyles();
  const theme = createTheme();
  const navigate = useNavigate();
  const { pathname, search } = useLocation();
  const role = localStorage.getItem('ROLE');

  const toggleDialogCreateData = () => {
    setIsOpenCreateGroupFile(!isOpenCreateGroupFile);
  };

  const handleUploadSuccess = (uploadMessage: boolean) => {
    if (uploadMessage) setIsLoading(!isLoading);
  };

  const handleSubmitCreateGroupFile = () => {
    // const token = localStorage.getItem('TOKEN');
    // const {campaign, rule} = assignParams;
    const configCreate = {
      method: 'post',
      url: `${IMG_URL}/file/group`,
      headers: {
        'Content-Type': 'application/json',
        // Authorization: `Bearer ${token}`,
      },
      data: {
        name,
        parentId: params.group,
      },
    };
    axios
      .request(configCreate)
      .then(res => {
        setIsOpenCreateGroupFile(false);
        setIsLoading(!isLoading);
        setName('');
      })
      .catch(error => {
        setIsOpenCreateGroupFile(false);
        console.log(error);
      });
  };
  const handleUpdatePublicGroupFile = (id: any, isPublic: boolean) => {
    const token = localStorage.getItem('TOKEN');
    const configDelete = {
      method: 'put',
      url: `${IMG_URL}/file/group`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: { id, isPublic },
    };

    axios
      .request(configDelete)
      .then(res => {
        setIsLoading(!isLoading);
        // toast.success(res.data.message, toastOptions);
      })
      .catch(error => {
        console.log(error);
      });
  };

  const handleUpdateFavorite = (id: any, isFavorite: boolean) => {
    const token = localStorage.getItem('TOKEN');
    const configDelete = {
      method: 'put',
      url: `${IMG_URL}/file/img`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: { id, isFavorite },
    };

    axios
      .request(configDelete)
      .then(res => {
        setIsLoading(!isLoading);
        // toast.success(res.data.message, toastOptions);
      })
      .catch(error => {
        console.log(error);
      });
  };

  const handleHiddenFile = (id: any) => {
    handleOpenBackdrop();
    const token = localStorage.getItem('TOKEN');
    const configDelete = {
      method: 'delete',
      url: `${IMG_URL}/file/img/hidden`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: { id },
    };
    axios
      .request(configDelete)
      .then(res => {
        setIsLoading(!isLoading);
      })
      .catch(error => {
        console.log(error);
      });
  };
  const handleRemoveFile = (id: any) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa vĩnh viễn tệp này không?')) {
      const token = localStorage.getItem('TOKEN');
      const configDelete = {
        method: 'delete',
        url: `${IMG_URL}/file/img`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        data: { id },
      };

      axios
        .request(configDelete)
        .then(res => {
          setIsLoading(!isLoading);
        })
        .catch(error => {
          console.log(error);
        });
    }
  };

  const handleRemoveFolder = (id: any) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa thư mục này không?')) {
      const token = localStorage.getItem('TOKEN');
      const configDelete = {
        method: 'delete',
        url: `${IMG_URL}/file/group`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        data: { id },
      };

      axios
        .request(configDelete)
        .then(res => {
          setIsLoading(!isLoading);
        })
        .catch(error => {
          console.log(error);
          return dispatch(
            utilsAction.showSnackbar({
              message:
                error.response.data.message ===
                'Can not remove folder that contains files'
                  ? 'Không thể xóa thư mục chứa file'
                  : error.response.data.message,
              variant: 'error',
            }),
          );
        });
    }
  };

  // @Loading data
  useEffect(() => {
    // const token = localStorage.getItem('TOKEN');
    const fetchData = () => {
      const configGetFiles = {
        method: 'get',
        url: `${IMG_URL}/file/img?reverse=${params.reverse}&keyword=${
          params.keyword || ''
        }&group=${params.group || ''}`,
        headers: {
          'Content-Type': 'application/json',
          // Authorization: `Bearer ${token}`,
        },
      };

      const configGetFileGroups = {
        method: 'get',
        url: `${IMG_URL}/file/group?reverse=${params.reverse}&keyword=${
          params.keyword || ''
        }&parentId=${params.group || ''}`,
        headers: {
          'Content-Type': 'application/json',
          // Authorization: `Bearer ${token}`,
        },
      };

      handleOpenBackdrop();
      axios
        .request(configGetFiles)
        .then(res => {
          handleCloseBackdrop();
          const metaData = res?.data?.data;
          const newList = metaData.map(item => {
            return { checked: false, ...item };
          });
          const result = res?.data?.data;
          // console.log('files: ', result);
          setList(newList);
          if (result) {
            setListFile(result);
          }
        })
        .catch(error => {
          console.log(error);
        });
      axios
        .request(configGetFileGroups)
        .then(res => {
          handleCloseBackdrop();
          const result = res?.data?.data;
          // console.log('file-group: ', result);
          if (result) {
            setListGroupFile(result.folders);
            setPageNavigate(result.pageNavigate);
          }
        })
        .catch(error => {
          console.log(error);
        });
    };
    if (Object.keys(params)?.length > 0) {
      fetchData();
    }
  }, [isLoading, pathname, params]);

  useEffect(() => {
    const searchParams = new URLSearchParams(search);
    const paramsObj: any = { reverse: '1' };
    searchParams.forEach((value, key) => {
      paramsObj[key] = value;
    });
    // console.log('searchParams: ', paramsObj);
    setParams(paramsObj);
  }, [search]);

  const divRef = React.useRef(null);

  const SimpleBackdrop = () => {
    return (
      <div>
        <Backdrop
          sx={{ color: '#fff', zIndex: theme => theme.zIndex.drawer + 1 }}
          open={openBackdrop}
          onClick={handleCloseBackdrop}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
      </div>
    );
  };

  function BasicBreadcrumbs() {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <HomeIcon color="primary" sx={{ fontSize: 20 }} />
        <Breadcrumbs
          aria-label="breadcrumb"
          sx={{
            '& .MuiBreadcrumbs-separator': {
              color: 'text.secondary',
            },
          }}
        >
          <Chip
            label="Thư mục gốc"
            variant="outlined"
            size="small"
            clickable
            onClick={() => {
              navigate('/quan-ly-hinh-anh');
              setPageNavigate([]);
              saveItem({
                'page-navigate': [],
              });
            }}
            sx={{
              '&:hover': {
                bgcolor: 'primary.light',
                color: 'white',
              },
            }}
            icon={<HomeIcon />}
          />

          {pageNavigate?.map((item, index) => (
            <Chip
              key={item?._id}
              label={item?.name}
              variant={
                index === pageNavigate.length - 1 ? 'filled' : 'outlined'
              }
              size="small"
              clickable
              onClick={() => {
                const findIndex = pageNavigate.findIndex(
                  navItem => navItem._id === item._id,
                );
                setPageNavigate(pageNavigate.slice(0, findIndex + 1));
                saveItem({
                  'page-navigate': pageNavigate.slice(0, findIndex + 1),
                });
                const searchParams = new URLSearchParams({
                  ...params,
                  group: item._id,
                });
                navigate({ search: searchParams.toString() });
              }}
              sx={{
                '&:hover': {
                  bgcolor: 'primary.light',
                  color: 'white',
                },
              }}
              icon={<FolderIcon />}
            />
          ))}
        </Breadcrumbs>
      </Box>
    );
  }

  function ViewDisplay() {
    return (
      <Box
        sx={{
          display: 'flex',
          bgcolor: 'grey.100',
          borderRadius: 2,
          p: 0.5,
          border: '1px solid',
          borderColor: 'grey.300',
        }}
      >
        <Tooltip title="Xem dạng danh sách">
          <IconButton
            size="small"
            onClick={() => setView('list')}
            sx={{
              color: view === 'list' ? 'primary.main' : 'text.secondary',
              bgcolor: view === 'list' ? 'white' : 'transparent',
              boxShadow: view === 'list' ? 1 : 0,
              '&:hover': {
                bgcolor: view === 'list' ? 'white' : 'grey.200',
              },
            }}
          >
            <ViewListIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Xem dạng lưới">
          <IconButton
            size="small"
            onClick={() => setView('grid')}
            sx={{
              color: view === 'grid' ? 'primary.main' : 'text.secondary',
              bgcolor: view === 'grid' ? 'white' : 'transparent',
              boxShadow: view === 'grid' ? 1 : 0,
              '&:hover': {
                bgcolor: view === 'grid' ? 'white' : 'grey.200',
              },
            }}
          >
            <GridViewIcon />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>🖼️ Quản lý hình ảnh - File Manager</title>
        <meta
          name="description"
          content="Hệ thống quản lý file và hình ảnh chuyên nghiệp"
        />
      </Helmet>

      {/* Create Folder Dialog */}
      <Dialog
        open={isOpenCreateGroupFile}
        onClose={toggleDialogCreateData}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 24px 48px rgba(0,0,0,0.2)',
          },
        }}
      >
        <Box sx={{ p: 4 }}>
          <Typography
            variant="h6"
            fontWeight="bold"
            sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}
          >
            <FolderIcon color="primary" />
            Tạo thư mục mới
          </Typography>

          <TextField
            fullWidth
            label="Tên thư mục"
            value={name}
            onChange={e => setName(e.target.value)}
            variant="outlined"
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <FolderIcon sx={{ mr: 1, color: 'text.secondary' }} />
              ),
            }}
            placeholder="Nhập tên thư mục..."
          />

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              onClick={toggleDialogCreateData}
              sx={{ px: 3 }}
            >
              Hủy
            </Button>
            <Button
              variant="contained"
              onClick={handleSubmitCreateGroupFile}
              disabled={!name.trim()}
              sx={{
                px: 3,
                background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                '&:hover': {
                  background:
                    'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                },
              }}
              startIcon={<AddIcon />}
            >
              Tạo mới
            </Button>
          </Box>
        </Box>
      </Dialog>

      {/* Main Content */}
      <Box
        ref={divRef}
        sx={{
          display: role !== 'ADMIN' ? 'none' : 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          bgcolor: '#f8fafc',
        }}
      >
        {/* Header AppBar */}
        <AppBar
          position="sticky"
          elevation={0}
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderBottom: '1px solid rgba(255,255,255,0.1)',
          }}
        >
          <Toolbar sx={{ py: 1 }}>
            <Box
              sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}
            >
              <Avatar
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  width: 48,
                  height: 48,
                }}
              >
                <ImageIcon />
              </Avatar>

              <Box>
                <Typography variant="h6" fontWeight="bold">
                  🖼️ Quản lý File & Hình ảnh
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Hệ thống quản lý file chuyên nghiệp
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Tooltip title="Làm mới">
                <IconButton
                  color="inherit"
                  onClick={() => setIsLoading(!isLoading)}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Tạo thư mục mới">
                <IconButton
                  color="inherit"
                  onClick={() => setIsOpenCreateGroupFile(true)}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' },
                  }}
                >
                  <AddIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </AppBar>

        <Container maxWidth="xl" sx={{ py: 3, flex: 1 }}>
          {/* Breadcrumbs & Controls */}
          <Card elevation={2} sx={{ mb: 3, borderRadius: 3 }}>
            <CardContent sx={{ py: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  flexWrap: 'wrap',
                  gap: 2,
                }}
              >
                <Box sx={{ flex: 1 }}>{BasicBreadcrumbs()}</Box>

                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Chip
                    label={`${listFile.length} files`}
                    color="primary"
                    variant="outlined"
                    icon={<ImageIcon />}
                  />
                  <Chip
                    label={`${listGroupFile.length} folders`}
                    color="secondary"
                    variant="outlined"
                    icon={<FolderIcon />}
                  />
                  {ViewDisplay()}
                </Box>
              </Box>
            </CardContent>
          </Card>
          {/* Upload & Recycle Section */}
          <Box sx={{ display: 'flex', gap: 3, mb: 4, flexWrap: 'wrap' }}>
            {/* Upload Section */}
            {!isRecycle && (
              <Card
                elevation={3}
                sx={{
                  flex: 1,
                  minWidth: 300,
                  borderRadius: 3,
                  border: '2px dashed',
                  borderColor: 'primary.light',
                  bgcolor: 'primary.50',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: 'primary.100',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  },
                }}
              >
                <CardContent sx={{ p: 4, textAlign: 'center' }}>
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    color="primary.main"
                    sx={{ mb: 2 }}
                  >
                    📤 Tải lên file mới
                  </Typography>
                  <FileUpload
                    onUploadSuccess={handleUploadSuccess}
                    group={params.group}
                  />
                </CardContent>
              </Card>
            )}

            {/* Recycle Toggle */}
            <Card
              elevation={3}
              sx={{
                minWidth: 200,
                borderRadius: 3,
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                bgcolor: isRecycle ? 'warning.light' : 'error.light',
                color: isRecycle
                  ? 'warning.contrastText'
                  : 'error.contrastText',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                },
              }}
              onClick={() => {
                setIsRecycle(!isRecycle);
                localStorage.setItem('isRecycle', isRecycle ? 'false' : 'true');
              }}
            >
              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                <Box sx={{ mb: 2 }}>
                  {isRecycle ? (
                    <ReplyIcon sx={{ fontSize: 48 }} />
                  ) : (
                    <DeleteIcon sx={{ fontSize: 48 }} />
                  )}
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {isRecycle ? '🔄 Khôi phục' : '🗑️ Thùng rác'}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {isRecycle ? 'Xem file đã xóa' : 'Chuyển đến thùng rác'}
                </Typography>
              </CardContent>
            </Card>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: isRecycle ? 'column-reverse' : 'column',
            }}
          >
            <Box>
              <BasicBreadcrumbs />
              <ViewDisplay />
              <Grid
                container
                spacing={{ xs: 1, sm: 1, md: 1, lg: 2 }}
                columns={{ xs: 2, sm: 3, md: 4, lg: 6 }}
                sx={{
                  py: 3,
                  justifyContent: 'start',
                  [theme.breakpoints.down('sm')]: {
                    justifyContent: 'center',
                  },
                  display: view === 'grid' ? 'flex' : 'none',
                }}
              >
                {listGroupFile?.map((item, index) => (
                  <Grid xs={1} sm={1} md={1} lg={1} key={index}>
                    <FolderBox
                      toggle={isLoading}
                      title={item.name}
                      isLocked={item.isPublic}
                      handleLock={() =>
                        handleUpdatePublicGroupFile(item._id, !item.isPublic)
                      }
                      onClick={() => {
                        // navigate(`/filemanager/${item._id}`);
                        const searchParams = new URLSearchParams({
                          ...params,
                          group: item?._id,
                        });
                        navigate({ search: searchParams.toString() });
                      }}
                      editGroup={() => {}}
                      removeGroup={() => handleRemoveFolder(item._id)}
                    />
                  </Grid>
                ))}
              </Grid>
              <Box
                sx={{
                  display: view === 'list' ? 'flex' : 'none',
                  flexDirection: 'column',
                  gap: 1,
                  p: 2,
                }}
              >
                {listGroupFile?.map((item, index) => (
                  <Box key={index}>
                    <FolderBox
                      toggle={isLoading}
                      title={item.name}
                      isLocked={item.isPublic}
                      handleLock={() =>
                        handleUpdatePublicGroupFile(item._id, !item.isPublic)
                      }
                      onClick={() => {
                        // navigate(`/filemanager/${item._id}`);
                        const searchParams = new URLSearchParams({
                          ...params,
                          group: item?._id,
                        });
                        navigate({ search: searchParams.toString() });
                      }}
                      editGroup={() => {}}
                      removeGroup={() => handleRemoveFolder(item._id)}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
            {/* Files Section */}
            <Card elevation={2} sx={{ borderRadius: 3, mt: 3 }}>
              <CardContent>
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}
                >
                  <ImageIcon color="primary" />
                  Hình ảnh ({listFile.length})
                </Typography>

                {listFile.length === 0 ? (
                  <Alert severity="info" sx={{ borderRadius: 2 }}>
                    <Typography>
                      {isRecycle
                        ? 'Thùng rác trống'
                        : 'Chưa có file nào trong thư mục này'}
                    </Typography>
                  </Alert>
                ) : (
                  <Box
                    sx={{
                      display: 'grid',
                      gridTemplateColumns:
                        view === 'grid'
                          ? {
                              xs: 'repeat(1, 1fr)',
                              sm: 'repeat(2, 1fr)',
                              md: 'repeat(3, 1fr)',
                              lg: 'repeat(4, 1fr)',
                              xl: 'repeat(5, 1fr)',
                            }
                          : 'repeat(1, 1fr)',
                      gap: 3,
                    }}
                  >
                    {listFile?.map((item, index) => (
                      <RecipeReviewCard
                        key={item._id}
                        toggle={isLoading}
                        img={`${IMG_URL}/file/img/${item.filename}`}
                        time={item.createdAt}
                        title={item.originalName}
                        content={item.note}
                        group={item.group?.name}
                        customer={item.customerId?.name}
                        isFavorite={item.isFavorite}
                        isPublic={item.isPublic}
                        isRecycle={isRecycle}
                        openImg={() => {
                          window.open(
                            `${IMG_URL}/file/img/${item.filename}`,
                            '_blank',
                          );
                        }}
                        handleFavorite={() =>
                          handleUpdateFavorite(item._id, !item.isFavorite)
                        }
                        handlePublic={() => {}}
                        hiddenImg={() => handleHiddenFile(item._id)}
                        removeImg={() => handleRemoveFile(item._id)}
                      />
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        </Container>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 6px 20px rgba(33, 150, 243, .4)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
              transform: 'scale(1.1)',
            },
            transition: 'all 0.3s ease',
            zIndex: 1000,
          }}
          onClick={() => setIsOpenCreateGroupFile(true)}
        >
          <Tooltip title="Tạo thư mục mới" placement="left">
            <AddIcon />
          </Tooltip>
        </Fab>

        {SimpleBackdrop()}
      </Box>
    </>
  );
}
