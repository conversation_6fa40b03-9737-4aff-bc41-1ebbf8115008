import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import Avatar from '@mui/material/Avatar';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import EditNoteIcon from '@mui/icons-material/EditNote';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import { useEffect } from 'react';

interface ExpandMoreProps extends IconButtonProps {
  expand: boolean;
}
interface RecipeReviewCardProps {
  title?: string;
  isLocked: boolean;
  toggle: boolean;
  onClick: () => void;
  handleLock: () => void;
  editGroup: () => void;
  removeGroup: () => void;
}

const ExpandMore = styled((props: ExpandMoreProps) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

export default function FolderBox({
  title,
  isLocked,
  toggle,
  onClick,
  handleLock,
  editGroup,
  removeGroup,
}: RecipeReviewCardProps) {
  const [expanded, setExpanded] = React.useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  function BasicMenu() {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };

    useEffect(() => {
      handleClose();
    }, [toggle]);

    return (
      <div>
        <IconButton
          id="basic-button"
          aria-controls={open ? 'basic-menu' : undefined}
          // aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          <MenuItem onClick={editGroup}>
            <EditNoteIcon color="primary" /> Cập nhật
          </MenuItem>
          <MenuItem onClick={removeGroup}>
            <DeleteForeverIcon color="error" /> Xoá thư mục
          </MenuItem>
        </Menu>
      </div>
    );
  }

  return (
    <Card
      sx={{
        width: '100%',
        height: '60px',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Typography
        onClick={onClick}
        className="two-line-typography"
        sx={{
          width: '100%',
          height: '40px',
          m: 2,
          fontSize: 12,
          fontWeight: 'bold',
          cursor: 'pointer',
          overflow: 'hidden',
        }}
      >
        {title}
      </Typography>
      <IconButton onClick={handleLock}>
        <LockOpenIcon
          sx={{
            display: !isLocked ? 'none' : '',
          }}
        />
        <LockIcon
          sx={{
            display: isLocked ? 'none' : '',
          }}
        />
      </IconButton>
      <Box sx={{}}>{BasicMenu()}</Box>
    </Card>
  );
}
