import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { Button } from '@mui/material';
import CloudUploadRoundedIcon from '@mui/icons-material/CloudUploadRounded';

const IMG_URL = process.env.REACT_APP_IMG_URL;

interface FileUploadProps {
  onUploadSuccess: (message: boolean) => void;
  group: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess, group }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  //   const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      // 'application/pdf': [],
    },
  });

  const uploadFiles = async (e: any) => {
    e.stopPropagation();
    // const token = localStorage.getItem('TOKEN');
    const formData = new FormData();
    if (group) formData.append('group', group);
    files.forEach(file => formData.append('file', file));

    setUploading(true);
    try {
      const response = await axios.post(`${IMG_URL}/file/img`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          // Authorization: `Bearer ${token}`,
        },
      });
      console.log('upload response: ', response.data.data.url);
      onUploadSuccess(true);
      setFiles([]);
    } catch (error) {
      onUploadSuccess(false);
      console.error('Error uploading file:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div
      {...getRootProps()}
      style={{
        border: '2px dashed #cccccc',
        padding: '30px',
        textAlign: 'center',
        width: '200px',
        height: '200px',
        background: 'orangegreen',
        cursor: 'pointer',
        borderRadius: '50%',
        fontSize: '12px',
        margin: '20px',
      }}
    >
      <input {...getInputProps()} />
      {isDragActive ? (
        <p>Kéo thả tại đây ...</p>
      ) : (
        <p>Kéo thả file vào đây hoặc click vào đây để chọn file</p>
      )}
      <CloudUploadRoundedIcon
        sx={{
          fontSize: 50,
          color: 'green',
        }}
      />
      <div>
        {files.map(file => (
          <div key={file.name}>{file.name}</div>
        ))}
      </div>
      {files?.length > 0 && (
        <Button variant="outlined" onClick={uploadFiles} disabled={uploading}>
          {uploading ? 'Uploading...' : 'Upload'}
        </Button>
      )}
    </div>
  );
};

export default FileUpload;
