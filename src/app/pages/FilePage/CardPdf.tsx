import * as React from 'react';
import { styled } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import Avatar from '@mui/material/Avatar';
import { red } from '@mui/material/colors';
import CardMedia from '@mui/material/CardMedia';
import CardContent from '@mui/material/CardContent';
import CardActions from '@mui/material/CardActions';
import Collapse from '@mui/material/Collapse';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import EditNoteIcon from '@mui/icons-material/EditNote';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';

interface ExpandMoreProps extends IconButtonProps {
  expand: boolean;
}

interface PdfItemProps {
  url: string;
  title?: string;
  time?: any;
  isFavorite: boolean;
  isPublic: boolean;
  content?: string;
  group?: string;
  customer?: string;
  editFile: () => void;
  handleFavorite: () => void;
  handlePublic: () => void;
  removeFile: () => void;
}

const ExpandMore = styled((props: ExpandMoreProps) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

const PdfItem: React.FC<PdfItemProps> = ({
  title,
  url,
  time,
  content,
  group,
  customer,
  isFavorite,
  isPublic,
  removeFile,
  editFile,
  handleFavorite,
  handlePublic,
}) => {
  const handleFileClick = () => {
    window.open(url, '_blank');
  };

  const [expanded, setExpanded] = React.useState(false);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  function BasicMenu() {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };

    return (
      <div>
        <IconButton
          id="basic-button"
          aria-controls={open ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          <MenuItem onClick={editFile}>
            <EditNoteIcon color="primary" /> Cập nhật
          </MenuItem>
          <MenuItem onClick={removeFile}>
            <DeleteForeverIcon color="error" /> Xoá file
          </MenuItem>
        </Menu>
      </div>
    );
  }

  return (
    <Card sx={{ maxWidth: 345 }}>
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
            {title?.split('')?.[0].toUpperCase() || 'A'}
          </Avatar>
        }
        action={BasicMenu()}
        title={title}
        subheader={new Date(time).toString()}
      />
      <CardMedia
        component="div"
        onClick={handleFileClick}
        sx={{
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '190px',
        }}
      >
        <PictureAsPdfIcon
          color="error"
          sx={{
            fontSize: '50px',
          }}
        />
      </CardMedia>
      <CardContent>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            fontStyle: 'italic',
          }}
        >
          Note: {content}
        </Typography>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            fontStyle: 'italic',
          }}
        >
          Khách hàng: {customer}
        </Typography>
      </CardContent>
      <CardActions disableSpacing>
        <IconButton aria-label="add to favorites" onClick={handleFavorite}>
          <FavoriteIcon
            sx={{
              color: isFavorite ? 'red' : 'inherit',
            }}
          />
        </IconButton>
        <IconButton aria-label="share">
          <ShareIcon />
        </IconButton>
        <ExpandMore
          expand={expanded}
          onClick={handleExpandClick}
          aria-expanded={expanded}
          aria-label="show more"
        >
          <ExpandMoreIcon />
        </ExpandMore>
      </CardActions>
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent>
          <Typography paragraph>Note:</Typography>
          <Typography paragraph whiteSpace={'pre-wrap'}>
            {content}
          </Typography>
        </CardContent>
      </Collapse>
    </Card>
  );
};

export default PdfItem;
