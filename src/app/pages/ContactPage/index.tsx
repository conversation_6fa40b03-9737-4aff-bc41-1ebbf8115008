import Box from '@mui/material/Box';
// import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import validator from 'validator';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import QueueIcon from '@mui/icons-material/Queue';
import CreateIcon from '@mui/icons-material/Create';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import SendIcon from '@mui/icons-material/Send';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import AccountBoxIcon from '@mui/icons-material/AccountBox';
import PhoneIphoneIcon from '@mui/icons-material/PhoneIphone';
import EmailIcon from '@mui/icons-material/Email';
import InputAdornment from '@mui/material/InputAdornment';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import {
  Backdrop,
  CircularProgress,
  TextField,
  useMediaQuery,
} from '@mui/material';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import { useUtilsSlice } from 'app/slice/utils';
import './styles.css';
import { editorConfigJodit, createMarkup } from 'utils/jodit';
import Maps from 'app/components/GooogleMaps/Maps';

import { request } from 'utils/request';
import 'jodit';
// import 'jodit/build/jodit.min.css';
import JoditEditor from 'jodit-react';
import { initAbout } from './about';

import banner1 from 'assets/images/about/banner-phuonglinhjsc_32ef92e5a0aa44bb85dd4e0859cfa370_grande.webp';
import { IconButton } from '@mui/material';

export function ContactPage() {
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);
  const [isEditAbout, setIsEditAbout] = useState<boolean>(false);
  const [data, setData] = useState<any>(initAbout);

  const [name, setName] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [content, setContent] = useState<string>('');

  const [openBackdrop, setOpenBackdrop] = React.useState(false);

  const handleCloseBackdrop = () => {
    setOpenBackdrop(false);
  };
  const handleOpenBackdrop = () => {
    setOpenBackdrop(true);
  };

  const SimpleBackdrop = () => {
    return (
      <div>
        <Backdrop
          sx={{ color: '#fff', zIndex: theme => theme.zIndex.drawer + 1 }}
          open={openBackdrop}
          onClick={handleCloseBackdrop}
        >
          <CircularProgress color="inherit" />
        </Backdrop>
      </div>
    );
  };

  const isValidEmail = (email: string): boolean => {
    return validator.isEmail(email);
  };

  const handleSendContact = async () => {
    if (email && !isValidEmail(email)) {
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Email không hợp lệ!',
          variant: 'error',
        }),
      );
    }

    handleOpenBackdrop();
    const newsData = {
      name,
      phone,
      email,
      content,
    };

    const dataSend = {
      to: '<EMAIL>',
      subject: 'Website phuonglinhjsc gửi thông báo',
      content: `Có một khách hàng vừa để lại thông tin email từ page liên hệ. Tên: ${newsData?.name} \nSố điện thoại: ${newsData?.phone} \nEmail: ${newsData?.email} \nNội dung: ${newsData?.content}`,
    };

    const signature = `
    <div style="margin-top: 20px; border-top: 1px solid #dddddd; padding-top: 20px;">
      <table cellpadding="0" cellspacing="0" style="font-family: Arial, sans-serif;">
        <tr>
          <td style="padding-right: 15px;">
            <img src="https://phuonglinhjsc.vn/logo.png" alt="Logo" style="width: 120px; height: auto;"/>
          </td>
          <td style="border-left: 2px solid #dddddd; padding-left: 15px;">
            <p style="margin: 0; font-weight: bold; color: #2B3990; font-size: 16px;">PHƯƠNG LINH JSC</p>
            <p style="margin: 5px 0; color: #666666;">Customer Executive</p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Hotline:</span> 
              <a href="tel:02822160427" style="color: #2B3990; text-decoration: none;">0926 87 87 99</a>
            </p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Email:</span> 
              <a href="mailto:<EMAIL>" style="color: #2B3990; text-decoration: none;"><EMAIL></a>
            </p>
            <p style="margin: 5px 0;">
              <span style="color: #666666;">Website:</span> 
              <a href="https://phuonglinhjsc.vn" style="color: #2B3990; text-decoration: none;">www.phuonglinhjsc.vn</a>
            </p>
            <div style="margin-top: 10px;">
              <a href="https://www.facebook.com/phuonglinhjsc.vn" style="text-decoration: none; margin-right: 10px;">
                <img src="https://phuonglinhjsc.vn/file/img/1727319813705_logomessenger.png" alt="Facebook" style="width: 20px; height: 20px;"/>
              </a>
            </div>
          </td>
        </tr>
      </table>
    </div>
  `;

    const dataReply = {
      to: newsData?.email,
      subject: 'Website phuonglinhjsc gửi thông báo',
      content: `
        <div style="font-family: Arial, sans-serif; color: #333333;">
          <p>Kính chào ${newsData?.name},</p>
          
          <p>Cảm ơn Quý khách đã quan tâm đến sản phẩm và dịch vụ của Phương Linh JSC.</p>
          
          <p>Để được tư vấn chi tiết và nhận những ưu đãi đặc quyền, Quý khách vui lòng liên hệ với bộ phận CSKH của chúng tôi qua Zalo: 
            <a href="https://zalo.me/02822160427" style="color: #2B3990;">0926 87 87 99</a>
          </p>
          
          <p>Trân trọng!</p>
          ${signature}
        </div>
      `,
    };

    const method = 'post';
    const url = `${process.env.REACT_APP_BACKEND_URL}/test/mail`;
    try {
      await request(method, url, dataSend);
      await request(method, url, dataReply);
      setEmail('');
      handleCloseBackdrop();
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Phương Linh cảm ơn quý khách đã để lại thông tin',
          variant: 'success',
        }),
      );
    } catch (error) {
      // console.log('err: ', error);
      handleCloseBackdrop();
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Gửi thông tin không thành công. Vui lòng thử lại sau',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {}, [data]);
  return (
    <>
      <Helmet>
        <title>Thông tin liên hệ</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            position: 'relative',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            width: '90%',
            height: '100%',
            p: 2,
            my: 1,
            borderRadius: 1,
            boxShadow:
              '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
          }}
        >
          <Box
            sx={{
              width: '90%',
              maxWidth: '600px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: ' space-between',
              alignItems: 'center',
              gap: 2,
              p: 2,
              my: 2,
              borderRadius: 1,
              boxShadow:
                '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
            }}
          >
            <Typography variant="h5" sx={{}}>
              Liên hệ với chúng tôi!
            </Typography>

            <TextField
              label="Họ và Tên"
              size="small"
              sx={{
                width: '100%',
              }}
              value={name}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setName(event.target.value);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AccountBoxIcon sx={{ color: '#00ccff' }} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              label="Số điện thoại"
              size="small"
              sx={{
                width: '100%',
              }}
              value={phone}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setPhone(event.target.value);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIphoneIcon sx={{ color: '#00ccff' }} />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Email"
              size="small"
              sx={{
                width: '100%',
              }}
              value={email}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setEmail(event.target.value);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon sx={{ color: '#00ccff' }} />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              label="Để lại lời nhắn"
              multiline
              rows={4}
              sx={{
                width: '100%',
              }}
              value={content}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setContent(event.target.value);
              }}
            />
            <Button
              sx={{
                color: 'white',
                bgcolor: '#00ccff',
                '&:hover': {
                  bgcolor: 'red',
                },
              }}
              onClick={handleSendContact}
            >
              Gửi
              <SendIcon fontSize="small" sx={{ ml: 1 }} />
            </Button>
          </Box>
          <Maps />
        </Box>
      </Paper>
      {SimpleBackdrop()}
    </>
  );
}
