import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import MenuList from '@mui/material/MenuList';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterAltIcon from '@mui/icons-material/FilterAlt';

interface Props {
  style?: React.CSSProperties;
}

const Filter: React.FC<Props> = ({ style = {} }) => {
  const [fiter, setFilter] = React.useState<string>('Sản phẩm nổi bật');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box
      sx={{
        width: 'fit-content',
        height: 'fit-content',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        borderRadius: 1,
        mr: 2,
        boxShadow:
          '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
      }}
    >
      <Button
        id="basic-button-display"
        aria-controls={open ? 'basic-menu-display' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        sx={{
          color: 'black',
          textTransform: 'none',
        }}
      >
        <FilterAltIcon
          sx={{
            color: 'gray',
          }}
        />{' '}
        {fiter}
      </Button>
      <Menu
        id="basic-menu-display"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button-display',
        }}
      >
        <MenuList>
          <MenuItem onClick={() => setFilter('Sản phẩm nổi bật')}>
            <ListItemText>Sản phẩm nổi bật</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => setFilter('Giá từ cao đến thấp')}>
            <ListItemText>Giá từ cao đến thấp</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => setFilter('Giá từ thấp đến cao')}>
            <ListItemText>Giá từ thấp đến cao</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => setFilter('Sản phẩm mới nhất')}>
            <ListItemText>Sản phẩm mới nhất</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => setFilter('Sản phẩm cũ nhất')}>
            <ListItemText>Sản phẩm cũ nhất</ListItemText>
          </MenuItem>
        </MenuList>
      </Menu>
    </Box>
  );
};

export default Filter;
