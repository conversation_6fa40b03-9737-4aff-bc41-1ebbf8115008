import React from 'react';

import { Box, Card, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from 'app/components/AppWrapper/theme';

import { backupTime } from 'utils/moment';
import { request } from 'utils/request';

const News: React.FC = () => {
  const navigate = useNavigate();

  const [noneVideos, setNoneVideos] = React.useState<any[]>([]);

  // TODO: get list banners from api
  const convertMongoCreatedAt = createdAt => {
    const now = new Date().getTime();
    const createdAtDate = new Date(createdAt).getTime();

    // Tính số mili giây chênh lệch giữa thời điểm hiện tại và thời điểm tạo
    const diffMilliseconds = now - createdAtDate;

    // Tính số phút chênh lệch
    const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));

    // Tính số giờ chênh lệch
    const diffHours = Math.floor(diffMilliseconds / (1000 * 60 * 60));

    // Tính số ngày chênh lệch
    const diffDays = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24));
    if (!createdAt) return '';
    if (diffHours < 1) {
      return `${diffMinutes} phút trước`;
    } else if (diffDays < 1) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 2) {
      return `${diffDays} ngày trước`;
    } else {
      return backupTime(createdAt); // Trả về thời gian tạo gốc nếu đã quá 2 ngày
    }
  };

  const fetchNewsPage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news`,
        );

        // console.log({ result });
        const newNoneVideos = () => {
          return result.filter(item => item.label !== 'video');
        };
        setNoneVideos(newNoneVideos);
        // console.log('noneVideo: ', newNoneVideos);
      } catch (error) {
        console.log(error);
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    fetchNewsPage();
  }, []);

  const onClickRef = id => {
    navigate(`/tin-tuc/${id}`);
  };

  const TruncatedTypography = styled(Typography)`
    padding: 0;
    overflow: hidden;
    font-family: inherit;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  `;

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 3,
        // px: 3,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            height: '100%',
            gap: 2,
          }}
        >
          {/* <Box
            sx={{
              flex: 2,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
            }}
          >
            <Typography
              sx={{
                fontSize: '30px',
                fontWeight: 'bold',
                fontFamily: 'inherit',
              }}
            >
              Tin tức mới nhất
            </Typography>

            <Box
              sx={{
                display: 'flex',
                gap: 2,
              }}
            >
              <Card
                onClick={() => {
                  onClickRef(noneVideos[0]?.id);
                }}
                sx={{
                  // width: '100%',
                  flex: 1,
                  height: '440px',
                  background: '',
                  borderRadius: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  justifyContent: 'start',
                  gap: 1,
                  textTransform: 'none',
                  color: 'black',
                  bgcolor: 'white',
                  position: 'relative',
                  cursor: 'pointer',
                  [theme.breakpoints.down('md')]: {
                    // justifyContent: 'center',
                    // alignItems: 'center',
                    mx: 'auto',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                  }}
                >
                  <img
                    src={noneVideos[0]?.imageUrl}
                    alt=""
                    width={'100%'}
                    height={'auto'}
                    loading="lazy"
                  />
                </Box>
                <Typography
                  sx={{
                    width: '100%',
                    color: 'gray',
                    fontStyle: 'italic',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontFamily: 'inherit',
                    px: 1,
                  }}
                >
                  {convertMongoCreatedAt(noneVideos[1]?.createdAt)}
                </Typography>
                <Typography
                  sx={{
                    width: '100%',
                    textAlign: 'left',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    fontFamily: 'inherit',
                    p: 1,
                  }}
                >
                  {noneVideos[0]?.name}
                </Typography>
                <Typography
                  sx={{
                    width: '100%',
                    color: 'gray',
                    // fontStyle: 'italic',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontFamily: 'inherit',
                    px: 1,
                  }}
                >
                  {noneVideos[0]?.description}
                </Typography>
              </Card>
              <Card
                onClick={() => {
                  onClickRef(noneVideos[1]?.id);
                }}
                sx={{
                  flex: 1,
                  height: '440px',
                  background: '',
                  borderRadius: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  justifyContent: 'start',
                  gap: 1,
                  textTransform: 'none',
                  color: 'black',
                  bgcolor: 'white',
                  position: 'relative',
                  cursor: 'pointer',
                  [theme.breakpoints.down('md')]: {
                    // justifyContent: 'center',
                    // alignItems: 'center',
                    mx: 'auto',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                  }}
                >
                  <img
                    src={noneVideos[1]?.imageUrl}
                    alt=""
                    width={'100%'}
                    height={'auto'}
                    loading="lazy"
                  />
                </Box>
                <Typography
                  sx={{
                    width: '100%',
                    color: 'gray',
                    fontStyle: 'italic',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontFamily: 'inherit',
                    px: 1,
                  }}
                >
                  {convertMongoCreatedAt(noneVideos[1]?.createdAt)}
                </Typography>
                <Typography
                  sx={{
                    width: '100%',
                    textAlign: 'left',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    fontFamily: 'inherit',
                    p: 1,
                  }}
                >
                  {noneVideos[1]?.name}
                </Typography>
                <Typography
                  sx={{
                    width: '100%',
                    color: 'gray',
                    // fontStyle: 'italic',
                    textAlign: 'left',
                    fontSize: '12px',
                    fontFamily: 'inherit',
                    px: 1,
                  }}
                >
                  {noneVideos[1]?.description}
                </Typography>
              </Card>
            </Box>
          </Box> */}
          <Box
            sx={{
              flex: 1,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              overflow: 'hidden',
              borderBottom: '1px gray solid',
              [theme.breakpoints.down('md')]: {
                display: 'none',
              },
            }}
          >
            <Typography
              sx={{
                width: '100%',
                bgcolor: '',
                fontWeight: 'bold',
                fontSize: '30px',
                fontFamily: 'inherit',
                borderBottom: '1px gray solid',
              }}
            >
              Tin tức liên quan
            </Typography>
            <Box
              sx={{
                flex: 1.5,
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                bgcolor: '',
                px: 1,
                pb: 7,
                gap: 5,
              }}
            >
              {noneVideos?.map((item, index) => (
                <Card
                  key={index}
                  sx={{
                    display: index > 1 && index < 5 ? 'flex' : 'none',
                    width: '100%',
                    height: '100px',
                    cursor: 'pointer',
                    overflow: 'hidden',
                  }}
                  onClick={() => {
                    onClickRef(item.id);
                  }}
                >
                  <Box
                    sx={{
                      width: '150px',
                      height: '100%',
                    }}
                  >
                    <img
                      src={item.imageUrl}
                      alt=""
                      width={'150px'}
                      height={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                      p: 1,
                    }}
                  >
                    <TruncatedTypography>{item.name}</TruncatedTypography>
                    <Typography
                      sx={{
                        width: '100%',
                        color: 'gray',
                        fontStyle: 'italic',
                        textAlign: 'left',
                        fontSize: '12px',
                        fontFamily: 'inherit',
                        px: 1,
                      }}
                    >
                      {convertMongoCreatedAt(item.createdAt)}
                    </Typography>
                  </Box>
                </Card>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default News;
