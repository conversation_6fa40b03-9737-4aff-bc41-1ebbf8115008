/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Button, Paper } from '@mui/material';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import QueueIcon from '@mui/icons-material/Queue';
import ListItemIcon from '@mui/material/ListItemIcon';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import IconButton from '@mui/material/IconButton';
import ArrowCircleDownIcon from '@mui/icons-material/ArrowCircleDown';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Stack from '@mui/material/Stack';
import Card from '@mui/material/Card';
import Link from '@mui/material/Link';
import Grid from '@mui/material/Unstable_Grid2';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Backdrop from '@mui/material/Backdrop';
import CircularProgress from '@mui/material/CircularProgress';
import { useMediaQuery } from '@mui/material';
import Pagination from '@mui/material/Pagination';
import MoreAction from 'app/components/MoreAction/MoreAction';
import AddProductDialog from 'app/components/Dialog/AddProductDialog';
import { useSelector, useDispatch } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';
import JoditEditor from 'jodit-react';
import { editorConfigJodit, createMarkup } from 'utils/jodit';
import TextField from '@mui/material/TextField';
import News from './News';

import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';
import omitBy from 'lodash/omitBy';
import isNil from 'lodash/isNil';
import { set } from 'lodash';
import { uploadFiles } from 'utils/uploadImg';
import MenuItem from '@mui/material/MenuItem';
import { keyframes } from '@emotion/react';

const formatCurrency = (number: number) => {
  return number?.toLocaleString('vi-VN', {
    style: 'currency',
    currency: 'VND',
  });
};

export const MONGOOSE_ID = new RegExp(/[a-fA-F0-9]{24}/);

const scrollToTop = (top: number) => {
  window.scrollTo({
    top,
    behavior: 'smooth',
  });
};

const upUp = keyframes`
  0% {
    transform: translateY(0px)
  }
  100% {
    transform: translateY(-10px)
  }
`;

export function ListProductPage() {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { path } = params;
  const brand = path || searchParams.get('brand') || '';
  const category = searchParams.get('category') || '';
  const keyword = searchParams.get('keyword') || '';

  const [products, setProducts] = React.useState<any>([]);
  const [currentProduct, setCurrentProduct] = React.useState<any>({});
  const [target, setTarget] = React.useState<any>({});
  const [data, setData] = useState<any>('');
  const [priceOption, setPriceOption] = useState<any>('');
  const [createdOption, setCreatedOption] = useState<any>('');
  const [limit, setLimit] = useState<any>(
    Number(searchParams.get('limit')) || 12,
  );
  const location = useLocation();

  const [maxHeight, setMaxHeight] = React.useState<string>('800px');
  const [total, setTotal] = React.useState<number>(0);
  const [count, setCount] = React.useState<number>(0);
  const [totalShow, setTotalShow] = React.useState<number>(1);
  const [page, setPage] = React.useState<number>(
    Number(searchParams.get('page')) || 1,
  );
  const [lastPage, setLastPage] = React.useState<number>(1);
  const [preview, setPreview] = React.useState<number>(20);
  const [inStock, setInStock] = React.useState<boolean>(false);
  const [isAdmin, setIsAdmin] = React.useState<boolean>(false);
  const [isCollab, setIsCollab] = React.useState<boolean>(false);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isAddDetail, setIsAddDetail] = React.useState<boolean>(false);
  const [outOfStock, setOutOfStock] = React.useState<boolean>(false);

  const [collectionData, setCollectionData] = React.useState<any>({});
  const [collectionImages, setCollectionImages] = React.useState<any[]>([]);
  const [collectionHeadersImages, setCollectionHeadersImages] =
    React.useState<any>();
  const [collection, setCollection] = React.useState<any>({});
  const [carts, setCarts] = React.useState<any>([]);
  const [isUserAction, setIsUserAction] = React.useState<boolean>(false);
  const [openBackdrop, setOpenBackdrop] = React.useState<boolean>(false);
  const [editProductDialog, setEditProductDialog] =
    React.useState<boolean>(false);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);

  const { actions: cartActions } = useCartSlice();
  const cartInfo = useSelector(cartInfoSelector);

  const navigate = useNavigate();
  const handleCloseBackdrop = () => setOpenBackdrop(false);
  const handleOpenBackdrop = () => setOpenBackdrop(true);

  useEffect(() => {
    if (userInfo?.role === 'ADMIN') {
      setIsAdmin(true);
    } else if (userInfo?.role === 'COLLAB') {
      setIsCollab(true);
    } else {
      setIsAdmin(false);
      setIsCollab(false);
    }
    // console.log('pathColl: ', path);
  }, [userInfo]);

  useEffect(() => {
    setCarts(cartInfo?.products);
  }, [cartInfo?.products]);

  useEffect(() => {
    try {
      handleOpenBackdrop();
      const apiFetch = async () => {
        try {
          const data: any = await request(
            'get',
            `${process.env.REACT_APP_BACKEND_URL}/api/v2/products`,
            { keyword, brandId: brand, categoryId: category, limit, page },
          );
          // console.log('data: ', data)
          setProducts(data.products);
          setTotal(data.length);
          setCount(data.totalCount);
          const num1 = Math.trunc(data.totalCount / parseInt(limit));
          const num2 = data.totalCount % parseInt(limit);
          const lastPage = num1 + (num2 > 0 ? 1 : 0);
          // console.log('lastPage: ', lastPage);
          setLastPage(lastPage);
          handleCloseBackdrop();
        } catch (error) {}
      };
      apiFetch();
      getCollectionByNameId(brand);
    } catch (error) {}
  }, [
    path,
    brand,
    category,
    keyword,
    limit,
    page,
    isLoading,
    priceOption,
    createdOption,
  ]);

  const updateRecently = async (id: any) => {
    try {
      const result: any = await request(
        'put',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/users`,
        { productId: id },
        userInfo?.token as any,
      );
      // console.log('data: ', result);
      // setData(result?.productRecentlies);
    } catch (error) {
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: get(error, 'message', 'Error Undefined'),
      //     variant: 'error',
      //   }),
      // );
    }
  };
  const handleProductAdd = (product?: any) => {
    if (product) {
      setCurrentProduct(product);
    }

    setEditProductDialog(true);
  };

  const handleProductRemove = async (product?: any) => {
    if (product && product._id) {
      try {
        const result = await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/products/${product._id}`,
        );

        dispatch(
          utilsAction.showSnackbar({
            message: 'Delete Success',
            variant: 'success',
          }),
        );

        // path && fetchProductsInCollection(path);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    }
  };

  const AddDetailDialog = () => {
    return (
      <MuiDialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={isAddDetail}
        onClose={() => setIsAddDetail(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignContent: 'center',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                fontWeight: 'bold',
              }}
            >
              Chỉnh sửa nội dung giới thiệu Brand
            </Typography>
            <Box>
              <IconButton color="warning" onClick={updateCollectionByNameId}>
                <SaveAsIcon />
              </IconButton>
              <IconButton color="warning" onClick={() => setIsAddDetail(false)}>
                <HighlightOffIcon />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              pb: 5,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <TextField
              size="small"
              label="Link canonical"
              value={collectionData?.canonical}
              onChange={e => {
                const setCloneCollection = {
                  ...collectionData,
                  canonical: e.target.value,
                };
                setCollectionData(setCloneCollection);
              }}
            />
            <TextField
              size="small"
              label="mô tả"
              value={collectionData?.description}
              onChange={e => {
                const setCloneCollection = {
                  ...collectionData,
                  description: e.target.value,
                };
                setCollectionData(setCloneCollection);
              }}
            />
            <TextField
              size="small"
              label="main-keyword"
              value={collectionData?.mainKeyword}
              onChange={e => {
                const setCloneCollection = {
                  ...collectionData,
                  mainKeyword: e.target.value,
                };
                setCollectionData(setCloneCollection);
              }}
            />
            <TextField
              size="small"
              label="second-keyword"
              value={collectionData?.keyword}
              onChange={e => {
                const setCloneCollection = {
                  ...collectionData,
                  keyword: e.target.value,
                };
                setCollectionData(setCloneCollection);
              }}
            />
            <Box>
              <input
                id="about-us-banner"
                name="file"
                type="file"
                onChange={event => {
                  const image = {
                    name: '',
                    file: undefined,
                    url: '',
                  };

                  image.file = get(event, 'currentTarget.files[0]');
                  if (image.file) {
                    const reader = new FileReader();
                    reader.readAsDataURL(image.file);
                    reader.onload = async () => {
                      set(image, 'url', reader.result);
                      set(image, 'name', get(image, 'file.name', ''));
                      // const uploadInfo = await s3Client.uploadFile(
                      //   `phuonglinh/homeImage/${Date.now()}-${image.name}`,
                      //   image.file,
                      // );
                      // image.url = get(uploadInfo, 'Location');
                      image.url = await uploadFiles([image.file], 'brand', '');
                      // const upload = UploadImg(image);
                      // console.log('upload: ', uploadInfo);
                      setCollectionImages([{ url: image.url }]);
                    };
                  }
                  event.preventDefault();
                }}
                className="form-control"
              />
              <Card
                sx={{
                  width: 328,
                  height: 228,
                }}
              >
                <img
                  src={collectionImages?.[0]?.url}
                  alt=""
                  loading="lazy"
                  width={'200px'}
                  height={'auto'}
                />
              </Card>
            </Box>
            <Box>
              <Typography sx={{ fontWeight: 'bold' }}>
                Hình ảnh headers
              </Typography>
              <input
                id="about-us-banner"
                name="file"
                type="file"
                onChange={event => {
                  const image = {
                    name: '',
                    file: undefined,
                    url: '',
                  };

                  image.file = get(event, 'currentTarget.files[0]');
                  if (image.file) {
                    const reader = new FileReader();
                    reader.readAsDataURL(image.file);
                    reader.onload = async () => {
                      set(image, 'url', reader.result);
                      set(image, 'name', get(image, 'file.name', ''));
                      // const uploadInfo = await s3Client.uploadFile(
                      //   `phuonglinh/homeImage/${Date.now()}-${image.name}`,
                      //   image.file,
                      // );
                      // image.url = get(uploadInfo, 'Location');
                      image.url = await uploadFiles([image.file], 'brand', '');
                      // const upload = UploadImg(image);
                      // console.log('upload: ', uploadInfo);
                      setCollectionHeadersImages({ url: image.url });
                    };
                  }
                  event.preventDefault();
                }}
                className="form-control"
              />
              <Card
              // sx={{
              //   width: 328,
              //   height: 228,
              // }}
              >
                <img
                  src={collectionHeadersImages?.url}
                  alt=""
                  loading="lazy"
                  style={{ width: '100%', objectFit: 'cover' }}
                />
              </Card>
            </Box>
            <JoditEditor
              value={data}
              config={editorConfigJodit}
              onChange={value => setData(value)}
            />
          </Box>
        </DialogContent>
      </MuiDialog>
    );
  };

  const dialogCloseAction = () => {
    setEditProductDialog(false);
    setCurrentProduct(null);
    setCollection(null);
    setIsLoading(!isLoading);
  };

  // Get Collection by ID name
  const getCollectionByNameId = (nameId: string | null) => {
    const apiHandler = async () => {
      try {
        const collection: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands/${nameId}`,
          // { select_detail: true },
        );

        // console.log('collection: ', collection);
        setCollectionData(collection);
        setCollectionImages(collection?.images);
        setCollectionHeadersImages(collection?.headersImage);
        setData(collection?.detail);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  // update Collection by ID name
  const updateCollectionByNameId = () => {
    const dataUpdate = {
      detail: data,
      description: collectionData?.description,
      canonical: collectionData?.canonical,
      mainKeyword: collectionData?.mainKeyword,
      keyword: collectionData?.keyword,
      images: collectionImages,
      headersImage: collectionHeadersImages,
    };
    const apiHandler = async () => {
      try {
        const collection: any = await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/brands/${brand || ''}`,
          omitBy(dataUpdate, isNil),
        );
        setIsAddDetail(false);
        // console.log(collection);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  function CustomSeparator() {
    const breadcrumbs = [
      <Link
        underline="hover"
        key="1"
        color="black"
        // href="/"
        onClick={() => navigate('/')}
        sx={{
          cursor: 'pointer',
          fontWeight: 'bold',
          [theme.breakpoints.down('md')]: {
            fontSize: '12px',
          },
        }}
      >
        Trang chủ
      </Link>,
      <Link
        underline="hover"
        key="2"
        color="black"
        href="/san-pham"
        onClick={() => {
          if (brand || path || keyword) navigate(`/san-pham`);
        }}
        sx={{
          cursor: 'pointer',
          fontWeight: 'bold',
          [theme.breakpoints.down('md')]: {
            fontSize: '12px',
          },
          // fontWeight:
          //   !brand && !path && !keyword && !category ? 'bold' : 'normal',
        }}
      >
        Trang sản phẩm
      </Link>,
      brand ? (
        <Link
          underline="hover"
          key="3"
          sx={{
            color: 'text.primary',
            p: 0,
            lineHeight: 2,
            // fontWeight: brand ? 'bold' : 'normal',
            textTransform: 'capitalize',
            fontWeight: 'bold',
            [theme.breakpoints.down('md')]: {
              fontSize: '12px',
            },
          }}
        >
          {brand}
        </Link>
      ) : (
        ''
      ),
      category ? (
        <Link
          underline="hover"
          key="3"
          sx={{
            color: 'text.primary',
            p: 0,
            lineHeight: 2,
            fontWeight: 'bold',
            // fontWeight: category ? 'bold' : 'normal',
            textTransform: 'capitalize',
            [theme.breakpoints.down('md')]: {
              fontSize: '12px',
            },
          }}
        >
          {category.replace('-', ' ')}
        </Link>
      ) : (
        ''
      ),
      keyword ? (
        <Link
          underline="hover"
          key="3"
          sx={{
            color: 'text.primary',
            p: 0,
            lineHeight: 2,
            // fontWeight: keyword ? 'bold' : 'normal',
            textTransform: 'capitalize',
            [theme.breakpoints.down('md')]: {
              fontSize: '12px',
            },
          }}
        >
          {keyword}
        </Link>
      ) : (
        ''
      ),
    ];
    return (
      <Stack spacing={2}>
        <Breadcrumbs
          separator="›"
          aria-label="breadcrumb"
          sx={{
            display: 'flex',
            alignItems: 'center',
            textAlign: 'center',
            my: 'auto',
          }}
        >
          {breadcrumbs}
        </Breadcrumbs>
      </Stack>
    );
  }

  return (
    <>
      <Helmet>
        <title>Sản phẩm</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Backdrop
        sx={theme => ({ color: '#fff', zIndex: theme.zIndex.drawer + 1 })}
        open={openBackdrop}
        onClick={handleCloseBackdrop}
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          overflow: 'hidden',
          // position: 'relative',
        }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: '90%',
            display: 'flex',
            justifyContent: 'start',
            py: 3,
            [theme.breakpoints.down('md')]: {
              py: 1,
            },
          }}
        >
          <CustomSeparator />
        </Box>

        <Box
          sx={{
            display: 'flex',
            width: '90%',
            bgcolor: '',
            [theme.breakpoints.down('md')]: {
              width: '100%',
              flexDirection: 'column',
            },
          }}
        >
          <Box
            sx={{
              // bgcolor: 'red',
              display: 'flex',
              flex: 5,
              // width: '50%',
              py: 2,
              flexDirection: 'column',
              gap: 1,
              [theme.breakpoints.down('md')]: {
                width: '100%',
                // p: 1,
              },
            }}
          >
            {/* <Box
              sx={{
                overflow: 'auto',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  minWidth: '1000px',
                  gap: 3,
                  p: 1,
                  overflow: 'auto',
                  [theme.breakpoints.down('sm')]: {
                    display: 'none',
                  },
                }}
              >
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    fontSize: '40px',
                    fontWeight: 'bold',
                    [theme.breakpoints.down('md')]: {
                      fontSize: '20px',
                    },
                    [theme.breakpoints.down('sm')]: {
                      fontSize: '14px',
                    },
                  }}
                >
                  Tất cả sản phẩm
                </Typography>
                <Box
                  sx={{
                    width: 'fit-content',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 3,
                    p: 1,
                    ml: 'auto',
                  }}
                >
                  <Typography>Hiển thị</Typography>
                  <TextField
                    id="outlined-select-limit"
                    select
                    size="small"
                    value={limit}
                    onChange={e => {
                      const limit = e.target.value;
                      setLimit(Number(limit));
                      searchParams.set('limit', String(limit));
                      navigate({ search: searchParams.toString() });
                    }}
                  >
                    <MenuItem value={12}>{12}</MenuItem>
                    <MenuItem value={24}>{24}</MenuItem>
                    <MenuItem value={36}>{36}</MenuItem>
                  </TextField>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'start',
                    gap: 2,
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                      fontWeight: page === 1 ? 'bold' : 'normal',
                      textDecoration: 'underline',
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      setPage(1);
                      searchParams.set('page', '1');
                      navigate({ search: searchParams.toString() });
                    }}
                  >
                    Đầu
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                      cursor: 'pointer',
                      '&: hover': {
                        fontWeight: 'bold',
                      },
                    }}
                    onClick={() => {
                      if (page > 1) {
                        setPage(page - 1);
                        searchParams.set('page', String(page - 1));
                        navigate({ search: searchParams.toString() });
                      }
                    }}
                  >
                    <NavigateBeforeIcon />
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                    }}
                    // onClick={() => {
                    //   setPage(2);
                    // }}
                  >
                    {page}
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                      cursor: 'pointer',
                      '&: hover': {
                        fontWeight: 'bold',
                      },
                    }}
                    onClick={() => {
                      if (page < lastPage) {
                        setPage(page + 1);
                        searchParams.set('page', String(page + 1));
                        navigate({ search: searchParams.toString() });
                      }
                    }}
                  >
                    <NavigateNextIcon />
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'inherit',
                      fontWeight: page === lastPage ? 'bold' : 'normal',
                      textDecoration: 'underline',
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      if (page < lastPage) {
                        setPage(lastPage);
                        searchParams.set('page', String(lastPage));
                        navigate({ search: searchParams.toString() });
                      }
                    }}
                  >
                    Cuối
                  </Typography>
                </Box>
              </Box>
            </Box> */}
            <Box
              sx={{
                flexGrow: 5,
                pr: 2,
                [theme.breakpoints.down('md')]: {
                  width: '100%',
                  pr: 0,
                },
              }}
            >
              <Grid
                container
                spacing={{ xs: 2, sm: 10, md: 5, lg: 10, xl: 10 }}
                columns={{ xs: 2, sm: 2, md: 3, lg: 4, xl: 4 }}
                sx={{
                  py: 3,
                  mx: 'auto',
                  justifyContent: 'start',
                  [theme.breakpoints.down('md')]: {
                    justifyContent: 'center',
                  },
                }}
              >
                {products
                  // ?.sort(
                  //   (a: any, b: any) =>
                  //     Number(new Date(b.updatedAt)) -
                  //     Number(new Date(a.updatedAt)),
                  // )
                  .map((product: any = {}, index: any) => (
                    <Grid
                      xs={1}
                      sm={1}
                      md={1}
                      lg={1}
                      key={index}
                      // sx={{
                      //   display: index < preview ? '' : 'none',
                      // }}
                    >
                      {/* <Link href={`/san-pham/${product?.id}`}> */}
                      <Card
                        key={index}
                        onClick={() => {
                          navigate(`/san-pham/${product?.id}`, {
                            state: { product },
                          });
                          updateRecently(product?._id);
                        }}
                        onMouseEnter={() => (product.hover = true)}
                        onMouseLeave={() => (product.hover = false)}
                        sx={{
                          maxWidth: '300px',
                          height: '460px',
                          minWidth: '250px',
                          background: '',
                          borderRadius: 5,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mx: 'auto',
                          // overflow: 'hidden',
                          position: 'relative',
                          cursor: 'pointer',
                          [theme.breakpoints.down('sm')]: {
                            maxWidth: '200px',
                            minWidth: '170px',
                            height: '280px',
                          },
                          // boxShadow:
                          //   '0px 0px 2px 0.5px rgba(0,0,0,0.05), 0px 0px 2px 0.5px rgba(0,0,0,0.05)',
                          '&:hover': {
                            bgcolor: 'white',
                            transition:
                              'box-shadow .2s ease-in-out,background .5s ease-in-out',
                            '&:hover': {},
                            boxShadow:
                              '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                          },
                        }}
                      >
                        <Box
                          sx={{
                            position: 'absolute',
                            width: '100%',
                            display: isAdmin || isCollab ? '' : 'none',
                          }}
                        >
                          <MoreAction
                            color="black"
                            add={false}
                            edit={true}
                            remove={true}
                            onClickEdit={() => {
                              handleProductAdd(product);
                            }}
                            onClickRemove={() => {
                              handleProductRemove(product);
                            }}
                          />
                        </Box>

                        <FavoriteBorderIcon
                          sx={{
                            position: 'absolute',
                            right: 10,
                            top: 10,
                          }}
                        />
                        <Typography
                          sx={{
                            mr: 'auto',
                            p: 2,
                            fontSize: '14px',
                            fontWeight: 'bold',
                            color: 'gray',
                            fontFamily: 'inherit',
                            textTransform: 'capitalize',
                            [theme.breakpoints.down('sm')]: {
                              fontSize: '12px',
                            },
                          }}
                        >
                          {product.categories[0]?.name || ''}
                        </Typography>
                        <Box
                          sx={{
                            width: '250px',
                            height: '250px',
                            display: 'flex',
                            justifyContent: 'center',
                            borderRadius: 3,
                            '&:hover': {},
                            [theme.breakpoints.down('sm')]: {
                              width: '100px',
                              height: '100px',
                            },
                          }}
                        >
                          <img
                            src={product.images?.[0]?.url}
                            alt={product.name}
                            width={'100%'}
                            height={'100%'}
                            loading="lazy"
                          />
                        </Box>
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'start',
                            justifyContent: 'end',
                            p: 3,
                            // position: 'absolute',
                            // background: 'black'
                          }}
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                            }}
                          >
                            <Typography
                              sx={{
                                display: product.categories.some(
                                  item => item.id === 'san-pham-moi',
                                )
                                  ? ''
                                  : 'none',
                                width: 'fit-content',
                                ml: 2,
                                py: 0.5,
                                px: 1,
                                fontFamily: 'inherit',
                                fontSize: '12px',
                                background:
                                  'linear-gradient(to right, #19d7e4, #42b4f6)',
                                borderRadius: 2,
                                color: 'white',
                              }}
                            >
                              Sản phẩm mới
                            </Typography>
                            <Typography
                              className="two-line-typography"
                              sx={{
                                py: 1,
                                pb: 0.5,
                                px: 2,
                                fontFamily: 'inherit',
                                fontWeight: 'bold',
                                [theme.breakpoints.down('sm')]: {
                                  px: 0,
                                  fontSize: 14,
                                },
                                // color: bestIndex === index ? 'red' : '',
                              }}
                            >
                              {product.name}
                            </Typography>
                            <Typography
                              sx={{
                                color: 'red',
                                fontWeight: 'bold',
                                fontFamily: 'inherit',
                                py: 1,
                                px: 2,
                                [theme.breakpoints.down('sm')]: {
                                  fontSize: 12,
                                },
                                // fontSize: '20px',
                              }}
                            >
                              {product.priceOrigin === 0
                                ? 'Liên hệ'
                                : formatCurrency(product.priceOrigin)}
                            </Typography>
                          </Box>
                        </Box>
                      </Card>
                      {/* </Link> */}
                    </Grid>
                  ))}
                <Grid xs={1} sm={1} md={1} lg={1}>
                  <Button
                    onClick={handleProductAdd}
                    sx={{
                      display: isAdmin || isCollab ? 'flex' : 'none',
                      width: '100%',
                      height: '100%',
                      maxWidth: '200px',
                      color: 'black',
                      borderRadius: 3,
                    }}
                  >
                    <ListItemIcon>
                      <QueueIcon fontSize="large" />
                    </ListItemIcon>
                  </Button>
                </Grid>
              </Grid>
              <Box
                sx={{
                  [theme.breakpoints.up('sm')]: {
                    display: 'none',
                  },
                  width: 'fit-content',
                  ml: 'auto',
                }}
              >
                <Pagination
                  size="small"
                  count={lastPage}
                  page={page}
                  siblingCount={0}
                  onChange={(
                    event: React.ChangeEvent<unknown>,
                    value: number,
                  ) => {
                    setPage(value);
                    searchParams.set('page', String(value));
                    navigate({ search: searchParams.toString() });
                    scrollToTop(100);
                  }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: 3,
                p: 1,
                mr: 2,
                overflow: 'auto',
                [theme.breakpoints.down('sm')]: {
                  display: 'none',
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 3,
                  p: 1,
                  ml: 'auto',
                }}
              >
                <Typography>Hiển thị</Typography>
                <TextField
                  id="outlined-select-limit"
                  select
                  size="small"
                  value={limit}
                  onChange={e => {
                    const limit = e.target.value;
                    setLimit(Number(limit));
                    setPage(1);
                    searchParams.set('page', '1');
                    searchParams.set('limit', String(limit));
                    navigate({ search: searchParams.toString() });
                    scrollToTop(50);
                  }}
                >
                  <MenuItem value={12}>{12}</MenuItem>
                  <MenuItem value={24}>{24}</MenuItem>
                  <MenuItem value={36}>{36}</MenuItem>
                </TextField>
              </Box>
              {/* <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'start',
                  gap: 2,
                }}
              >
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    fontWeight: page === 1 ? 'bold' : 'normal',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    setPage(1);
                    searchParams.set('page', '1');
                    navigate({ search: searchParams.toString() });
                  }}
                >
                  Đầu
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    '&: hover': {
                      fontWeight: 'bold',
                    },
                  }}
                  onClick={() => {
                    if (page > 1) {
                      setPage(page - 1);
                      searchParams.set('page', String(page - 1));
                      navigate({ search: searchParams.toString() });
                    }
                  }}
                >
                  <NavigateBeforeIcon />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                  }}
                  // onClick={() => {
                  //   setPage(2);
                  // }}
                >
                  {page}
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    '&: hover': {
                      fontWeight: 'bold',
                    },
                  }}
                  onClick={() => {
                    if (page < lastPage) {
                      setPage(page + 1);
                      searchParams.set('page', String(page + 1));
                      navigate({ search: searchParams.toString() });
                    }
                  }}
                >
                  <NavigateNextIcon />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                    fontWeight: page === lastPage ? 'bold' : 'normal',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    if (page < lastPage) {
                      setPage(lastPage);
                      searchParams.set('page', String(lastPage));
                      navigate({ search: searchParams.toString() });
                    }
                  }}
                >
                  Cuối
                </Typography>
              </Box> */}
              <Stack spacing={2}>
                {/* <Typography>Page: {page}</Typography> */}
                <Pagination
                  count={lastPage}
                  page={page}
                  onChange={(
                    event: React.ChangeEvent<unknown>,
                    value: number,
                  ) => {
                    setPage(value);
                    searchParams.set('page', String(value));
                    navigate({ search: searchParams.toString() });
                    scrollToTop(50);
                  }}
                />
              </Stack>
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            display: brand ? 'flex' : 'none',
            gap: 1,
            width: '90%',
            bgcolor: '',
            overflow: 'hidden',
            [theme.breakpoints.down('md')]: {
              width: '100%',
              flexDirection: 'column',
            },
          }}
        >
          <Box
            sx={{
              flex: 2,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <Paper
              // elevation={3}
              sx={{
                display: data ? 'flex' : 'none',
                // position: 'relative',
                flexDirection: 'column',
                justifyContent: 'start',
                alignItems: 'center',
                width: '100%',
                height: maxHeight,
                ml: 0.5,
                my: 0.5,
                p: 2,
                // borderRadius: 5,
                overflow: 'hidden',
                // boxShadow:
                //   '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  dispaly: collectionHeadersImages?.url ? 'block' : 'none',
                }}
              >
                <img
                  src={collectionHeadersImages?.url || ''}
                  alt="brand"
                  loading="lazy"
                  style={{ width: '100%', objectFit: 'cover' }}
                />
              </Box>
              <Box
                dangerouslySetInnerHTML={createMarkup(data)}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                  height: '100%',
                  // px: 2,
                  [theme.breakpoints.down('sm')]: {},
                }}
              />
            </Paper>
            <Button
              variant="outlined"
              sx={{
                display: maxHeight !== '100%' && data ? 'flex' : 'none',
                gap: 1,
                textTransform: 'none',
                position: 'absolute',
                bottom: 15,
                mx: 'auto',
                color: 'white',
                bgcolor: '#00ccffbd',
                borderRadius: 8,
                animation: `${upUp} 1s ease-in-out infinite alternate`,
                [theme.breakpoints.up('sm')]: {
                  fontSize: '20px',
                },
                '&:hover': {
                  bgcolor: '#00ccff',
                  // color: 'black',
                },
              }}
              onClick={() => {
                setMaxHeight('100%');
              }}
            >
              Xem thêm
              <ArrowCircleDownIcon fontSize="small" />
            </Button>
            <Box
              sx={{
                display: isAdmin || isCollab ? 'flex' : 'none',
                flexDirection: 'column',
                alignItems: 'center',
                position: '',
                width: '100%',
                bottom: 0,
                background: '',
              }}
            >
              <IconButton
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textTransform: 'none',
                  background: 'white',
                  color: 'gray',
                  // boxShadow:
                  //   '0px 0px 20px 2px rgba(0, 0, 0, 0.05), inset 0px 0px 2px 2px rgba(0, 0, 0, 0.05)',
                }}
                onClick={() => {
                  setIsAddDetail(true);
                }}
              >
                <QueueIcon fontSize="small" />{' '}
              </IconButton>
            </Box>
          </Box>
          {/* <Paper
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              height: 'fit-content',
              mr: 0.5,
              my: 0.5,
              p: 2,
            }}
          >
            <News />
          </Paper> */}
        </Box>
        {editProductDialog && (
          <AddProductDialog
            params={{
              collection,
              product: currentProduct,
            }}
            onClose={dialogCloseAction}
            open={editProductDialog}
          />
        )}
        {/* <Recently /> */}
        {AddDetailDialog()}
      </Paper>
    </>
  );
}
