import Box from '@mui/material/Box';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
// import { Helmet } from 'react-helmet';
import { Person } from 'schema-dts';
import { helmetJsonLdProp } from 'react-schemaorg';
import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import Card from '@mui/material/Card';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { ImQuotesLeft } from 'react-icons/im';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import ListItemIcon from '@mui/material';
import { useMediaQuery } from '@mui/material';
import get from 'lodash/get';
import set from 'lodash/set';
import { request } from 'utils/request';

import CoreValues from './AboutUs';

import banner1 from 'assets/images/banner/Banner-lead.jpg';
import banner2 from 'assets/images/event/banner_event-1.webp';
import banner3 from 'assets/images/banner/banner_5.webp';
import banner4 from 'assets/images/banner/Banner-slide1.jpg';
import banner5 from 'assets/images/banner/Banner-slide2.jpg';
import productType1 from 'assets/images/product/type/product_type_1.webp';
import productType2 from 'assets/images/product/type/product_type_2.webp';
import productType3 from 'assets/images/product/type/product_type_3.jpeg';
import productType4 from 'assets/images/product/type/product_type_4.webp';
import productType5 from 'assets/images/product/type/product_type_5.webp';

import { useUtilsSlice } from 'app/slice/utils';
import { useUserSlice } from 'app/slice/user';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import { uploadFiles } from 'utils/uploadImg';

import About1 from 'assets/images/about/banner-ve-chung-toi-phuong-linh-jsc-1920x1000.jpg';
import About2 from 'assets/images/about/banner-tam-nhin-phuong-linh-jsc-1920x840.png';
import About3 from 'assets/images/about/banner-su-menh-phuong-linh-jsc-1920x840.png';

const aboutUsContent =
  'PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng của mình những sản phẩm và dịch vụ chất lượng tốt nhất. Cam kết với đối tác sự uy tín và tôn trọng đồng thời không ngừng gia tăng giá trị qua thời gian. Giá trị cốt lõi của PhuongLinhJSC là xây dựng một doanh nghiệp đầu ngành, tạo ra sản phẩm hữu ích phục vụ khách hàng đồng thời mang lại nhiều việc làm đóng góp cho xã hội. \n PhuongLinhJSC luôn nỗ lực, cống hiến để mang đến những trải nghiệm tốt nhất trong lĩnh vực âm thanh, nghe nhìn cho khách hàng của mình. Chúng tôi luôn xem trải nghiệm khách hàng và lợi ích của đối tác là trọng tâm cho mọi xây dựng, phát triển của công ty.';

export function AboutPage() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: userAction } = useUserSlice();

  const userInfo = useSelector(userInfoSelector);

  const [openEdit, setOpenEdit] = React.useState<boolean>(false);
  const [bannerReward, setBannerReward] = React.useState<any>([]);
  const [newProductList, setNewProductList] = React.useState<any[]>([]);
  const [collectionList, setCollectionList] = React.useState<any[]>([]);
  const [brandList, setBreandList] = React.useState<any[]>([]);
  const [categoryList, setCategoryList] = React.useState<any[]>([]);
  const [partnerList, setPartnerList] = React.useState<any[]>([]);
  const [partnerImages, setPartnerImages] = React.useState<any[]>([]);
  const [collectionImageList, setCollectionImageList] = React.useState<any[]>(
    [],
  );
  const [imageIndex, setImageIndex] = React.useState<number>(-1);
  const [us, setUs] = React.useState<any>({
    aboutUs: {
      content: aboutUsContent,
      imageUrl: '',
    },
    whyUs: [],
  });

  const [middleBanner, setMidderBanner] = React.useState<any>({
    imageUrl: banner3,
    title: 'Home Appliances',
    subTitle: 'Decorate and heal your life.',
  });

  const [typeIndex, setTypeIndex] = React.useState<any>();
  const [type1, setType1] = React.useState<any>({
    background: productType1,
    title: 'Gia Dụng',
  });
  const [type2, setType2] = React.useState<any>({
    background: '',
    title: 'Vật Dụng Cá Nhân',
  });
  const [type3, setType3] = React.useState<any>({
    background: productType3,
    title: 'Phụ Kiện Di Động',
  });
  const [type4, setType4] = React.useState<any>({
    background: productType4,
    title: 'Phụ Kiện Để Bàn',
  });
  const [type5, setType5] = React.useState<any>({
    background: productType5,
    title: 'Phụ Kiện Thời Trang',
  });

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [homeSchema, setHomeSchema] = React.useState<any>({});
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setHomeSchema(result);
        setBannerReward(result?.banner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchBrands = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'BRAND' },
        );

        // console.log('brands: ', result);
        setBreandList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchCategories = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'CATEGORY' },
        );

        // console.log('CATEGORY: ', result);
        setCategoryList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    fetchHomePage();
    fetchBrands();
    fetchCategories();
  }, []);

  React.useEffect(() => {
    const {
      banner,
      us,
      middleBanner,
      categoryList,
      newProducts,
      collectionList,
      slidePartner,
    } = homeSchema;
    if (banner && banner.length > 0) {
      setBannerReward(banner[0]);
    }

    if (slidePartner && slidePartner.length > 0) {
      const newImages = slidePartner?.map(item => {
        return { url: item.imageUrl };
      });
      setPartnerImages(newImages);
    }

    if (us) {
      setUs(us);
    }

    if (middleBanner) {
      setMidderBanner(middleBanner);
    }

    if (slidePartner) {
      setPartnerList(slidePartner);
    }

    if (newProducts && newProducts.length) {
      setNewProductList(newProducts);
    }

    if (collectionList && collectionList.length) {
      // console.log('collectionList: ', collectionList)
      setCollectionList([
        collectionList.filter((item: any) => !get(item, 'imageUrl'))[0],
      ]);

      setCollectionImageList(
        collectionList.filter((item: any) => get(item, 'imageUrl')),
      );
    }
  }, [homeSchema]);

  const onClose = () => {
    setOpenEdit(false);
  };
  const handleEditHome = async () => {
    try {
      const newPartnerList = await Promise.all(
        partnerImages.map(async (image: any) => {
          let { file, name, url = '' } = image;
          if (file && name) {
            // const uploadInfo = await s3Client.uploadFile(
            //   `phuonglinh/partnerImage/${Date.now()}-${name}`,
            //   file,
            // );

            // url = get(uploadInfo, 'Location');
            url = await uploadFiles([file], 'partner', '');
          }
          return { imageUrl: url, content: '', href: '' };
        }),
      );
      const newHomeSchema = { ...homeSchema, slidePartner: newPartnerList };
      setHomeSchema(newHomeSchema);
      const result: any = await request(
        'put',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/upsert`,
        newHomeSchema,
      );

      // console.log({ result });
      setHomeSchema(result);
      setOpenEdit(false);
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const vietnameseToAscii = inputStr => {
    // Chuyển đổi từ tiếng Việt có dấu sang không dấu
    var convertedStr = inputStr
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Thay đổi dấu cách thành '-'
    convertedStr = convertedStr.replace(/\s+/g, '-');

    return convertedStr;
  };

  const labelOptions = [
    {
      value: 'phone',
      label: 'phone',
    },
    {
      value: 'email',
      label: 'email',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Trang chủ</title>
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          fontFamily: 'inherit',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '1000px',
            display: 'flex',
            // flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'start',
            gap: 3,
            pl: '200px',
            background: `url("${About1}")`,
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            pt: '300px',
            fontSize: '60px',
            position: 'relative',
            [theme.breakpoints.down('md')]: {
              px: '100px',
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: '30px',
              px: '40px',
            },
          }}
        >
          <Typography
            sx={{
              color: 'white',
              [theme.breakpoints.down('sm')]: {
                display: 'none',
              },
            }}
          >
            <ImQuotesLeft fontSize={60} />
          </Typography>
          <Typography
            sx={{
              color: 'white',
              [theme.breakpoints.up('sm')]: {
                display: 'none',
              },
            }}
          >
            <ImQuotesLeft fontSize={30} />
          </Typography>
          <Typography
            sx={{
              width: '650px',
              textTransform: 'uppercase',
              fontSize: 'inherit',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Về công ty phương linh jsc
          </Typography>
        </Box>
        <Box
          sx={{
            width: '100%',
            height: '800px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'start',
            gap: 1,
            pl: '200px',
            background: `url("${About2}")`,
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            pt: '300px',
            position: 'relative',
            [theme.breakpoints.down('md')]: {
              pl: '100px',
            },
            [theme.breakpoints.down('sm')]: {
              px: 1,
            },
          }}
        >
          <Typography
            sx={{
              // width: '650px',
              fontSize: '30px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Tầm nhìn
          </Typography>
          <Typography
            sx={{
              // width: '650px',
              fontSize: '50px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
              [theme.breakpoints.down('sm')]: {
                fontSize: '40px',
              },
            }}
          >
            Dẫn đầu thị trường
          </Typography>
          <Typography
            sx={{
              width: '650px',
              // fontSize: '50px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
              [theme.breakpoints.down('sm')]: {
                width: '100%',
              },
            }}
          >
            Trở thành công ty nhập khẩu và phân phối sản phẩm công nghệ hàng đầu
            trong khu vực, cung cấp những sản phẩm tiên tiến và chất lượng đến
            tay người tiêu dùng.
          </Typography>
          <Typography
            sx={{
              width: '200px',
              p: 0.5,
              borderRadius: 3,
              bgcolor: 'white',
            }}
          ></Typography>
        </Box>
        <Box
          sx={{
            width: '100%',
            height: '800px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'end',
            justifyContent: 'start',
            gap: 3,
            pr: '200px',
            background: `url("${About3}")`,
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            pt: '300px',
            position: 'relative',
            [theme.breakpoints.down('md')]: {
              pr: '100px',
            },
            [theme.breakpoints.down('sm')]: {
              px: 1,
            },
          }}
        >
          <Typography
            sx={{
              // width: '650px',
              fontSize: '30px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: '#1a3a58',
            }}
          >
            Sứ mệnh
          </Typography>
          <Typography
            sx={{
              // width: '650px',
              fontSize: '50px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: '#1a3a58',
              textAlign: 'right',
              [theme.breakpoints.down('sm')]: {
                fontSize: '40px',
              },
            }}
          >
            Nâng cao chất lượng cuộc sống
          </Typography>
          <Typography
            sx={{
              width: '650px',
              // fontSize: '50px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: '#1a3a58',
              [theme.breakpoints.down('sm')]: {
                width: '100%',
              },
            }}
          >
            Đóng góp vào việc nâng cao chất lượng cuộc sống của người tiêu dùng
            thông qua việc cung cấp các sản phẩm công nghệ tiện ích, hỗ trợ công
            việc, giải trí và kết nối hàng ngày.
          </Typography>
          <Typography
            sx={{
              width: '200px',
              p: 0.5,
              borderRadius: 3,
              bgcolor: '#1a3a58',
            }}
          ></Typography>
        </Box>
        <CoreValues />
      </Paper>
    </>
  );
}
