import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography, Card } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Grid from '@mui/material/Unstable_Grid2';
import { theme } from 'app/components/AppWrapper/theme';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { keyframes } from '@emotion/react';
import { useNavigate } from 'react-router-dom';

import value1 from 'assets/images/about/banner-gia-tri-cot-loi-1-phuong-linh-jsc-300x300.png';
import value2 from 'assets/images/about/banner-gia-tri-cot-loi-2-phuong-linh-jsc-300x300.png';
import value3 from 'assets/images/about/banner-gia-tri-cot-loi-3-phuong-linh-jsc-300x300.png';
import value4 from 'assets/images/about/banner-gia-tri-cot-loi-4-phuong-linh-jsc-300x300.png';

const initCorevalues = [
  {
    background: value1,
    href: '',
    title: 'Đổi mới',
    content:
      'Luôn tìm kiếm, cập nhật và phân phối các công nghệ tiên tiến nhất',
    bestSaleId: '',
  },
  {
    background: value2,
    href: '',
    title: 'Hợp tác',
    content:
      'Đề cao tinh thần đồng đội và hợp tác với đối tác để cùng đạt mục tiêu chung',
    bestSaleId: '',
  },
  {
    background: value3,
    href: '',
    title: 'Phát triển bền vững',
    content:
      'Hướng đến sự phát triển dài hạn, bền vững và vững mạnh trong tương lai',
    bestSaleId: '',
  },
  {
    background: value4,
    href: '',
    title: 'Minh bạch',
    content:
      'Xây dựng niềm tin với khách hàng và đối tác dựa trên sự chính trực',
    bestSaleId: '',
  },
];

const animation1 = keyframes`
  0% {
    transform: translate(-30px, -30px)
  }
  50% {
    transform: translate(-10px, -20px)
  }
  100% {
    transform: translate(0px, 0px)
  }
`;
const animation2 = keyframes`
  0% {
    transform: translate(0px, 0px) rotate(0deg)
  }
  50% {
    transform: translate(-20px, -20px) rotate(25deg)
  }
  100% {
    transform: translate(-30px, -30px) rotate(45deg)
  }
`;

interface Props {
  title?: string;
  buttonTitle?: string;
  banner?: string;
  content?: string;
  href?: string;
  list?: any[];
}

const CoreValues: React.FC<Props> = ({ banner, content, href, list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [whyUsIndex, setWhyUsIndex] = React.useState<number>(1);
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [coreValues, setCoreValues] = React.useState<any[]>(initCorevalues);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        alignItems: 'start',
        gap: 3,
        my: 3,
      }}
    >
      {coreValues.map(item => (
        <Box
          key={item.title}
          sx={{
            width: 300,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'start',
            alignItems: 'center',
            gap: 5,
            [theme.breakpoints.down('sm')]: {
              mx: 'auto',
            },
          }}
        >
          <Box>
            <img src={item.background} alt={item.title} loading="lazy" />
          </Box>
          <Typography
            sx={{
              fontSize: '30px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              textAlign: 'center',
              color: '#1a3a58',
            }}
          >
            {item.title}
          </Typography>
          <Typography
            sx={{
              // fontSize: '30px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              textAlign: 'center',
              color: '#1a3a58',
            }}
          >
            {item.content}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default CoreValues;
