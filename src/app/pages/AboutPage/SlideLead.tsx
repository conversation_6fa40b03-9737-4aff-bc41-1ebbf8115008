import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import {
  Navigation,
  Pagination,
  Autoplay,
  Mousewheel,
  EffectFlip,
  EffectCoverflow,
} from 'swiper/modules';
import { Box, Button, Card, Typography } from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';

import banner1 from 'assets/images/banner/Banner-slide1.jpg';
import banner2 from 'assets/images/banner/Banner-slide2.jpg';
import banner3 from 'assets/images/banner/Banner-slide3.jpg';
import banner4 from 'assets/images/banner/Banner-slide4.jpg';
import { request } from 'utils/request';
import { get } from 'lodash';

const InitListBanners = [
  {
    background: banner1,
    href: 'banner-1',
  },
  {
    background: banner2,
    href: 'banner-2',
  },
  {
    background: banner3,
    href: 'banner-3',
  },
  {
    background: banner4,
    href: 'banner-4',
  },
];

interface Props {
  list?: any;
  bannerRightTop?: any;
  bannerRightBottom?: any;
}

type Collection = {
  _id?: any;
  id?: string;
  name?: string;
  description?: string;
  parentIds?: any[];
  type?: string;
  isTag?: boolean;
  allowEdit?: boolean;
  collections?: Collection[];
};

const SlideLead: React.FC<Props> = ({
  list,
  bannerRightTop,
  bannerRightBottom,
}) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [previewShow, setPreviewShow] = React.useState<number>(3);
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [isCenter, setIsCenter] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const [menuTree, setMenuTree] = React.useState<Collection[]>([]);

  // TODO: get list banners from api
  const [banners, setBanners] = React.useState<any[]>(InitListBanners);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = () => {};

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
    if (width <= 900 && previewShow !== 1) {
      setPreviewShow(1);
      setIsCenter(true);
    } else if (width > 900 && previewShow === 1) {
      setPreviewShow(3);
      setIsCenter(false);
    }
  }, [width]);

  useEffect(() => {
    const apiMenuTreeHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections/menu-tree`,
        );
        const newMenuTree = [...result];
        newMenuTree.sort((a, b) => {
          return a.name.localeCompare(b.name);
        });
        setMenuTree(newMenuTree);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    apiMenuTreeHandler();
  }, []);

  const handleClickCollection = (collection: Collection, params: any = {}) => {
    const id = get(collection, 'id', '');
    let uri = `/bo-suu-tap/${id}`;

    // if (!isEmpty(id)) {
    //   setMouseEnter(false);
    //   if (!isEmpty(params)) {
    //     const query = new URLSearchParams(params).toString();
    //     uri += `?${query}`;
    //   }

    // }
    navigate(uri);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '400px',
          maxWidth: '1200px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'start',
          mx: 'auto',
          gap: 1,
        }}
      >
        <Card
          className="scrollbar-hidden"
          sx={{
            width: '300px',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            // py: 1,
            boxShadow:
              'rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px',
            overflowY: 'auto',
            [theme.breakpoints.down('md')]: {
              display: 'none',
            },
          }}
        >
          {menuTree.length &&
            menuTree.map((item, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  cursor: 'pointer',
                  '&: hover': {
                    bgcolor: '#fbf6d6',
                  },
                }}
                onClick={() => {
                  handleClickCollection(item);
                }}
              >
                <Typography
                  sx={{
                    width: '100%',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    textTransform: 'capitalize',
                    borderRadius: 1,
                    py: 0.5,
                    px: 1,
                    color: 'green',
                  }}
                >
                  {item?.name}
                </Typography>
                <NavigateNextIcon fontSize="small" />
              </Box>
            ))}
        </Card>
        <Box
          onMouseEnter={() => {
            setIsNavigate(true);
          }}
          onMouseLeave={() => {
            setIsNavigate(false);
          }}
          ref={containerRef}
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            borderRadius: 1,
            boxShadow:
              'rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px',
          }}
        >
          <Swiper
            style={{
              width: containerWidth,
              zIndex: 0,
              // paddingTop: '10px',
              paddingBottom: '10px',
            }}
            spaceBetween={30}
            // centeredSlides={true}
            effect={''}
            mousewheel={true}
            grabCursor={true}
            slidesPerView={1}
            autoplay={{ delay: random, disableOnInteraction: false }}
            freeMode={true}
            modules={[
              Navigation,
              Pagination,
              Autoplay,
              // Mousewheel,
              EffectCoverflow,
            ]}
            navigation={isNavigate}
            pagination={{ clickable: true }}
            // scrollbar={{ draggable: true }}
          >
            {list?.map(({ imageUrl, href }, index: any) => (
              <Box
                key={index}
                sx={{
                  width: 'auto',
                  display: 'flex',
                  '&:hover': {
                    opacity: 0.5,
                  },
                }}
              >
                <SwiperSlide
                  key={index}
                  onClick={() => {
                    window.open(href);
                  }}
                  onMouseEnter={() => {}}
                  onMouseLeave={() => {}}
                  style={{
                    width: '100%',
                    display: 'flex',
                  }}
                >
                  <Box
                    sx={{
                      width: '100%',
                      height: '360px',
                      // borderRadius: 1,
                      display: 'flex',
                      overflow: 'hidden',
                      [theme.breakpoints.down('md')]: {
                        // height: '260px',
                      },
                      transition: 'transform .3s ease-in-out',
                      '&:hover': {
                        // transform: 'scale(1.05)',
                      },
                    }}
                  >
                    <img
                      src={imageUrl}
                      alt="hình ảnh"
                      width={'100%'}
                      height={'100%'}
                      loading="lazy"
                    />
                  </Box>
                </SwiperSlide>
              </Box>
            ))}
          </Swiper>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
            mr: 1,
            width: '100%',
            height: '100%',
            maxWidth: '350px',
            minWidth: '350px',
            borderRadius: 1,
            boxShadow:
              'rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px',
            overflow: 'hidden',
            [theme.breakpoints.down('md')]: {
              display: 'none',
            },
          }}
        >
          <Box
            sx={{
              flex: 1,
              width: '100%',
              height: 'calc(50% - 0.5px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 1,
              overflow: 'hidden',
              cursor: 'pointer',
            }}
            onClick={() => window.open(bannerRightTop?.href)}
          >
            <img
              src={bannerRightTop?.imageUrl}
              alt="Banner 4"
              loading="lazy"
              style={{ width: '100%', height: 'auto', borderRadius: 1 }}
            />
          </Box>
          <Box
            sx={{
              flex: 1,
              width: '100%',
              height: 'calc(50% - 0.5px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 1,
              overflow: 'hidden',
              cursor: 'pointer',
            }}
            onClick={() => window.open(bannerRightBottom?.href)}
          >
            <img
              src={bannerRightBottom?.imageUrl}
              alt="Banner 5"
              loading="lazy"
              style={{ width: '100%', height: 'auto', borderRadius: 1 }}
            />
          </Box>
        </Box>
      </Box>
      {/* <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 4,
          px: 5,
          mt: 3,
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          [theme.breakpoints.up('md')]: {
            display: 'none',
          },
        }}
      >
        <Box
          sx={{
            flex: 1,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 2,
            overflow: 'hidden',
            cursor: 'pointer',
            transition: 'transform .3s ease-in-out',
            '&:hover': {
              transform: 'scale(1.05)',
            },
          }}
          onClick={() => window.open(bannerRightTop?.href)}
        >
          <img
            src={bannerRightTop?.imageUrl}
            alt="Banner 4"
            style={{ width: '100%', height: 'auto', borderRadius: 4 }}
            loading="lazy"
          />
        </Box>
        <Box
          sx={{
            flex: 1,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 2,
            overflow: 'hidden',
            cursor: 'pointer',
            transition: 'transform .3s ease-in-out',
            '&:hover': {
              transform: 'scale(1.05)',
            },
          }}
          onClick={() => window.open(bannerRightBottom?.href)}
        >
          <img
            src={bannerRightBottom?.imageUrl}
            alt="Banner 5"
            loading="lazy"
            style={{ width: '100%', height: 'auto', borderRadius: 4 }}
          />
        </Box>
      </Box> */}
    </Box>
  );
};

export default SlideLead;
