import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Button, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

const Services: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [hover, setHover] = React.useState<string>('');
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setBrands(result?.slidePartner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        height: '900px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: -1,
        px: 2,
        py: 10,
        borderRadius: '3px',
        background: `url("/section-cac-dich-vu-phuong-linh-jsc-1920x860.jpg")`,
        backgroundRepeat: 'none',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: '1200px',
          textAlign: 'center',
          display: 'flex',
          justifyContent: 'center',
          gap: 3,
        }}
      >
        <Box
          sx={{
            flex: 2,
            display: 'flex',
            justifyContent: 'end',
            gap: 3,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'end',
              gap: 3,
              pt: 3,
            }}
          >
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-tiep-can-phu-hop-phuong-linh-jsc-360x250.jpg"
                alt="banner-tiep-can-phu-hop-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-dich-vu-tron-goi-phuong-linh-jsc-440x290.jpg"
                alt="banner-dich-vu-tron-goi-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-doi-ngu-chuyen-nghiep-phuong-linh-jsc-280x190.jpg"
                alt="banner-doi-ngu-chuyen-nghiep-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'start',
              gap: 3,
              pb: 3,
            }}
          >
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-mang-luoi-toan-quoc-phuong-linh-jsc-360x250.jpg"
                alt="banner-mang-luoi-toan-quoc-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-nhanh-chong-tin-cay-phuong-linh-jsc-320x220.jpg"
                alt="banner-nhanh-chong-tin-cay-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
            <Box
              sx={{
                width: 'fit-content',
                borderRadius: 5,
                overflow: 'hidden',
              }}
            >
              <img
                src="/service/banner-tiep-thi-ban-hang-phuong-linh-jsc-360x250.jpg"
                alt="banner-tiep-thi-ban-hang-phuong-linh-jsc"
                loading="lazy"
              />
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            // p: 3,
          }}
        >
          <Box
            sx={{
              width: '100%',
              textAlign: 'center',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              onMouseEnter={() => setHover('mang-luoi-toan-quoc')}
              onMouseLeave={() => setHover('')}
              sx={{
                cursor: 'pointer',
              }}
            >
              {hover === 'mang-luoi-toan-quoc' ? (
                <img
                  src="/service/mang-luoi-toan-quoc-hover.png"
                  alt="mang-luoi-toan-quoc"
                  loading="lazy"
                />
              ) : (
                <img
                  src="/service/mang-luoi-toan-quoc.png"
                  alt="mang-luoi-toan-quoc"
                  loading="lazy"
                />
              )}
            </Box>
            <Typography
              sx={{
                fontSize: '25px',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                fontStyle: 'italic',
                mx: 'auto',
                p: 1,
              }}
            >
              Mạng lưới toàn quốc
            </Typography>
            <Typography
              sx={{
                width: 100,
                py: '2px',
                background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                position: 'absolute',
                bottom: 0,
                borderRadius: 5,
                mb: '-2px',
              }}
            ></Typography>
          </Box>
          <Typography
            sx={{
              // fontSize: '18px',
              // fontWeight: 'bold',
              fontFamily: 'inherit',
              // fontStyle: 'italic',
              color: 'white',
              bgcolor: '#51a3dc',
              mx: 'auto',
              p: 3,
              borderRadius: 10,
            }}
          >
            Xây dựng đươc mạng lưới phân phối trên toàn quốc, gồm các cửa hàng
            thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default Services;
