import React, { useEffect } from 'react';
import { Box, Button, Typography } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';

const Industry: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 10,
        pt: 5,
        // clipPath: 'polygon(50% 4%, 100% 0%, 100% 100%, 0 100%, 0 0)',
        // px: 3,
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          // borderBottom: 1,
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            color: '#006cb1',
            mx: 'auto',
            p: 2,
          }}
        >
          NGÀNH HÀNG KINH DOANH
        </Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 3,
        }}
      >
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
            }}
          >
            <img
              src="/banner-cham-soc-phuong-linh-jsc-300x300.jpg"
              alt="banner-cham-soc"
              loading="lazy"
            />
          </Box>
          <Typography
            sx={{
              width: '250px',
              height: '150px',
              fontSize: '30px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: '',
              textAlign: 'center',
              mx: 'auto',
              p: 5,
            }}
          >
            Chăm sóc sức khoẻ
          </Typography>
          <ArrowForwardIcon
            sx={{
              fontSize: '30px',
              bgcolor: '#21a1f1',
              color: 'white',
              borderRadius: '100%',
              cursor: 'pointer',
            }}
          />
        </Box>
        <Box
          sx={{
            flex: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
            }}
          >
            <img
              src="/banner-nhanh-cong-nghe-phuong-linh-jsc-300x600.jpg"
              alt="banner-nhanh-cong-nghe"
              loading="lazy"
            />
          </Box>
          <Typography
            sx={{
              width: '250px',
              height: '150px',
              fontSize: '30px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: '',
              textAlign: 'center',
              mx: 'auto',
              p: 5,
            }}
          >
            Thiết bị công nghệ
          </Typography>
          <ArrowForwardIcon
            sx={{
              fontSize: '50px',
              bgcolor: '#21a1f1',
              color: 'white',
              borderRadius: '100%',
              cursor: 'pointer',
            }}
          />
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
            }}
          >
            <img
              src="/banner-phu-kien-phuong-linh-jsc-300x300.jpg"
              alt="banner-phu-kien-phuong"
              loading="lazy"
            />
          </Box>
          <Typography
            sx={{
              width: '250px',
              height: '150px',
              fontSize: '30px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: '',
              textAlign: 'center',
              mx: 'auto',
              p: 5,
              pb: 0,
            }}
          >
            Phụ kiện
          </Typography>
          <ArrowForwardIcon
            sx={{
              fontSize: '30px',
              bgcolor: '#21a1f1',
              color: 'white',
              borderRadius: '100%',
              cursor: 'pointer',
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default Industry;
