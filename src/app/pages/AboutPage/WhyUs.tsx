import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Button, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

const WhyUs: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setBrands(result?.slidePartner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        height: '600px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: -1,
        px: 2,
        py: 10,
        borderRadius: '3px',
        background: `url("https://phuonglinhjsc.vn/file/img/1727319378989_sectionthongsophuonglinhjsc1920x860.jpg")`,
        clipPath: 'polygon(0 0, 100% 0%, 100% 95%, 50% 100%, 0 95%)',
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: 1200,
          textAlign: 'center',
          borderBottom: 1,
          borderColor: 'white',
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            color: 'white',
            mx: 'auto',
            p: 1,
          }}
        >
          LỰA CHỌN CHÚNG TÔI
        </Typography>
        <Typography
          sx={{
            width: 200,
            py: '3px',
            bgcolor: '#006cb1',
            position: 'absolute',
            bottom: 0,
            borderRadius: 5,
            mb: '-3px',
          }}
        ></Typography>
      </Box>
      <Box
        sx={{
          maxWidth: 1200,
          display: 'flex',
          justifyContent: 'space-between',
          gap: 3,
        }}
      >
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            100+
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Đối tác trên toàn quốc
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            1 Tr+
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Sản phẩm bán ra
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            10+
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Năm thành lập
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default WhyUs;
