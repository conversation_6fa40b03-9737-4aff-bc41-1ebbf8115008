import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import {
  Navigation,
  Pagination,
  Autoplay,
  Mousewheel,
  EffectFlip,
  EffectCoverflow,
} from 'swiper/modules';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';

import banner1 from 'assets/images/banner/Banner-slide1.jpg';
import banner2 from 'assets/images/banner/Banner-slide2.jpg';
import banner3 from 'assets/images/banner/Banner-slide3.jpg';
import banner4 from 'assets/images/banner/Banner-slide4.jpg';

const InitListBanners = [
  {
    background: banner1,
    href: 'banner-1',
  },
  {
    background: banner2,
    href: 'banner-2',
  },
  {
    background: banner3,
    href: 'banner-3',
  },
  {
    background: banner4,
    href: 'banner-4',
  },
];

interface Props {
  list?: any;
}

const SlideMiddle: React.FC<Props> = ({ list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [previewShow, setPreviewShow] = React.useState<number>(3);
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [isCenter, setIsCenter] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [banners, setBanners] = React.useState<any[]>(InitListBanners);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = () => {};

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
    if (width <= 900 && previewShow !== 1) {
      setPreviewShow(1);
      setIsCenter(true);
    } else if (width > 900 && previewShow === 1) {
      setPreviewShow(3);
      setIsCenter(false);
    }
  }, [width]);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        display: 'flex',
        justifyContent: 'center',
        mt: 3,
      }}
    >
      <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
          // paddingTop: '10px',
          paddingBottom: '10px',
        }}
        spaceBetween={30}
        // centeredSlides={true}
        effect={''}
        mousewheel={true}
        grabCursor={true}
        slidesPerView={1}
        autoplay={{ delay: random, disableOnInteraction: false }}
        freeMode={true}
        modules={[
          Navigation,
          Pagination,
          Autoplay,
          // Mousewheel,
          EffectCoverflow,
        ]}
        // navigation={isNavigate}
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {list?.map(({ imageUrl, href }, index: any) => (
          <Box
            key={index}
            sx={{
              width: 'auto',
              display: 'flex',
              '&:hover': {
                opacity: 0.5,
              },
            }}
          >
            <SwiperSlide
              key={index}
              onClick={() => {
                window.open(href);
              }}
              onMouseEnter={() => {}}
              onMouseLeave={() => {}}
              style={{
                width: '100%',
                display: 'flex',
                borderRadius: 0,
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  // maxWidth: '600px',
                  // background: `url("${background}")`,
                  // backgroundRepeat: 'no-repeat',
                  // backgroundSize: 'cover',
                  // backgroundPosition: 'center',
                  borderRadius: 1,
                  display: 'flex',
                  overflow: 'hidden',
                  [theme.breakpoints.down('md')]: {
                    // height: '260px',
                  },
                  transition: 'transform .3s ease-in-out',
                  '&:hover': {
                    // transform: 'scale(1.05)',
                  },
                }}
              >
                <img
                  src={imageUrl}
                  alt=""
                  width={'100%'}
                  height={'100%'}
                  loading="lazy"
                />
              </Box>
            </SwiperSlide>
          </Box>
        ))}
      </Swiper>
    </Box>
  );
};

export default SlideMiddle;
