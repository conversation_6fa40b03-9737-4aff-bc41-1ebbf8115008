import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

import brand1 from 'assets/images/partner/Soundpeats-Cellphones-Chinh-hang.png';
import brand2 from 'assets/images/brand/brand_2.webp';
import brand3 from 'assets/images/brand/brand_3.webp';
import brand4 from 'assets/images/brand/brand_4.webp';
import brand5 from 'assets/images/brand/brand_5.jpeg';
import brand6 from 'assets/images/brand/brand_6.jpeg';

interface Props {
  title?: string;
  buttonTitle?: string;
}

const Partners: React.FC<Props> = ({ title, buttonTitle }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (partnerId: string) => {
    if (partnerId) {
      window.location.href = partnerId;
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Not found',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setBrands(result?.slidePartner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchPartners = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/partners`,
          {},
        );

        console.log('result: ', result);
        setBrands(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchPartners();
  }, []);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        height: '800px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        mb: -1,
        px: 2,
        py: 10,
        borderRadius: '3px',
        background: `url("https://phuonglinhjsc.vn/file/img/1727319396192_sectiondoitacphuonglinhjsc1920x860.jpg")`,
        clipPath: 'polygon(0 0, 100% 0%, 100% 95%, 50% 100%, 0 95%)',
      }}
    >
      <Typography
        sx={{
          fontWeight: 'bold',
          fontSize: '60px',
          fontFamily: 'inherit',
          textTransform: 'uppercase',
          color: 'white',
          py: 3,
          mx: 'auto',
          textAlign: 'center',
        }}
      >
        đối tác của chúng tôi
      </Typography>
      <Box
        sx={{
          maxWidth: '1200px',
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 3,
          py: 5,
          mx: 'auto',
        }}
      >
        {brands.map(({ href, url, title }, index: any) => (
          <Box
            sx={{
              // width: '100%',
              // height: '100%',
              // background: `url("${imageUrl}")`,
              // backgroundRepeat: 'no-repeat',
              // backgroundSize: 'cover',
              // backgroundPosition: 'center',
              bgcolor: 'white',
              borderRadius: 5,
              display: 'flex',
              px: 3,
              transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
              transition: 'opacity .2s ease-in-out,transform .5s ease-in-out',
              '&:hover': {},
            }}
          >
            <img
              src={url}
              alt=""
              width={'auto'}
              height={'70px'}
              loading="lazy"
            />
          </Box>
        ))}
      </Box>
      <Box
        sx={{
          maxWidth: '1200px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'end',
          gap: 3,
          bgcolor: 'white',
          px: 10,
          mx: 'auto',
        }}
      >
        <Typography
          sx={{
            fontWeight: 'bold',
            fontSize: '90px',
            fontFamily: 'inherit',
            textTransform: 'uppercase',
            color: '#00a2d3',
          }}
        >
          100+
        </Typography>
        <Typography
          sx={{
            fontWeight: 600,
            fontSize: '50px',
            fontFamily: 'inherit',
            textTransform: 'uppercase',
            color: '#00a2d3',
            p: 2,
          }}
        >
          ĐỐI TÁC TRÊN TOÀN QUỐC
        </Typography>
      </Box>
      {/* <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
          borderRadius: 5,
          paddingBottom: 2,
          padding: '10px',
          background: '#fbf8f8',
        }}
        spaceBetween={50}
        effect={'coverflow'}
        grabCursor={true}
        slidesPerView={'auto'}
        autoplay={{ delay: random, disableOnInteraction: true }}
        freeMode={true}
        modules={[Navigation, Pagination, Autoplay]}
        navigation={isNavigate}
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {brands.map(({ href, imageUrl, title }, index: any) => (
          <SwiperSlide
            key={index}
            onClick={() => {
              onClickType(href);
            }}
            onMouseEnter={() => {
              setBestIndex(index);
            }}
            onMouseLeave={() => {
              setBestIndex(-1);
            }}
            style={{
              width: 'auto',
              height: '100px',
              minWidth: '200px',
              background: '',
              borderRadius: 5,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'start',
              // overflow: 'hidden',
              position: 'relative',
              // boxShadow:
              //   '0px 0px 2px 1px rgba(0,0,0,0.05),inset 0px 0px 20px 1px rgba(0,0,0,0.05)',
            }}
          >
            <Box
              sx={{
                width: '100%',
                height: '100%',
                // background: `url("${imageUrl}")`,
                // backgroundRepeat: 'no-repeat',
                // backgroundSize: 'cover',
                // backgroundPosition: 'center',
                borderRadius: '5%',
                display: 'flex',
                transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                transition: 'opacity .2s ease-in-out,transform .5s ease-in-out',
                '&:hover': {},
              }}
            >
              <img src={imageUrl} alt="" width={'auto'} height={'100px'} />
            </Box>
          </SwiperSlide>
        ))}
      </Swiper> */}
    </Box>
  );
};

export default Partners;
