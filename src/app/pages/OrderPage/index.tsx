import {
  Box,
  Button,
  Paper,
  Tooltip,
  Typography,
  Card,
  IconButton,
  TableContainer,
  useMediaQuery,
  Dialog,
  DialogTitle,
  TextField,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableFooter,
} from '@mui/material';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import Recently from 'app/components/Recently/Recently';
import get from 'lodash/get';
import { request } from 'utils/request';
import { useDispatch, useSelector } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';

import { formatTime } from 'utils/moment';

import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';

const formatCurrency = (number: number) => {
  return number?.toLocaleString('vi-VN', {
    style: 'currency',
    currency: 'VND',
  });
};

type Order = {
  _id: string;
  name: string;
  variant: any;
  images: any[];
  quantity: number;
  price: number;
  priceOrigin: number;
  status: string;
  userInfo: any;
  createdAt: any;
};

const MAPPING_STATUS = {
  WAITING: 'Chờ xử lý',
  CONFIRMED: 'Đã xác nhận',
  DELIVERING: 'Đang giao hàng',
  RECEIVED: 'Đã nhận hàng',
  CANCEL_BY_USER: 'Đã hủy bởi khách hàng',
  CANCEL_BY_ADMIN: 'Đã hủy bởi admin',
};

export function OrderPage() {
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);
  const dispatch = useDispatch();

  // TODO: get list order from api
  const [orders, setOrders] = React.useState<any[]>([]);
  const [order, setOrder] = React.useState<any>({});
  const [dataUpdate, setDataUpdate] = React.useState<any>({});
  const [isOpenOrder, setIsOpenOrder] = React.useState<boolean>(false);

  const columnHelper = createColumnHelper<Order>();
  const columns: any[] = [
    // columnHelper.accessor(row => row._id, {
    //   id: '_id',
    //   cell: info => {
    //     return (
    //       <Tooltip
    //         PopperProps={{
    //           disablePortal: true,
    //         }}
    //         onClose={handleTooltipClose}
    //         open={open && copiedText === info.getValue()}
    //         disableFocusListener
    //         disableHoverListener
    //         disableTouchListener
    //         title={`Copy ${info.getValue()}`}
    //       >
    //         <Button
    //           sx={{
    //             maxWidth: '96px',
    //           }}
    //           onClick={() => {
    //             copyToClipboard(info.getValue());
    //             handleTooltipOpen();
    //           }}
    //         >
    //           <Typography
    //             fontSize={14}
    //             width={1}
    //             whiteSpace={'nowrap'}
    //             overflow={'hidden'}
    //             textOverflow={'ellipsis'}
    //           >
    //             {info.getValue()}
    //           </Typography>
    //         </Button>
    //       </Tooltip>
    //     );
    //   },
    //   header: () => <span>ID</span>,
    // }),
    columnHelper.accessor(row => row.images, {
      id: 'images',
      cell: info => (
        <Box
          sx={{
            width: '60px',
            height: '60px',
            background: `url("${info.getValue()?.[0].url}")`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></Box>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Sản Phẩm</span>
      ),
    }),
    columnHelper.accessor(row => row.name, {
      id: 'name',
      cell: info => (
        <Tooltip title={info.getValue()} arrow>
          <Typography
            fontSize={14}
            maxWidth={128}
            whiteSpace={'nowrap'}
            overflow={'hidden'}
            textOverflow={'ellipsis'}
          >
            {info.getValue()}
          </Typography>
        </Tooltip>
      ),
      header: () => '',
    }),
    columnHelper.accessor(row => row.variant?.color, {
      id: 'color',
      cell: info => (
        <Typography fontSize={14} textTransform={'capitalize'}>
          {info.getValue()}
        </Typography>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Màu sắc</span>
      ),
    }),
    columnHelper.accessor(row => row.price, {
      id: 'price',
      cell: info => (
        <Box
          sx={{
            display: 'flex',
            gap: 1,
          }}
        >
          <Typography
            sx={{
              color: 'red',
            }}
          >
            {formatCurrency(info.getValue())}
          </Typography>
        </Box>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Giá</span>
      ),
    }),
    columnHelper.accessor(row => row.quantity, {
      id: 'quantity',
      cell: info => (
        <Typography
          fontSize={14}
          maxWidth={64}
          whiteSpace={'nowrap'}
          overflow={'hidden'}
          textOverflow={'ellipsis'}
          textAlign={'center'}
        >
          {info.getValue()}
        </Typography>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Số lượng</span>
      ),
    }),
    columnHelper.accessor(row => row.quantity * row.price, {
      id: 'sum',
      cell: info => (
        <Typography fontSize={14} textAlign={'left'}>
          {formatCurrency(info.getValue())}
        </Typography>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Tổng</span>
      ),
    }),
    // columnHelper.accessor(row => row, {
    //   id: 'sum',
    //   cell: info => {
    //     const [indexClick, setIndexClick] = useState(0);
    //     if (paymentPart.length) {
    //       return (
    //         <Stepper activeStep={indexClick} alternativeLabel nonLinear>
    //           {sortBy(paymentPart, o => [o.updatedAt]).map((part, index) => (
    //             <Step key={index} active={false} completed={!!part?.paymented}>
    //               <StepButton
    //                 sx={{
    //                   '& .MuiStepLabel-root': {
    //                     fontSize: '8px',
    //                   },
    //                   '& .MuiStepLabel-alternativeLabel': {
    //                     fontSize: '8px',
    //                   },
    //                 }}
    //                 hidden={!part?.paymented}
    //                 icon={
    //                   <PaymentIcon
    //                     sx={{
    //                       color: part?.paymented ? 'green' : 'red',
    //                     }}
    //                   />
    //                 }
    //                 onClick={() => {
    //                   setIndexClick(index);
    //                   // setConfirmPayment(part);
    //                 }}
    //               >
    //                 <Typography fontSize={10}>
    //                   {formatCurrency(part?.pricePart)}
    //                 </Typography>
    //               </StepButton>
    //             </Step>
    //           ))}
    //         </Stepper>
    //       );
    //     }

    //     const price = info.getValue()?.price;
    //     return (
    //       <Step active={false}>
    //         <StepButton
    //           sx={{
    //             '& .MuiStepLabel-root': {
    //               fontSize: '8px',
    //             },
    //             '& .MuiStepLabel-alternativeLabel': {
    //               fontSize: '8px',
    //             },
    //           }}
    //           icon={
    //             <PaymentIcon
    //               sx={{
    //                 // color: part?.paymented ? 'green' : 'red',
    //                 color: 'red',
    //               }}
    //             />
    //           }
    //           onClick={() => {
    //             // setConfirmPayment('NaN');
    //           }}
    //         >
    //           <Typography fontSize={10}>{formatCurrency(price)}</Typography>
    //         </StepButton>
    //       </Step>
    //     );
    //   },
    //   header: () => <span>Payment</span>,
    // }),
    columnHelper.accessor(row => row.status, {
      id: 'status',
      cell: info => (
        <Typography
          sx={{
            width: 'fit-content',
            // textAlign: 'right',
            color:
              MAPPING_STATUS[info.getValue()] === 'Đã xác nhận'
                ? 'green'
                : MAPPING_STATUS[info.getValue()] === 'Đã hủy'
                ? 'black'
                : 'orangered',
            borderRadius: '4px',
            // px: 1,
            fontSize: '12px',
            // mx: 'auto',
          }}
        >
          {MAPPING_STATUS[info.getValue()]}
        </Typography>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>Trạng thái</span>
      ),
    }),
    columnHelper.accessor(row => row.createdAt, {
      id: 'timestamp',
      cell: info => (
        <Typography fontSize={14} textAlign={'left'}>
          {formatTime(info.getValue())}
        </Typography>
      ),
      header: () => (
        <span style={{ whiteSpace: 'nowrap', color: 'green' }}>
          Thời điểm đặt hàng
        </span>
      ),
    }),
  ];

  if (userInfo?.role === 'ADMIN') {
    columns.push(
      columnHelper.accessor(row => row.userInfo, {
        id: 'userInfo',
        cell: info => (
          <Tooltip
            title={
              <Box>
                <Typography fontSize={10}>{info.getValue()?.email}</Typography>
                <Typography fontSize={10}>{info.getValue()?.phone}</Typography>
                <Typography fontSize={10}>{`${info.getValue()?.firstName} ${
                  info.getValue()?.lastName
                }`}</Typography>
              </Box>
            }
            arrow
          >
            <Typography
              fontSize={14}
              maxWidth={128}
              whiteSpace={'nowrap'}
              overflow={'hidden'}
              textOverflow={'ellipsis'}
            >
              {info.getValue()?.email}
            </Typography>
          </Tooltip>
        ),
        header: () => 'Khách hàng',
      }),
    );
  }

  columns.push(
    columnHelper.accessor(row => row._id, {
      id: 'delete',
      cell: info => (
        <IconButton
          disabled={info.row.original.status !== 'WAITING'}
          sx={{
            display: userInfo?.role === 'ADMIN' ? '' : 'none',
            px: 1,
          }}
          onClick={(e: any) => {
            e.stopPropagation();
            const confirm = window.confirm(
              'Bạn có chắc muốn xoá đơn hàng này!!!',
            );
            if (confirm) {
              removeOrder(info.row.original._id);
            }
          }}
        >
          <DeleteForeverIcon />
        </IconButton>
      ),
      header: () => '',
    }),
  );

  const table = useReactTable({
    data: orders,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const fetchOrderDetail = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/orders`,
          {},
          userInfo?.token as any,
        );

        // console.log('result', { result });

        setOrders(result);
      } catch (error) {
        // return dispatch(
        //   utilsAction.showSnackbar({
        //     message: get(error, 'message', 'Error Undefined'),
        //     variant: 'error',
        //   }),
        // );
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    fetchOrderDetail();
  }, []);

  const handleSendCancel = async () => {
    const dataSend = {
      // to: order?.userInfo?.email,
      // to: ['<EMAIL>', '<EMAIL>'],
      to: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ],
      subject: 'Xác nhận huỷ đơn hàng từ website phuonglinhjsc',
      content: `<p>Đơn hàng ${order?.name} đã được huỷ bởi khách hàng ${order?.userInfo?.email}</p><p>Trân trọng!</p>`,
    };
    const method = 'post';
    const url = `${process.env.REACT_APP_BACKEND_URL}/test/mail`;
    try {
      await request(method, url, dataSend);
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: 'Phương Linh cảm ơn quý khách đã để lại thông tin',
      //     variant: 'success',
      //   }),
      // );
    } catch (error) {
      // console.log('err: ', error);
      // return dispatch(
      //   utilsAction.showSnackbar({
      //     message: 'Gửi thông tin không thành công. Vui lòng thử lại sau',
      //     variant: 'error',
      //   }),
      // );
    }
  };
  const handleSendConfirm = async () => {
    const dataSend = {
      // to: ['<EMAIL>', '<EMAIL>'],
      to: order?.userInfo?.email,
      subject: 'Xác nhận đơn hàng từ website phuonglinhjsc',
      content: `<p>Đơn hàng ${order?.name} của bạn đã được xác nhận và sẽ được giao trong thời gian sớm nhất</p><p>Phương Linh xin chân thành cảm ơn quý khách hàng</p><p>\nTrân trọng!</p>`,
    };
    const method = 'post';
    const url = `${process.env.REACT_APP_BACKEND_URL}/test/mail`;
    try {
      await request(method, url, dataSend);
      return dispatch(
        utilsAction.showSnackbar({
          message:
            'Gửi email xác nhận đơn hàng đang giao đến khách hàng thành công',
          variant: 'success',
        }),
      );
    } catch (error) {
      // console.log('err: ', error);
      return dispatch(
        utilsAction.showSnackbar({
          message: 'Gửi thông tin xác nhận đơn hàng đang giao không thành công',
          variant: 'error',
        }),
      );
    }
  };

  const UpdateOrderDetail = () => {
    return (
      <Dialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('sm'))}
        fullWidth={true}
        maxWidth="sm"
        open={isOpenOrder}
        onClose={() => setIsOpenOrder(false)}
        // PaperProps={{
        //   sx: {
        //     borderRadius: 2,
        //     backgroundColor: '#efeff1',
        //     margin: 0,
        //     height: '80vh',
        //     [theme.breakpoints.down('sm')]: {
        //       height: '100vh',
        //     },
        //     overflow: 'hidden',
        //   },
        // }}
      >
        <DialogTitle>Thông tin đơn hàng</DialogTitle>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            p: 2,
            mt: 1,
          }}
        >
          <Typography
            sx={{
              color: 'orangered',
            }}
          >
            Khách hàng: {order.userInfo?.email}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Box
              sx={{
                width: '60px',
                height: '60px',
                background: `url("${order?.images?.[0].url}")`,
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            ></Box>
            <Tooltip title={order?.name} arrow>
              <Typography
                fontSize={14}
                maxWidth={300}
                whiteSpace={'nowrap'}
                overflow={'hidden'}
                textOverflow={'ellipsis'}
              >
                {order?.name}
              </Typography>
            </Tooltip>
            <Typography fontSize={14} textTransform={'capitalize'}>
              {' '}
              - {order?.variant?.color}
            </Typography>
          </Box>
          <Typography>Giá: {formatCurrency(order.price)}</Typography>
          <Typography>Số lượng: {order.quantity}</Typography>
          <Typography>
            Tổng: {formatCurrency(order.quantity * order.price)}
          </Typography>
          <Typography
            sx={{
              display: userInfo?.role === 'ADMIN' ? 'none' : '',
            }}
          >
            Trạng thái đơn hàng: {MAPPING_STATUS[order.status]}
          </Typography>
          <FormControl
            sx={{
              width: 'fit-content',
              minWidth: '200px',
              display: userInfo?.role === 'ADMIN' ? '' : 'none',
            }}
          >
            <InputLabel id="order-status">Trạng thái đơn hàng</InputLabel>
            <Select
              size="small"
              labelId="order-status-label"
              id="order-status"
              value={dataUpdate.status}
              label="Trạng thái đơn hàng"
              onChange={e => {
                setDataUpdate({ ...dataUpdate, status: e.target.value });
              }}
            >
              {Object.keys(MAPPING_STATUS).map((item, index) => (
                <MenuItem key={index} value={item}>
                  {MAPPING_STATUS[Object.keys(MAPPING_STATUS)[index]]}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        <DialogActions>
          <Button
            variant="outlined"
            color="warning"
            onClick={() => setIsOpenOrder(false)}
          >
            Thoát
          </Button>
          <Button
            variant="contained"
            color="success"
            disabled={userInfo?.role !== 'ADMIN' && order?.status !== 'WAITING'}
            onClick={() => {
              if (userInfo?.role === 'ADMIN') {
                updateOrder(order._id, dataUpdate);
                setIsOpenOrder(false);
              } else {
                updateOrder(order._id, { status: 'CANCEL_BY_USER' });
                setIsOpenOrder(false);
                handleSendCancel();
              }
            }}
          >
            {userInfo?.role === 'ADMIN' ? 'Xác nhận' : 'Huỷ đơn hàng'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const updateOrder = (_id: any, data: any) => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'put',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/orders/${_id}`,
          data,
          userInfo?.token as any,
        );

        // console.log('result', { result });
        fetchOrderDetail();
        if (dataUpdate.status === 'DELIVERING') handleSendConfirm();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const removeOrder = (_id: any) => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'delete',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/orders/${_id}`,
          {},
          userInfo?.token as any,
        );

        // console.log('result', { result });
        fetchOrderDetail();
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  return (
    <>
      <Helmet>
        <title>Đặt hàng</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          px: '100px',
          [theme.breakpoints.down('lg')]: {
            px: '0px',
          },
        }}
      >
        <Typography
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            p: 4,
            fontSize: '14px',
            fontWeight: '500',
          }}
        >
          Đơn hàng
          <NavigateNextIcon fontSize="small" />
        </Typography>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            width: '100%',
            height: '100%',
            my: 2,
            px: 1,
            [theme.breakpoints.down('md')]: {
              flexDirection: 'column',
              justifyContent: 'space-between',
              gap: 3,
            },
          }}
        >
          {/* <Box
              sx={{
                display: 'flex',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  flex: 7,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'start',
                }}
              >
                <Typography>Sản phẩm</Typography>
              </Box>
              <Box
                sx={{
                  flex: 1,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <Typography>Số lượng</Typography>
              </Box>
              <Box
                sx={{
                  flex: 1,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'end',
                  // bgcolor: 'green',
                }}
              >
                <Typography>Tổng</Typography>
              </Box>
              <Box
                sx={{
                  flex: 2,
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'end',
                  // bgcolor: 'green',
                }}
              >
                <Typography>Trạng thái</Typography>
              </Box>
            </Box>
            {orders?.map(
              (
                {
                  _id,
                  // brand,
                  title,
                  // color,
                  // model,
                  images,
                  quantity,
                  price,
                  priceOrigin,
                  status,
                },
                index,
              ) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    width: '100%',
                    gap: 1,
                    opacity: status === 'Đã xử lý' ? 0.5 : 1,
                  }}
                >
                  <Box
                    sx={{
                      flex: 7,
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'start',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        gap: 2,
                      }}
                    >
                      <Box
                        sx={{
                          width: '80px',
                          height: '80px',
                          background: `url("${image}")`,
                          backgroundRepeat: 'no-repeat',
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                        }}
                      ></Box>
                      <Box>
                        <Typography>{title}</Typography>
                        <Box
                          sx={{
                            display: 'flex',
                            gap: 2,
                          }}
                        >
                          <Typography
                            sx={{
                              color: 'red',
                            }}
                          >
                            {formatCurrency(price)}
                          </Typography>
                          <Typography
                            sx={{
                              textDecoration: 'line-through',
                            }}
                          >
                            {formatCurrency(priceOrigin)}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      flex: 1,
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <Typography fontWeight={'bold'}>{quantity}</Typography>
                    <Button
                      sx={{
                        color: 'red',
                      }}
                      onClick={() => {}}
                    >
                      <DeleteForeverIcon fontSize="small" />{' '}
                    </Button>
                  </Box>
                  <Box
                    sx={{
                      flex: 1,
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'end',
                      alignItems: 'center',
                      // bgcolor: 'green',
                    }}
                  >
                    <Typography>{formatCurrency(price * quantity)}</Typography>
                  </Box>
                  <Box
                    sx={{
                      flex: 2,
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'end',
                      alignItems: 'center',
                      // bgcolor: 'green',
                    }}
                  >
                    <Typography
                      sx={{
                        // width: '100%',
                        textAlign: 'right',
                        bgcolor: status === 'Đã xử lý' ? 'green' : 'orangered',
                        borderRadius: '10px',
                        px: 1,
                      }}
                    >
                      {MAPPING_STATUS[status]}
                    </Typography>
                  </Box>
                </Box>
              ),
            )}*/}

          {/* Table */}
          <TableContainer
            className="scrollbar-hidden"
            component={Paper}
            sx={{
              mt: 4,
              maxHeight: '500px',
              overflowY: 'auto',
              // bgcolor: 'primary.main',
            }}
          >
            <Table stickyHeader>
              <TableHead>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableCell key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {table
                  .getRowModel()
                  .rows.sort(
                    (a, b) =>
                      new Date(b.original.createdAt).getTime() -
                      new Date(a.original.createdAt).getTime(),
                  )
                  .map(row => (
                    <TableRow
                      key={row.id}
                      onClick={() => {
                        // console.log('oderid: ', row.original);
                        if (
                          row.original.status === 'WAITING' ||
                          row.original.status === 'CONFIRMED' ||
                          row.original.status === 'DELIVERING'
                        ) {
                          setOrder(row.original);
                          setDataUpdate({ status: row.original.status });
                          setIsOpenOrder(true);
                        }
                      }}
                      sx={{
                        cursor:
                          row.original.status !== 'WAITING' &&
                          row.original.status !== 'CONFIRMED' &&
                          row.original.status !== 'DELIVERING'
                            ? ''
                            : 'pointer',
                        opacity:
                          row.original.status !== 'WAITING' &&
                          row.original.status !== 'CONFIRMED' &&
                          row.original.status !== 'DELIVERING'
                            ? 0.5
                            : 1,
                      }}
                    >
                      {row.getVisibleCells().map(cell => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
              </TableBody>
              <TableFooter>
                {table.getFooterGroups().map(footerGroup => (
                  <TableRow key={footerGroup.id}>
                    {footerGroup.headers.map(header => (
                      <TableCell key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.footer,
                              header.getContext(),
                            )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableFooter>
            </Table>
          </TableContainer>
          {/* End Table */}

          {/* <Box
            sx={{
              flex: 3,
              // bgcolor: 'green',
              // width: '30%',
              display: 'flex',
              flexDirection: 'column',
              gap: 3,
            }}
          >
            <Card
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                // borderRadius: '10px',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                }}
              >
                Thống kê trong tháng
              </Typography>
            </Card>
            <Card
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                // borderRadius: '10px',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                }}
              >
                Thống kê tháng trước
              </Typography>
            </Card>
            <Card
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                p: 2,
                gap: 2,
                // borderRadius: '10px',
                // boxShadow:
                //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Typography
                sx={{
                  fontWeight: 'bold',
                }}
              >
                Thống kê trong năm
              </Typography>
            </Card>
          </Box> */}
        </Box>
        <Recently />
        {UpdateOrderDetail()}
      </Paper>
    </>
  );
}
