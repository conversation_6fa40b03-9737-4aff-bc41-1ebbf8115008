import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography, Card } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Grid from '@mui/material/Unstable_Grid2';
import { theme } from 'app/components/AppWrapper/theme';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { keyframes } from '@emotion/react';
import { useNavigate } from 'react-router-dom';

import category1 from 'assets/images/category/headset.png';
import category2 from 'assets/images/category/icon-cable.png';
import category3 from 'assets/images/category/loudspeaker.png';
import category4 from 'assets/images/category/headphones.png';
import category5 from 'assets/images/category/massager.png';
import category6 from 'assets/images/category/vs-rang-mieng.png';
import category7 from 'assets/images/category/vaccum-cleaner.png';

const initCategory = [
  {
    background: category1,
    href: '',
    title: 'Tai nghe chụp tai',
    content: '',
  },
  {
    background: category2,
    href: '',
    title: 'Phụ kiện',
    content: '',
  },
  {
    background: category3,
    href: '',
    title: 'Loa',
    content: '',
  },
  {
    background: category4,
    href: '',
    title: 'Tai nghe không dây',
    content: '',
  },
  {
    background: category5,
    href: '',
    title: 'Máy Massage',
    content: '',
  },
  {
    background: category6,
    href: '',
    title: 'Bàn chải tăm nước',
    content: '',
  },
  {
    background: category7,
    href: '',
    title: 'Robot vệ sinh',
    content: '',
  },
];

const animation1 = keyframes`
  0% {
    transform: translate(-30px, -30px)
  }
  50% {
    transform: translate(-10px, -20px)
  }
  100% {
    transform: translate(0px, 0px)
  }
`;
const animation2 = keyframes`
  0% {
    transform: translate(0px, 0px) rotate(0deg)
  }
  50% {
    transform: translate(-20px, -20px) rotate(25deg)
  }
  100% {
    transform: translate(-30px, -30px) rotate(45deg)
  }
`;

const Category: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [whyUsIndex, setWhyUsIndex] = React.useState<number>(1);
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [category, setCategory] = React.useState<any[]>(initCategory);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: 1200,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        // flexWrap: 'wrap',
        overflowX: 'auto',
        py: 10,
        [theme.breakpoints.down('sm')]: {
          gap: 3,
          justifyContent: 'start',
        },
      }}
    >
      {category.map(item => (
        <Box
          key={item.title}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'start',
            gap: 3,
            p: 1,
            cursor: 'pointer',
            // width: 80,
            [theme.breakpoints.down('sm')]: {
              width: 200,
              // height: 150,
            },
          }}
        >
          <Box
            sx={{
              width: 60,
              height: 60,
            }}
          >
            <img
              src={item.background}
              alt={item.title}
              width={'100%'}
              height={'auto'}
              loading="lazy"
            />
          </Box>
          <Typography
            sx={{
              width: '100%',
              fontSize: '14px',
              fontFamily: 'inherit',
              textAlign: 'center',
              [theme.breakpoints.down('sm')]: {
                width: 90,
              },
            }}
          >
            {item.title}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

export default Category;
