import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';

import bestSale1 from 'assets/images/product/Lens_Protector.webp';
import bestSale2 from 'assets/images/product/screen_protector_01.webp';

const InitListBestSales = [
  {
    background: bestSale1,
    href: 'bestSale-1',
    title: 'Miếng dán bảo vệ Camera',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
  },
  {
    background: bestSale2,
    href: 'bestSale-2',
    title: 'Miếng dán bảo vệ màn hình',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
  },
];

interface Props {
  title?: string;
  buttonTitle?: string;
}

const HotdealList: React.FC<Props> = ({ title, buttonTitle }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [bestSales, setBestSales] = React.useState<any[]>(InitListBestSales);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (product: string) => {
    if (product) {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Product not exist',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        p: 2,
        my: 2,
        borderRadius: 1,
        bgcolor: '#b00202',
      }}
    >
      <Box
        onMouseEnter={() => {
          setIsNavigate(true);
        }}
        onMouseLeave={() => {
          setIsNavigate(false);
        }}
        ref={containerRef}
        sx={{
          width: '95%',
          my: 2,
        }}
      >
        <Swiper
          style={{
            width: containerWidth,
            zIndex: 0,
            borderRadius: 5,
          }}
          spaceBetween={30}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: false }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          navigation={isNavigate}
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {bestSales.map(
            ({ bestSaleId, background, title, content }, index: any) => (
              <Box
                key={index}
                sx={{
                  width: 'auto',
                  height: 'auto',
                  '&:hover': {
                    opacity: 0.5,
                  },
                }}
              >
                <SwiperSlide
                  key={index}
                  onClick={() => {
                    onClickType(bestSaleId);
                  }}
                  onMouseEnter={() => {
                    setBestIndex(index);
                  }}
                  onMouseLeave={() => {
                    setBestIndex(-1);
                  }}
                  style={{
                    width: '50%',
                    height: '300px',
                    minWidth: '400px',
                    background: '',
                    borderRadius: 5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden',
                    position: 'relative',
                  }}
                >
                  {/* <Box
                    sx={{
                      width: '90%',
                      height: '90%',
                      backgroundColor: '#9f9f9f',
                      background: `url("${background}")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      borderRadius: 3,
                      display: 'flex',
                      transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                      transition:
                        'opacity .2s ease-in-out,transform 1.2s ease-in-out',
                      '&:hover': {},
                    }}
                  ></Box> */}
                  <Box
                    sx={{
                      width: '100%',
                      height: '100%',
                      p: 4,
                      position: 'absolute',
                      background:
                        'linear-gradient(to top,rgba(0,0,0,0) 15%,rgba(0,0,0,.3))',
                    }}
                  >
                    <Typography
                      sx={{
                        color: 'white',
                        fontSize: '20px',
                        fontWeight: 'bold',
                      }}
                    >
                      {title}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'white',
                        // fontSize: '20px',
                      }}
                    >
                      {content}
                    </Typography>
                    <Button
                      sx={{
                        mt: 2,
                        color: 'black',
                        bgcolor: 'gold',
                        borderRadius: 1,
                        boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                      }}
                      onClick={e => {
                        e.stopPropagation();
                      }}
                    >
                      Mua ngay
                    </Button>
                  </Box>
                </SwiperSlide>
              </Box>
            ),
          )}
        </Swiper>
      </Box>
    </Box>
  );
};

export default HotdealList;
