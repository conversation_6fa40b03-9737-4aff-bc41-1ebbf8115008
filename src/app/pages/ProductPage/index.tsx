import Box from '@mui/material/Box';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
// import { Helmet } from 'react-helmet';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import get from 'lodash/get';
import { request } from 'utils/request';

import SlideLead from './SlideLead';
import BrandsList from './BrandsList';
import BrandsList2 from './BrandsList2';
import SlideProducts from './SlideProducts';
import HotDeal from './HotDeal';

import { useUtilsSlice } from 'app/slice/utils';
import { useUserSlice } from 'app/slice/user';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import { uploadFiles } from 'utils/uploadImg';

export function ProductPage() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: userAction } = useUserSlice();

  const userInfo = useSelector(userInfoSelector);

  const [openEdit, setOpenEdit] = React.useState<boolean>(false);
  const [brand, setBrand] = React.useState<string>('');
  const [bannerReward, setBannerReward] = React.useState<any>([]);
  const [bannerMobile, setBannerMobile] = React.useState<any>([]);
  const [partnerImages, setPartnerImages] = React.useState<any[]>([]);
  const [collections, setCollections] = React.useState<any[]>([]);

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [homeSchema, setHomeSchema] = React.useState<any>({});
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setHomeSchema(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    const fetchBannerLead = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/banner`,
        );
        // console.log({ result });
        setBannerReward(result.banner);
        setBannerMobile(result.bannerMobile);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    fetchBannerLead();
    // apiHandler();
  };

  const fetchCollections = async () => {
    try {
      const result: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/collections`,
      );
      // console.log({ result });
      setCollections(result.collections);
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  React.useEffect(() => {
    fetchHomePage();
    fetchCollections();
  }, []);

  // React.useEffect(() => {
  //   const { slidePartner } = homeSchema;

  //   if (slidePartner && slidePartner.length > 0) {
  //     const newImages = slidePartner?.map(item => {
  //       return { url: item.imageUrl };
  //     });
  //     setPartnerImages(newImages);
  //   }
  // }, [homeSchema]);

  const onClose = () => {
    setOpenEdit(false);
  };
  const handleEditHome = async () => {
    try {
      const newPartnerList = await Promise.all(
        partnerImages.map(async (image: any) => {
          let { file, name, url = '' } = image;
          if (file && name) {
            // const uploadInfo = await s3Client.uploadFile(
            //   `phuonglinh/partnerImage/${Date.now()}-${name}`,
            //   file,
            // );

            // url = get(uploadInfo, 'Location');
            url = await uploadFiles([file], 'partner', '');
          }
          return { imageUrl: url, content: '', href: '' };
        }),
      );
      const newHomeSchema = { ...homeSchema, slidePartner: newPartnerList };
      setHomeSchema(newHomeSchema);
      const result: any = await request(
        'put',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/upsert`,
        newHomeSchema,
      );

      // console.log({ result });
      setHomeSchema(result);
      setOpenEdit(false);
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const vietnameseToAscii = inputStr => {
    // Chuyển đổi từ tiếng Việt có dấu sang không dấu
    var convertedStr = inputStr
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Thay đổi dấu cách thành '-'
    convertedStr = convertedStr.replace(/\s+/g, '-');

    return convertedStr;
  };

  const labelOptions = [
    {
      value: 'phone',
      label: 'phone',
    },
    {
      value: 'email',
      label: 'email',
    },
  ];

  const handleClickBreadcrumb = (event: any) => {
    event.preventDefault();
  };

  function CustomSeparator() {
    const breadcrumbs = [
      <Link
        underline="hover"
        key="1"
        color="black"
        // href="/"
        onClick={() => navigate('/')}
        sx={{
          cursor: 'pointer',
          fontWeight: 'bold',
          [theme.breakpoints.up('sm')]: { fontSize: '22px' },
        }}
      >
        Trang chủ
      </Link>,
      <Link
        underline="hover"
        key="2"
        color="black"
        // href="/san-pham"
        onClick={() => {
          if (brand) navigate(`/san-pham`);
        }}
        sx={{
          cursor: 'pointer',
          fontWeight: !brand ? 'bold' : 'normal',
          [theme.breakpoints.up('sm')]: { fontSize: '22px' },
        }}
      >
        Trang sản phẩm
      </Link>,
      brand ? (
        <Typography
          key="3"
          sx={{
            color: 'text.primary',
            [theme.breakpoints.up('sm')]: { fontSize: '22px' },
          }}
        >
          {brand}
        </Typography>
      ) : (
        ''
      ),
    ];
    return (
      <Stack spacing={2} sx={{ mt: 2, display: 'flex', alignItems: 'end' }}>
        <Breadcrumbs separator="›" aria-label="breadcrumb">
          {breadcrumbs}
        </Breadcrumbs>
      </Stack>
    );
  }

  return (
    <>
      <Helmet>
        <title>Sản phẩm</title>
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          fontFamily: 'inherit',
        }}
      >
        {/* <Box
          sx={{
            width: '100%',
            height: '800px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'center',
            gap: 1,
            pl: '200px',
            background: `url("${mainBanner}")`,
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            // backgroundSize: '70%',
            position: 'relative',
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
        >
          <Typography
            sx={{
              textTransform: 'uppercase',
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Phương linh jsc - nhà phân phối sản phẩm công nghệ
          </Typography>
          <Typography
            sx={{
              width: 'auto',
              maxWidth: '900px',
              textTransform: 'uppercase',
              fontSize: '55px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            " Đưa thế giới công nghệ vào tầm tay "
          </Typography>
          <Button
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              ml: 10,
              textTransform: 'none',
              bgcolor: 'white',
              fontSize: '16px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              px: 5,
              py: 1,
              borderRadius: 5,
              cursor: 'pointer',
              '&:hover': {
                color: 'black',
              },
              [theme.breakpoints.down('md')]: {
                display: 'none',
              },
            }}
          >
            Xem thêm
            <ArrowForwardIcon
              sx={{
                fontSize: '16px',
                bgcolor: '#22c1f1',
                color: 'black',
                borderRadius: '100%',
              }}
            />
          </Button>
        </Box> */}
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            position: 'relative',
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
        >
          <SlideLead
            list={bannerReward}
            // bannerRightTop={homeSchema?.bannerRightTop}
            // bannerRightBottom={homeSchema?.bannerRightBottom}
          />
        </Box>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 1,
            position: 'relative',
            [theme.breakpoints.up('sm')]: {
              display: 'none',
            },
          }}
        >
          <SlideLead
            list={bannerMobile}
            // bannerRightTop={homeSchema?.bannerRightTop}
            // bannerRightBottom={homeSchema?.bannerRightBottom}
          />
        </Box>
        <Box
          sx={{
            width: '100%',
            maxWidth: '1200px',
            display: 'flex',
            justifyContent: 'start',
            py: 3,
          }}
        >
          <CustomSeparator />
        </Box>
        {/* <Category /> */}
        <BrandsList2 />
        {/* <Box
          sx={{
            width: '80%',
            minWidth: '380px',
            mt: '10px',
            overflow: 'hidden',
            [theme.breakpoints.down('sm')]: {
              width: '50%',
            },
          }}
        >
          <SlideBannerList list={homeSchema?.slideBanner} />
        </Box> */}
        {/* <Services /> */}
        {/* <WhyUs /> */}
        {/* <Industry /> */}
        {/* <Partners /> */}
        {/* <Box
          sx={{
            width: '95%',
            height: 'auto',
            display: 'flex',
            position: 'relative',
            cursor: 'pointer',
            mb: 5,
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
        >
          <SlideMiddle list={homeSchema?.middleBanner} />
        </Box> */}
        {/* <Box
          sx={{
            width: '95%',
            height: 'auto',
            display: 'flex',
            position: 'relative',
            cursor: 'pointer',
            mb: 5,
            [theme.breakpoints.up('sm')]: {
              display: 'none',
            },
          }}
        >
          <SlideMiddle list={homeSchema?.middleBannerMobile} />
        </Box> */}
        {/* <HotdealList /> */}

        {/* Todo: handle from API */}
        {/* <NewProductsList list={newProductList} /> */}
        {collections?.map((item, index) => (
          <SlideProducts
            key={index}
            keyword={item.keyword}
            brandId={item.brandId}
            categoryId={item.categoryId}
            image={item.imageUrl}
            title={item.title}
          />
        ))}

        {/* <SlideProducts
          keyword={'tai nghe'}
          image={category4}
          title="Tai nghe không dây nổi bật"
        />
        <SlideProducts
          keyword={'phụ kiện'}
          image={category2}
          title="Phụ kiện"
        />
        <SlideProducts
          keyword={'loa bluetooth'}
          categoryId={'loa-bluetooth'}
          image={category3}
          title="Loa"
        />
        <SlideProducts
          keyword={'máy massage'}
          image={category5}
          title="Máy massage"
        />
        <SlideProducts
          keyword={'bàn chải'}
          image={category6}
          title="Thiết bị chăm sóc răng miệng"
        />
        <SlideProducts
          keyword={'máy hút bụi'}
          image={category7}
          title="Robot vệ sinh"
        /> */}
        <HotDeal />
        {/* <Recently /> */}
        {/* <Feedback /> */}
        {/* <News /> */}
        <BrandsList list />
      </Paper>
    </>
  );
}
