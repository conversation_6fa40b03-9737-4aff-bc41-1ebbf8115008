import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import AddToCart from 'app/components/AppWrapper/AddToCart';
import { request } from 'utils/request';
import { theme } from 'app/components/AppWrapper/theme';
import { keyframes } from '@emotion/react';
interface Props {
  image?: string;
  title?: string;
  keyword?: string;
  brandId?: string;
  categoryId?: string;
}

const upUp = keyframes`
  0% {
    transform: translateY(0px)
  }
  100% {
    transform: translateY(-10px)
  }
`;

const SlideProducts: React.FC<Props> = ({
  keyword,
  brandId,
  categoryId,
  title,
  image,
}) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [collections, setCollections] = React.useState<
    { _id; images; name; price; priceOrigin; id; brands; categories }[]
  >([]);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const fetchCollection = async () => {
    try {
      const data: any = await request(
        'get',
        `${process.env.REACT_APP_BACKEND_URL}/api/v2/products`,
        {
          keyword: keyword?.trim() || '',
          brandId: brandId || '',
          categoryId: categoryId || '',
          limit: 6,
        },
      );
      setCollections(data.products);
      // console.log({ data });
    } catch (error) {
      console.log('error: ', error);
    }
  };

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  useEffect(() => {
    if (keyword || brandId || categoryId) {
      fetchCollection();
    }
  }, []);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 0,
        pt: 1,
        // clipPath: 'polygon(50% 4%, 100% 0%, 100% 100%, 0 100%, 0 0)',
        // px: 3,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'start',
          alignItems: 'center',
          gap: 2,
          my: 1,
          py: 5,
          [theme.breakpoints.down('sm')]: {
            pb: 1,
          },
        }}
      >
        <Box
          sx={{
            border: '1px solid #ccc',
            borderRadius: 4,
            cursor: 'pointer',
            width: 80,
            height: 80,
            p: 1.5,
            overflow: 'hidden',
            '&:hover': {
              border: '1px solid #f00',
            },
            [theme.breakpoints.down('sm')]: {
              width: 60,
              height: 60,
            },
          }}
        >
          <img src={image} alt="hinh-anh" width="100%" height="100%" />
        </Box>
        <Typography
          sx={{
            py: 0.5,
            px: 2,
            fontSize: '36px',
            fontFamily: 'inherit',
            fontWeight: 'bold',
            [theme.breakpoints.down('sm')]: {
              fontSize: '18px',
            },
          }}
        >
          {title}
        </Typography>
        <Button
          variant="outlined"
          sx={{
            color: 'gray',
            ml: 'auto',
            mr: 2,
            borderRadius: 4,
            textTransform: 'none',
            animation: `${upUp} 1s ease-in-out infinite alternate`,
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
          onClick={() => {
            navigate(
              `/bo-suu-tap/${brandId}?keyword=${keyword}&category=${categoryId}`,
            );
          }}
        >
          Xem tất cả
        </Button>
      </Box>
      <Box
        sx={{
          [theme.breakpoints.down('sm')]: {
            display: 'none',
          },
        }}
      >
        <Swiper
          style={{
            width: containerWidth,
            zIndex: 0,
            borderRadius: 5,
            padding: 2,
            // boxShadow:
            //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
            // padding: '30px',
          }}
          spaceBetween={30}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          // navigation
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {collections.map(
            (
              { _id, images, name, price, priceOrigin, id, brands, categories },
              index: any,
            ) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: '300px',
                  height: '460px',
                  minWidth: '300px',
                  background: '',
                  borderRadius: 20,
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  // overflow: 'hidden',
                  position: 'relative',
                  // transform: `${bestIndex === index ? 'scale(1.05)' : ''}`,
                  // transition:
                  //   'opacity .2s ease-in-out, transform .5s ease-in-out',
                  boxShadow:
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 0.5px rgba(0,0,0,0.05), 0px 0px 1px 0.5px rgba(0,0,0,0.05)',
                }}
              >
                <FavoriteBorderIcon
                  sx={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}
                />
                <Typography
                  sx={{
                    mr: 'auto',
                    p: 2,
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: 'gray',
                    fontFamily: 'inherit',
                    textTransform: 'capitalize',
                  }}
                >
                  {categories?.[0]?.name}
                </Typography>
                <Box
                  sx={{
                    width: '250px',
                    height: '250px',
                    display: 'flex',
                    justifyContent: 'center',
                    borderRadius: 3,
                    '&:hover': {},
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'100%'}
                    height={'100%'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography
                      sx={{
                        display: categories.some(
                          item => item.id === 'san-pham-moi',
                        )
                          ? ''
                          : 'none',
                        width: 'fit-content',
                        ml: 2,
                        py: 0.5,
                        px: 1,
                        fontFamily: 'inherit',
                        // fontSize: '12px',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        borderRadius: 2,
                        color: 'white',
                      }}
                    >
                      Sản phẩm mới
                    </Typography>
                    <Typography
                      className="two-line-typography"
                      sx={{
                        py: 1,
                        pb: 0.5,
                        px: 2,
                        fontFamily: 'inherit',
                        fontSize: '18px',
                        fontWeight: 'bold',
                        color: bestIndex === index ? 'red' : '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'red',
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                        py: 1,
                        px: 2,
                        // fontSize: '20px',
                      }}
                    >
                      {priceOrigin === 0
                        ? 'Liên hệ'
                        : formatCurrency(priceOrigin)}
                    </Typography>
                  </Box>
                  {/* <AddToCart productId={_id} quantity={1}>
                  <Button
                    sx={{
                      width: '100%',
                      display: 'flex',
                      gap: 1,
                      mt: 2,
                      px: 2,
                      color: 'white',
                      bgcolor: '#c1bdbd',
                      borderRadius: 1,
                      textTransform: 'none',
                      fontSize: '10px',
                      // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                      '&:hover': {
                        bgcolor: 'red',
                      },
                    }}
                  >
                    Thêm vào giỏ
                    <AddShoppingCartIcon />
                  </Button>
                </AddToCart> */}
                </Box>
                {/* <Typography
                sx={{
                  color: 'white',
                  bgcolor: 'red',
                  borderRadius: '2px 0 0 2px',
                  mr: 'auto',
                  px: 1,
                  fontSize: '',
                  fontWeight: '',
                  position: 'absolute',
                  // translate: '10px 0px',
                }}
              >
                Save {formatCurrency(save)}
              </Typography> */}
              </SwiperSlide>
            ),
          )}
        </Swiper>
      </Box>
      <Box
        sx={{
          [theme.breakpoints.up('sm')]: {
            display: 'none',
          },
        }}
      >
        <Swiper
          style={{
            width: containerWidth,
            zIndex: 0,
            borderRadius: 5,
            padding: 2,
            // boxShadow:
            //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
            // padding: '30px',
          }}
          spaceBetween={30}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          // navigation
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {collections.map(
            (
              { _id, images, name, price, priceOrigin, id, brands, categories },
              index: any,
            ) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: '200px',
                  height: '300px',
                  minWidth: '200px',
                  background: '',
                  borderRadius: 20,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  // overflow: 'hidden',
                  position: 'relative',
                  // transform: `${bestIndex === index ? 'scale(1.05)' : ''}`,
                  // transition:
                  //   'opacity .2s ease-in-out, transform .5s ease-in-out',
                  boxShadow:
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 0.5px rgba(0,0,0,0.05), 0px 0px 1px 0.5px rgba(0,0,0,0.05)',
                }}
              >
                <FavoriteBorderIcon
                  sx={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                    [theme.breakpoints.down('sm')]: {
                      display: 'none',
                    },
                  }}
                />
                <Typography
                  sx={{
                    mr: 'auto',
                    p: 2,
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: 'gray',
                    fontFamily: 'inherit',
                    textTransform: 'capitalize',
                    textWrap: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {categories?.[0]?.name}
                </Typography>
                <Box
                  sx={{
                    width: '150px',
                    height: '150px',
                    display: 'flex',
                    justifyContent: 'center',
                    borderRadius: 3,
                    '&:hover': {},
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'100%'}
                    height={'100%'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography
                      sx={{
                        display: categories.some(
                          item => item.id === 'san-pham-moi',
                        )
                          ? ''
                          : 'none',
                        width: 'fit-content',
                        ml: 2,
                        py: 0.5,
                        px: 1,
                        fontFamily: 'inherit',
                        fontSize: '10px',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        borderRadius: 2,
                        color: 'white',
                      }}
                    >
                      Sản phẩm mới
                    </Typography>
                    <Typography
                      className="two-line-typography"
                      sx={{
                        py: 1,
                        pb: 0.5,
                        px: 2,
                        fontFamily: 'inherit',
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: bestIndex === index ? 'red' : '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'red',
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                        py: 1,
                        px: 2,
                        fontSize: '12px',
                      }}
                    >
                      {priceOrigin === 0
                        ? 'Liên hệ'
                        : formatCurrency(priceOrigin)}
                    </Typography>
                  </Box>
                  {/* <AddToCart productId={_id} quantity={1}>
                  <Button
                    sx={{
                      width: '100%',
                      display: 'flex',
                      gap: 1,
                      mt: 2,
                      px: 2,
                      color: 'white',
                      bgcolor: '#c1bdbd',
                      borderRadius: 1,
                      textTransform: 'none',
                      fontSize: '10px',
                      // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                      '&:hover': {
                        bgcolor: 'red',
                      },
                    }}
                  >
                    Thêm vào giỏ
                    <AddShoppingCartIcon />
                  </Button>
                </AddToCart> */}
                </Box>
                {/* <Typography
                sx={{
                  color: 'white',
                  bgcolor: 'red',
                  borderRadius: '2px 0 0 2px',
                  mr: 'auto',
                  px: 1,
                  fontSize: '',
                  fontWeight: '',
                  position: 'absolute',
                  // translate: '10px 0px',
                }}
              >
                Save {formatCurrency(save)}
              </Typography> */}
              </SwiperSlide>
            ),
          )}
        </Swiper>
      </Box>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          p: 2,
          alignItems: 'center',
          [theme.breakpoints.up('sm')]: {
            display: 'none',
          },
        }}
      >
        <Button
          variant="outlined"
          sx={{
            color: 'gray',
            width: '100%',
            mb: 1,
            textTransform: 'none',
            fontSize: '12px',
            borderRadius: 3,
          }}
          onClick={() => {
            navigate(
              `/bo-suu-tap?keyword=${keyword}&brand=${brandId}&category=${categoryId}`,
            );
          }}
        >
          Xem tất cả
        </Button>
      </Box>
    </Box>
  );
};

export default SlideProducts;
