import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Card, Grid, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';
import { theme } from 'app/components/AppWrapper/theme';

import brand1 from 'assets/images/brand/1612_Soundpeats-1024x205.png';
import brand2 from 'assets/images/brand/60410b5d26ef2b00045692ec.png';
import brand3 from 'assets/images/brand/Earfun-logo.png';
import brand4 from 'assets/images/brand/Logo-kidcare.png';

interface Props {
  list?: any;
}

const BrandsList2: React.FC<Props> = list => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/brand`,
        );

        setBrands(result?.slideBrand);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        mx: 'auto',
        mt: 2,
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '30px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            // color: '#006cb1',
            mx: 'auto',
            p: 1,
            [theme.breakpoints.down('sm')]: { fontSize: '20px' },
          }}
        >
          Thương hiệu bạn đang tìm kiếm
        </Typography>
      </Box>
      {/* <Box
        sx={{
          display: 'flex',
          justifyContent: 'start',
          flexWrap: 'wrap',
          gap: 3,
          py: 5,
          [theme.breakpoints.down('md')]: {
            // width: 'fit-content',
            // justifyContent: 'start',
            // bgcolor: 'green',
            display: 'none',
          },
        }}
      >
        {brands.map(({ imageUrl, content, href }, index: any) => (
          <Box
            key={index}
            sx={{
              width: '200px',
              height: '50px',
              background: ``,
              borderRadius: 3,
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              // border: 0.5,
              // borderColor: '#ded8d8',
              boxShadow:
                'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 5px 10px -1px, rgba(0, 0, 0, 0.05) 0px 4px 6px -1px',
              // p: 0.5,
              cursor: 'pointer',
              transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
              transition: 'opacity .2s ease-in-out,transform .5s ease-in-out',
              '&:hover': {},
              [theme.breakpoints.down('sm')]: {
                width: '160px',
              },
            }}
            onClick={() => {
              if (content) navigate(`/bo-suu-tap/${content}`);
            }}
          >
            <img
              src={imageUrl}
              alt={content || ''}
              width={'100%'}
              height={'100%'}
              loading="lazy"
            />
          </Box>
        ))}
      </Box> */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          maxWidth: '1200px',
          mx: 'auto',
        }}
      >
        <Grid
          container
          spacing={{ xs: 0, sm: 2, md: 0, lg: 2, xl: 2 }}
          columns={{ xs: 2, sm: 3, md: 3, lg: 4, xl: 5 }}
          sx={{
            pl: 1,
            width: 'fit-content',
            justifyContent: 'start',
            // bgcolor: 'green',
            [theme.breakpoints.up('sm')]: {
              pl: 5,
            },
          }}
        >
          {brands.map(({ imageUrl, content, href }, index: any) => (
            <Grid
              xs={1}
              sm={1}
              md={1}
              lg={1}
              sx={{
                py: 2,
                [theme.breakpoints.down('sm')]: {
                  py: 1,
                },
              }}
              key={index}
            >
              <Card
                key={index}
                sx={{
                  width: '200px',
                  height: '50px',
                  background: ``,
                  borderRadius: 3,
                  display: 'flex',
                  alignItems: 'center',
                  overflow: 'hidden',
                  // border: 0.5,
                  // borderColor: '#ded8d8',
                  // boxShadow:
                  //   'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 5px 10px -1px, rgba(0, 0, 0, 0.05) 0px 4px 6px -1px',
                  // p: 0.5,
                  cursor: 'pointer',
                  transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                  transition:
                    'opacity .2s ease-in-out,transform .5s ease-in-out',
                  '&:hover': {},
                  [theme.breakpoints.down('sm')]: {
                    width: '160px',
                  },
                }}
                onClick={() => {
                  if (content) navigate(`/bo-suu-tap/${content}`);
                }}
              >
                <img
                  src={imageUrl}
                  alt={content || ''}
                  width={'100%'}
                  height={'100%'}
                  loading="lazy"
                />
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default BrandsList2;
