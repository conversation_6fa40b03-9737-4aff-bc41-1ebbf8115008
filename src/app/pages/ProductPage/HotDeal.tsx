import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

import brand1 from 'assets/images/brand/1612_Soundpeats-1024x205.png';
import brand2 from 'assets/images/brand/60410b5d26ef2b00045692ec.png';
import brand3 from 'assets/images/brand/Earfun-logo.png';
import brand4 from 'assets/images/brand/Logo-kidcare.png';

interface Props {
  list?: any;
}

const HotDeal: React.FC<Props> = list => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        setBrands(result?.slideBrand);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        mx: 'auto',
        my: 3,
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          borderBottom: 1,
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '30px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            // color: '#006cb1',
            mx: 'auto',
            p: 1,
          }}
        >
          Hot trong ngày
        </Typography>
        <Typography
          sx={{
            width: 100,
            py: '3px',
            bgcolor: '#42adef',
            position: 'absolute',
            bottom: 0,
            borderRadius: 5,
            mb: '-3px',
          }}
        ></Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'start',
          flexWrap: 'wrap',
          gap: 3,
          pt: 5,
          cursor: 'pointer',
        }}
      >
        <img
          src="https://phuonglinhjsc.vn/file/img/1724635585683_bannertreosoundpeatsjpg"
          alt="bannertreosoundpeats"
          width={'100%'}
          loading="lazy"
        />
      </Box>
    </Box>
  );
};

export default HotDeal;
