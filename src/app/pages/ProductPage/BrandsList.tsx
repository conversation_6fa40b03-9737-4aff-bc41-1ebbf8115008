import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';

import brand1 from 'assets/images/brand/1612_Soundpeats-1024x205.png';
import brand2 from 'assets/images/brand/60410b5d26ef2b00045692ec.png';
import brand3 from 'assets/images/brand/Earfun-logo.png';
import brand4 from 'assets/images/brand/Logo-kidcare.png';

interface Props {
  list?: any;
}

const BrandsList: React.FC<Props> = list => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/brand`,
        );

        setBrands(result?.slideBrand);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        display: 'flex',
        gap: 2,
        mx: 'auto',
        my: 1,
      }}
    >
      {/* <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          justifyContent: 'center',
          my: 0,
          // px: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: '25px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            textTransform: 'uppercase',
          }}
        >
          các thương hiệu
        </Typography>
        <Typography
          sx={{
            fontFamily: 'inherit',
            fontSize: '13px',
          }}
        >
          Mỗi thương hiệu thể hiện của sự đa dạng và tầm nhìn chiến lược của
          công ty
        </Typography>
      </Box> */}
      <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
          borderRadius: 5,
          paddingBottom: 1,
          paddingLeft: '30px',
          paddingRight: '30px',
          padding: '10px',
        }}
        spaceBetween={40}
        effect={'coverflow'}
        grabCursor={true}
        slidesPerView={'auto'}
        autoplay={{ delay: random, disableOnInteraction: true }}
        freeMode={true}
        modules={[Navigation, Pagination, Autoplay]}
        navigation={isNavigate}
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {brands.map(({ imageUrl, content, href }, index: any) => (
          <SwiperSlide
            key={index}
            onClick={() => {
              // window.open(href);
              if (content) navigate(`/bo-suu-tap/${content}`);
            }}
            onMouseEnter={() => {
              setBestIndex(index);
            }}
            onMouseLeave={() => {
              setBestIndex(-1);
            }}
            style={{
              width: 'auto',
              height: '100px',
              minWidth: '200px',
              background: '',
              borderRadius: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'start',
              position: 'relative',
              cursor: 'pointer',
            }}
          >
            <Box
              sx={{
                width: '200px',
                height: '50px',
                background: ``,
                borderRadius: 3,
                display: 'flex',
                alignItems: 'center',
                overflow: 'hidden',
                // border: 0.5,
                boxShadow:
                  'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
                transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                transition: 'opacity .2s ease-in-out,transform .5s ease-in-out',
                '&:hover': {},
              }}
            >
              {' '}
              <img
                src={imageUrl}
                alt={content || ''}
                width={'100%'}
                height={'100%'}
                loading="lazy"
              />
            </Box>
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default BrandsList;
