import Box from '@mui/material/Box';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
// import { Helmet } from 'react-helmet';
import { Person } from 'schema-dts';
import { helmetJsonLdProp } from 'react-schemaorg';
import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';

import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import QueueIcon from '@mui/icons-material/Queue';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import Card from '@mui/material/Card';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import ListItemIcon from '@mui/material';
import { useMediaQuery } from '@mui/material';
import get from 'lodash/get';
import set from 'lodash/set';
import { request } from 'utils/request';

import s3Client from 'utils/s3';
import { filterArrayByProperty } from 'utils/app';

import SlideBannerList from './SlideBannerList';
import SlideLead from './SlideLead';
import NewProductsList from './NewProductsList';
import BrandsList from './BrandsList';
import SlideProducts from './SlideProducts';
import Collection from './Collection';
import Recently from 'app/components/Recently/Recently';
import MoreAction from 'app/components/MoreAction/MoreAction';
import Partners from './Partners';
import AboutUs from './AboutUs';
import SlideMiddle from './SlideMiddleBanner';
import News from './News';
import Feedback from './Feedback';
import Industry from './Industry';
import WhyUs from './WhyUs';
import Services from './Services';

import banner1 from 'assets/images/banner/Banner-lead.jpg';
import banner2 from 'assets/images/event/banner_event-1.webp';
import banner3 from 'assets/images/banner/banner_5.webp';
import banner4 from 'assets/images/banner/Banner-slide1.jpg';
import banner5 from 'assets/images/banner/Banner-slide2.jpg';
import productType1 from 'assets/images/product/type/product_type_1.webp';
import productType2 from 'assets/images/product/type/product_type_2.webp';
import productType3 from 'assets/images/product/type/product_type_3.jpeg';
import productType4 from 'assets/images/product/type/product_type_4.webp';
import productType5 from 'assets/images/product/type/product_type_5.webp';

import { useUtilsSlice } from 'app/slice/utils';
import { useUserSlice } from 'app/slice/user';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import { uploadFiles } from 'utils/uploadImg';

const mainBanner =
  'https://phuonglinhjsc.vn/file/img/1750055850839_bannertrangchuphuonglinhjscpc1920x1000.jpg';
const mainBannerMobile =
  'https://phuonglinhjsc.vn/file/img/1750055858159_bannertrangchuphuonglinhjscmobile1920x1000.jpg';

const aboutUsContent =
  'PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng của mình những sản phẩm và dịch vụ chất lượng tốt nhất. Cam kết với đối tác sự uy tín và tôn trọng đồng thời không ngừng gia tăng giá trị qua thời gian. Giá trị cốt lõi của PhuongLinhJSC là xây dựng một doanh nghiệp đầu ngành, tạo ra sản phẩm hữu ích phục vụ khách hàng đồng thời mang lại nhiều việc làm đóng góp cho xã hội. \n PhuongLinhJSC luôn nỗ lực, cống hiến để mang đến những trải nghiệm tốt nhất trong lĩnh vực âm thanh, nghe nhìn cho khách hàng của mình. Chúng tôi luôn xem trải nghiệm khách hàng và lợi ích của đối tác là trọng tâm cho mọi xây dựng, phát triển của công ty.';

export function HomePage() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { actions: userAction } = useUserSlice();

  const userInfo = useSelector(userInfoSelector);

  const [openEdit, setOpenEdit] = React.useState<boolean>(false);
  const [bannerReward, setBannerReward] = React.useState<any>([]);
  const [newProductList, setNewProductList] = React.useState<any[]>([]);
  const [collectionList, setCollectionList] = React.useState<any[]>([]);
  const [brandList, setBreandList] = React.useState<any[]>([]);
  const [categoryList, setCategoryList] = React.useState<any[]>([]);
  const [partnerList, setPartnerList] = React.useState<any[]>([]);
  const [partnerImages, setPartnerImages] = React.useState<any[]>([]);
  const [collectionImageList, setCollectionImageList] = React.useState<any[]>(
    [],
  );
  const [imageIndex, setImageIndex] = React.useState<number>(-1);
  const [us, setUs] = React.useState<any>({
    aboutUs: {
      content: aboutUsContent,
      imageUrl: '',
    },
    whyUs: [],
  });

  const [middleBanner, setMidderBanner] = React.useState<any>({
    imageUrl: banner3,
    title: 'Home Appliances',
    subTitle: 'Decorate and heal your life.',
  });

  const [typeIndex, setTypeIndex] = React.useState<any>();
  const [type1, setType1] = React.useState<any>({
    background: productType1,
    title: 'Gia Dụng',
  });
  const [type2, setType2] = React.useState<any>({
    background: '',
    title: 'Vật Dụng Cá Nhân',
  });
  const [type3, setType3] = React.useState<any>({
    background: productType3,
    title: 'Phụ Kiện Di Động',
  });
  const [type4, setType4] = React.useState<any>({
    background: productType4,
    title: 'Phụ Kiện Để Bàn',
  });
  const [type5, setType5] = React.useState<any>({
    background: productType5,
    title: 'Phụ Kiện Thời Trang',
  });

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [homeSchema, setHomeSchema] = React.useState<any>({});
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setHomeSchema(result);
        // setBannerReward(result?.banner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    const fetchUs = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/us`,
        );

        // console.log({ result });
        setUs(result.us);
        // setBannerReward(result?.banner);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    fetchUs();
    // apiHandler();
  };
  const fetchBrands = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'BRAND' },
        );

        // console.log('brands: ', result);
        setBreandList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  const fetchCategories = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/collections`,
          { type: 'CATEGORY' },
        );

        // console.log('CATEGORY: ', result);
        setCategoryList(result);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    fetchHomePage();
    // fetchBrands();
    // fetchCategories();
  }, []);

  React.useEffect(() => {
    const {
      banner,
      us,
      middleBanner,
      categoryList,
      newProducts,
      collectionList,
      slidePartner,
    } = homeSchema;
    if (banner && banner.length > 0) {
      setBannerReward(banner[0]);
    }

    if (slidePartner && slidePartner.length > 0) {
      const newImages = slidePartner?.map(item => {
        return { url: item.imageUrl };
      });
      setPartnerImages(newImages);
    }

    if (us) {
      setUs(us);
    }

    if (middleBanner) {
      setMidderBanner(middleBanner);
    }

    if (slidePartner) {
      setPartnerList(slidePartner);
    }

    if (categoryList && categoryList.length === 5) {
      setType1({
        background: get(categoryList, '[0].imageUrl'),
        title: get(categoryList, '[0].collectionInfo.name'),
      });
      setType2({
        background: get(categoryList, '[1].imageUrl'),
        title: get(categoryList, '[1].collectionInfo.name'),
      });
      setType3({
        background: get(categoryList, '[2].imageUrl'),
        title: get(categoryList, '[2].collectionInfo.name'),
      });
      setType4({
        background: get(categoryList, '[3].imageUrl'),
        title: get(categoryList, '[3].collectionInfo.name'),
      });
      setType5({
        background: get(categoryList, '[4].imageUrl'),
        title: get(categoryList, '[4].collectionInfo.name'),
      });
    }

    if (newProducts && newProducts.length) {
      setNewProductList(newProducts);
    }

    // if (collectionList && collectionList.length) {
    //   // console.log('collectionList: ', collectionList)
    //   setCollectionList([
    //     collectionList.filter((item: any) => !get(item, 'imageUrl'))[0],
    //   ]);

    //   setCollectionImageList(
    //     collectionList.filter((item: any) => get(item, 'imageUrl')),
    //   );
    // }
  }, [homeSchema]);

  const onClose = () => {
    setOpenEdit(false);
  };
  const handleEditHome = async () => {
    try {
      const newPartnerList = await Promise.all(
        partnerImages.map(async (image: any) => {
          let { file, name, url = '' } = image;
          if (file && name) {
            // const uploadInfo = await s3Client.uploadFile(
            //   `phuonglinh/partnerImage/${Date.now()}-${name}`,
            //   file,
            // );

            // url = get(uploadInfo, 'Location');
            url = await uploadFiles([file], 'partner', '');
          }
          return { imageUrl: url, content: '', href: '' };
        }),
      );
      const newHomeSchema = { ...homeSchema, slidePartner: newPartnerList };
      setHomeSchema(newHomeSchema);
      const result: any = await request(
        'put',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/upsert`,
        newHomeSchema,
      );

      // console.log({ result });
      setHomeSchema(result);
      setOpenEdit(false);
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const vietnameseToAscii = inputStr => {
    // Chuyển đổi từ tiếng Việt có dấu sang không dấu
    var convertedStr = inputStr
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');

    // Thay đổi dấu cách thành '-'
    convertedStr = convertedStr.replace(/\s+/g, '-');

    return convertedStr;
  };

  const labelOptions = [
    {
      value: 'phone',
      label: 'phone',
    },
    {
      value: 'email',
      label: 'email',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Trang chủ</title>
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
          fontFamily: 'inherit',
        }}
      >
        <Box
          sx={{
            width: '100%',
            minHeight: '400px',
            aspectRatio: {
              xs: '9/16', // Mobile banner ratio (900x1600)
              sm: '9/16', // Mobile banner ratio
              md: '1920/1000', // Desktop banner ratio
              lg: '1920/1000', // Desktop banner ratio
            },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'center',
            gap: 1,
            pl: { xs: 2, sm: 4, md: 8, lg: '200px' },
            pr: { xs: 2, sm: 4, md: 8 },
            backgroundImage: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url("${mainBannerMobile}")`,
            '@media (max-width: 599px)': {
              aspectRatio: '9/16',
              backgroundSize: 'contain',
            },
            '@media (min-width: 600px) and (max-width: 899px)': {
              aspectRatio: '9/16',
              backgroundSize: 'contain',
            },
            '@media (min-width: 900px) and (max-width: 1199px)': {
              aspectRatio: '1920/1000',
              backgroundImage: `linear-gradient(rgba(0,0,0,0.1), rgba(0,0,0,0.1)), url("${mainBanner}")`,
              backgroundSize: 'contain',
            },
            '@media (min-width: 1200px)': {
              aspectRatio: '1920/1000',
              backgroundImage: `url("${mainBanner}")`,
              backgroundSize: 'contain',
            },
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
            // Tablet landscape optimization
            '@media (orientation: landscape) and (max-height: 768px)': {
              aspectRatio: 'auto',
              height: '100vh',
              backgroundSize: 'cover',
              backgroundPosition: 'center center',
            },

            backgroundAttachment: { xs: 'scroll', lg: 'fixed' },
            fontSize: { xs: '16px', sm: '18px', md: '22px', lg: '25px' },
            position: 'relative',
            overflow: 'hidden',
            // Performance optimizations
            willChange: 'transform',
            backfaceVisibility: 'hidden',
            // Smooth scrolling on mobile
            WebkitOverflowScrolling: 'touch',
            // Add loading placeholder
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: '#f5f5f5',
              backgroundImage:
                'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
              backgroundSize: '20px 20px',
              backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
              opacity: 0,
              transition: 'opacity 0.3s ease',
              zIndex: -1,
            },
          }}
          onClick={() => {
            navigate(
              '/tin-tuc/phuong-linh-jsc-tuyen-%C4%91ai-ly-phan-phoi-toan-quoc',
            );
          }}
        >
          {/* <Button
            sx={{
              display: { xs: 'none', sm: 'flex', md: 'flex' },
              alignItems: 'center',
              gap: { xs: 1, sm: 2 },
              ml: { xs: 0, sm: 2, md: 4, lg: 10 },
              mt: { xs: 2, sm: 3, md: 4 },
              textTransform: 'none',
              bgcolor: 'white',
              fontSize: { xs: '14px', sm: '15px', md: '16px' },
              fontWeight: 'bold',
              fontFamily: 'inherit',
              px: { xs: 3, sm: 4, md: 5 },
              py: { xs: 1, sm: 1.5, md: 2 },
              borderRadius: { xs: 3, sm: 4, md: 5 },
              cursor: 'pointer',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              transition: 'all 0.3s ease',
              '&:hover': {
                color: 'black',
                bgcolor: 'rgba(255,255,255,0.95)',
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
              },
            }}
          >
            Xem thêm
            <ArrowForwardIcon
              sx={{
                fontSize: '16px',
                bgcolor: '#22c1f1',
                color: 'black',
                borderRadius: '100%',
              }}
            />
            <img src="https://phuonglinhjsc.vn/file/img/1750205819598_xemthem.png" alt="xemthem" />
          </Button> */}
        </Box>
        <AboutUs
          banner={us?.aboutUs?.imageUrl}
          content={us?.aboutUs?.content}
          href={us?.aboutUs?.href}
          list={us?.whyUs}
        />
        <BrandsList />
        <Services />
        <WhyUs />
        <Industry />
        <Partners />
        <SlideProducts />

        {/* <Recently /> */}
        <Feedback />
        <News />
      </Paper>
    </>
  );
}
