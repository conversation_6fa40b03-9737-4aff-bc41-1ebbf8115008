import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Button, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';
import { theme } from 'app/components/AppWrapper/theme';

const Services: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [hover, setHover] = React.useState<string>('');
  const [image, setImage] = React.useState<string>(
    'https://phuonglinhjsc.vn/file/img/1727250683105_mangluoitoanquocpng',
  );
  const [toggle, setToggle] = React.useState<boolean>(false);
  const [content, setContent] = React.useState<string>(
    'Phương Linh JSC đã xây dựng được mạng lưới phân phối trên toàn quốc; gồm các cửa hàng thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam. Chúng tôi đã liên kết và cung cấp dịch vụ giao hàng tận nơi cho tất cả cửa hàng truyền thống và thương mại điện tử ở Việt Nam.',
  );
  const [title, setTitle] = React.useState<string>('Mạng lưới toàn cầu');
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);
  const [whyUs, setWhyUs] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log(result);
        // setBrands(result?.slidePartner);
        setWhyUs(result?.us?.whyUs);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    // apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  useEffect(() => {
    setToggle(!toggle);
  }, [title]);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      sx={{
        width: '100%',
        height: '900px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: -1,
        px: 2,
        py: 10,
        borderRadius: '3px',
        background: `url("https://phuonglinhjsc.vn/file/img/1727317853268_sectioncacdichvuphuonglinhjsc1920x860.jpg")`,
        backgroundRepeat: 'none',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        [theme.breakpoints.down('md')]: {
          height: 1800,
        },
        [theme.breakpoints.down('sm')]: {
          height: 3400,
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: '1200px',
          textAlign: 'center',
          display: 'flex',
          justifyContent: 'center',
          gap: 3,
          [theme.breakpoints.down('md')]: {
            maxWidth: '100vw',
          },
        }}
      >
        <Box
          sx={{
            flex: 2,
            display: 'flex',
            justifyContent: 'end',
            gap: 3,
            [theme.breakpoints.down('md')]: {
              flexDirection: 'column',
              flex: 1,
              width: '100%',
            },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'end',
              gap: 3,
              pt: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Tiếp cận phù hợp');
                  setContent(
                    'Việc tiếp cận phù hợp giúp công ty chúng tôi tạo ra những trải nghiệm khách hàng cá nhân hóa, đáp ứng chính xác mong muốn của từng người.',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250693799_tiepcanphuhoppng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250654295_bannertiepcanphuhopphuonglinhjsc360x250jpg"
                  alt="banner-tiep-can-phu-hop-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250693799_tiepcanphuhoppng'
                      }
                      alt="mang-luoi-toan-quoc"
                      height={'100%'}
                      width={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Tiếp cận phù hợp'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    // fontSize: '18px',
                    // fontWeight: 'bold',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Việc tiếp cận phù hợp giúp công ty chúng tôi tạo ra những trải nghiệm khách hàng cá nhân hóa, đáp ứng chính xác mong muốn của từng người.'
                  }
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Tiếp thị bán hàng');
                  setContent(
                    'Phát triển kế hoạch tiếp thị các chiến dịch quảng cáo, khuyến mãi và các hoạt động PR nhằm tiếp cận khách hàng',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250696642_tiepthibanhangpng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250632288_bannerdichvutrongoiphuonglinhjsc440x290jpg"
                  alt="banner-dich-vu-tron-goi-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250696642_tiepthibanhangpng'
                      }
                      alt="mang-luoi-toan-quoc"
                      height={'100%'}
                      width={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Tiếp thị bán hàng'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    // fontSize: '18px',
                    // fontWeight: 'bold',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Phát triển kế hoạch tiếp thị các chiến dịch quảng cáo, khuyến mãi và các hoạt động PR nhằm tiếp cận khách hàng'
                  }
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Đội ngũ chuyên nghiệp');
                  setContent(
                    'Chúng tôi có kiến thức tốt về thấu hiểu khách hàng và cách khai thác nó để xây dựng chiến lược cho thương hiệu.',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250669541_doinguchuyennghieppng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250637071_bannerdoinguchuyennghiepphuonglinhjsc280x190jpg"
                  alt="banner-doi-ngu-chuyen-nghiep-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250669541_doinguchuyennghieppng'
                      }
                      alt="mang-luoi-toan-quoc"
                      height={'100%'}
                      width={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Đội ngũ chuyên nghiệp'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    // fontSize: '18px',
                    // fontWeight: 'bold',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Chúng tôi có kiến thức tốt về thấu hiểu khách hàng và cách khai thác nó để xây dựng chiến lược cho thương hiệu.'
                  }
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'start',
              gap: 3,
              pb: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Mạng lưới toàn cầu');
                  setContent(
                    'Xây dựng được mạng lưới phân phối trên toàn quốc; gồm các cửa hàng thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250683105_mangluoitoanquocpng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250642974_bannermangluoitoanquocphuonglinhjsc360x250jpg"
                  alt="banner-mang-luoi-toan-quoc-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250683105_mangluoitoanquocpng'
                      }
                      alt="mang-luoi-toan-quoc"
                      width={'100%'}
                      height={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Mạng lưới toàn cầu'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    width: '100%',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Xây dựng được mạng lưới phân phối trên toàn quốc; gồm các cửa hàng thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam'
                  }
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Nhanh chóng tin cậy');
                  setContent(
                    'Xây dựng được mạng lưới phân phối trên toàn quốc; gồm các cửa hàng thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250688750_nhanhchongtincaypng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250647602_bannernhanhchongtincayphuonglinhjsc320x220jpg"
                  alt="banner-nhanh-chong-tin-cay-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    onMouseEnter={() => setHover('mang-luoi-toan-quoc')}
                    onMouseLeave={() => setHover('')}
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250688750_nhanhchongtincaypng'
                      }
                      alt="mang-luoi-toan-quoc"
                      width={'100%'}
                      height={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Nhanh chóng tin cậy'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    // fontSize: '18px',
                    // fontWeight: 'bold',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Xây dựng được mạng lưới phân phối trên toàn quốc; gồm các cửa hàng thương mại điện tử và chuỗi cửa hàng truyền thống trên khắp Việt Nam'
                  }
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                [theme.breakpoints.down('sm')]: {
                  flexDirection: 'column',
                  gap: 2,
                },
              }}
            >
              <Box
                sx={{
                  width: 'fit-content',
                  borderRadius: 5,
                  overflow: 'hidden',
                  cursor: 'pointer',
                }}
                onMouseEnter={() => {
                  setTitle('Dịch vụ trọn gói');
                  setContent(
                    'Cung cấp các dịch vụ trọn gói cho các sản phẩm của thương hiệu được phân phối tại Việt Nam bao gồm phương pháp lập kế hoạch, hậu cần, tiếp thị và hậu mãi',
                  );
                  setImage(
                    'https://phuonglinhjsc.vn/file/img/1727250665544_dichvutrongoipng',
                  );
                }}
              >
                <img
                  src="https://phuonglinhjsc.vn/file/img/1727250660025_bannertiepthibanhangphuonglinhjsc360x250jpg"
                  alt="banner-tiep-thi-ban-hang-phuong-linh-jsc"
                  height={'100%'}
                  width={'100%'}
                  loading="lazy"
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  px: 3,
                  [theme.breakpoints.up('md')]: {
                    display: 'none',
                  },
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    textAlign: 'center',
                    position: 'relative',
                    display: 'flex',
                    // flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    onMouseEnter={() => setHover('mang-luoi-toan-quoc')}
                    onMouseLeave={() => setHover('')}
                    sx={{
                      cursor: 'pointer',
                      transform:
                        hover === 'mang-luoi-toan-quoc' || toggle
                          ? 'rotateY(360deg)'
                          : '',
                      transition: 'all 3s ease-in-out',
                      width: '60px',
                    }}
                  >
                    <img
                      src={
                        'https://phuonglinhjsc.vn/file/img/1727250665544_dichvutrongoipng'
                      }
                      alt="mang-luoi-toan-quoc"
                      width={'100%'}
                      height={'100%'}
                      loading="lazy"
                    />
                  </Box>
                  <Typography
                    sx={{
                      fontSize: '25px',
                      fontWeight: 'bold',
                      fontFamily: 'inherit',
                      fontStyle: 'italic',
                      mx: 'auto',
                      p: 1,
                    }}
                  >
                    {'Dịch vụ trọn gói'}
                  </Typography>
                  <Typography
                    sx={{
                      width: 100,
                      py: '2px',
                      background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                      position: 'absolute',
                      bottom: 0,
                      borderRadius: 5,
                      mb: '-2px',
                    }}
                  ></Typography>
                </Box>
                <Typography
                  sx={{
                    // fontSize: '18px',
                    // fontWeight: 'bold',
                    fontFamily: 'inherit',
                    // fontStyle: 'italic',
                    color: 'white',
                    bgcolor: '#51a3dc',
                    mx: 'auto',
                    mt: 1,
                    p: 3,
                    borderRadius: 7,
                    transform: toggle ? 'rotateY(360deg)' : '',
                    transition: 'all .5s ease-in-out',
                  }}
                >
                  {
                    'Cung cấp các dịch vụ trọn gói cho các sản phẩm của thương hiệu được phân phối tại Việt Nam bao gồm phương pháp lập kế hoạch, hậu cần, tiếp thị và hậu mãi'
                  }
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            [theme.breakpoints.down('md')]: {
              display: 'none',
              flex: 1,
              width: '100%',
            },
          }}
        >
          <Box
            sx={{
              width: '100%',
              textAlign: 'center',
              position: 'relative',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              onMouseEnter={() => setHover('mang-luoi-toan-quoc')}
              onMouseLeave={() => setHover('')}
              sx={{
                cursor: 'pointer',
                transform:
                  hover === 'mang-luoi-toan-quoc' || toggle
                    ? 'rotateY(360deg)'
                    : '',
                transition: 'all 3s ease-in-out',
              }}
            >
              <img
                src={image}
                alt="mang-luoi-toan-quoc"
                height={'100%'}
                width={'100%'}
                loading="lazy"
              />
            </Box>
            <Typography
              sx={{
                fontSize: '25px',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                fontStyle: 'italic',
                mx: 'auto',
                p: 1,
              }}
            >
              {title}
            </Typography>
            <Typography
              sx={{
                width: 100,
                py: '2px',
                background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
                position: 'absolute',
                bottom: 0,
                borderRadius: 5,
                mb: '-2px',
              }}
            ></Typography>
          </Box>
          <Typography
            sx={{
              // fontSize: '18px',
              // fontWeight: 'bold',
              fontFamily: 'inherit',
              // fontStyle: 'italic',
              color: 'white',
              bgcolor: '#51a3dc',
              mx: 'auto',
              mt: 10,
              p: 3,
              borderRadius: 10,
              transform: toggle ? 'rotateY(360deg)' : '',
              transition: 'all .5s ease-in-out',
            }}
          >
            {content}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default Services;
