import React from 'react';

import { Box, Button, Card, Typography } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Avatar from '@mui/material/Avatar';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { theme } from 'app/components/AppWrapper/theme';
import { ImQuotesLeft } from 'react-icons/im';

import { backupTime } from 'utils/moment';
import { request } from 'utils/request';

const Feedback: React.FC = () => {
  const navigate = useNavigate();

  const [noneVideos, setNoneVideos] = React.useState<any[]>([]);

  // TODO: get list banners from api
  const convertMongoCreatedAt = createdAt => {
    const now = new Date().getTime();
    const createdAtDate = new Date(createdAt).getTime();

    // Tính số mili giây chênh lệch giữa thời điểm hiện tại và thời điểm tạo
    const diffMilliseconds = now - createdAtDate;

    // Tính số phút chênh lệch
    const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));

    // Tính số giờ chênh lệch
    const diffHours = Math.floor(diffMilliseconds / (1000 * 60 * 60));

    // Tính số ngày chênh lệch
    const diffDays = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24));
    if (!createdAt) return '';
    if (diffHours < 1) {
      return `${diffMinutes} phút trước`;
    } else if (diffDays < 1) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 2) {
      return `${diffDays} ngày trước`;
    } else {
      return backupTime(createdAt); // Trả về thời gian tạo gốc nếu đã quá 2 ngày
    }
  };

  const fetchNewsPage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/news`,
        );

        // console.log({ result });
        const newNoneVideos = () => {
          return result.filter(item => item.label !== 'video');
        };
        setNoneVideos(newNoneVideos);
        // console.log('noneVideo: ', newNoneVideos);
      } catch (error) {
        console.log(error);
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    // fetchNewsPage();
  }, []);

  const onClickRef = id => {
    navigate(`/tin-tuc/${id}`);
  };

  const TruncatedTypography = styled(Typography)`
    padding: 0;
    overflow: hidden;
    font-family: inherit;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  `;

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        my: 3,
        background: `url("https://phuonglinhjsc.vn/file/img/1727319541304_sectionphanhoitukhachhangphuonglinhjsc1920x860.jpg")`,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        p: 10,
        [theme.breakpoints.down('sm')]: {
          p: 2,
        },
      }}
    >
      <Box
        sx={{
          maxWidth: '1200px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: '100%',
          mx: 'auto',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            height: '100%',
            gap: 2,
            fontSize: '30px',
            [theme.breakpoints.down('md')]: {
              flexDirection: 'column',
            },
            [theme.breakpoints.down('sm')]: {
              fontSize: '26px',
            },
          }}
        >
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              p: 2,
              color: 'white',
            }}
          >
            <Typography
              sx={{
                fontSize: 'inherit',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                textTransform: 'uppercase',
              }}
            >
              khách hàng nói gì về chúng tôi
            </Typography>
            <Typography
              sx={{
                // fontSize: '30px',
                // fontWeight: 'bold',
                fontFamily: 'inherit',
              }}
            >
              "Đóng góp của khách hàng là vô giá đối với Phương Linh JSC. Chúng
              tôi rất trân trọng những phản hồi tích cực và sẽ tiếp tục cải
              thiện để phục vụ tốt hơn."
            </Typography>
            {/* <Button
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                width: 'fit-content',
                textTransform: 'none',
                background: '#1bc7f79f',
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                px: 5,
                py: 1,
                borderRadius: 5,
                cursor: 'pointer',
                '&:hover': {
                  color: 'black',
                },
              }}
            >
              Xem thêm
              <ArrowForwardIcon
                sx={{
                  fontSize: '16px',
                  bgcolor: 'white',
                  color: 'black',
                  borderRadius: '100%',
                }}
              />
            </Button> */}
          </Box>
          <Box
            sx={{
              flex: 2,
              height: '100%',
              display: 'flex',
              gap: 5,
              [theme.breakpoints.down('md')]: {
                flex: 1,
              },
              [theme.breakpoints.down('sm')]: {
                flex: 1,
                flexDirection: 'column',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 5,
                justifyContent: 'space-between',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  justifyContent: 'space-between',
                  p: 2,
                  bgcolor: 'white',
                  borderRadius: 3,
                  position: 'relative',
                }}
              >
                <Typography
                  sx={{
                    position: 'absolute',
                    top: '-30px',
                    right: 10,
                    color: '#3dc3ec',
                  }}
                >
                  <ImQuotesLeft fontSize={60} />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                  }}
                >
                  {
                    '"Phương Linh JSC không chỉ cung cấp sản phẩm tốt mà còn tạo cảm giác như được chăm sóc thật sự. Cảm ơn vì sự chu đáo <3 "'
                  }
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                  }}
                >
                  <Avatar
                    alt="Avata"
                    src="https://phuonglinhjsc.vn/file/img/1727250591309_vanhaijpg"
                    imgProps={{ loading: 'lazy' }}
                  />
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                      }}
                    >
                      Văn Hải
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: 'inherit',
                      }}
                    >
                      Khách hàng
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  justifyContent: 'space-between',
                  p: 2,
                  bgcolor: 'white',
                  borderRadius: 3,
                  position: 'relative',
                }}
              >
                <Typography
                  sx={{
                    position: 'absolute',
                    top: '-30px',
                    right: 10,
                    color: '#3dc3ec',
                  }}
                >
                  <ImQuotesLeft fontSize={60} />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                  }}
                >
                  "Mình đã mua một số thiết bị công nghệ từ Phương Linh JSC phân
                  phối ngon phết ưng nhất là mấy cái bảo hành 12 tháng 😀 "
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                  }}
                >
                  <Avatar
                    alt="Avata"
                    src="https://phuonglinhjsc.vn/file/img/1727250587861_thuyjpg"
                    imgProps={{ loading: 'lazy' }}
                  />
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                      }}
                    >
                      Thuỷ
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: 'inherit',
                      }}
                    >
                      Khách hàng
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 5,
                justifyContent: 'space-between',
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  justifyContent: 'space-between',
                  p: 2,
                  bgcolor: 'white',
                  borderRadius: 3,
                  position: 'relative',
                }}
              >
                <Typography
                  sx={{
                    position: 'absolute',
                    top: '-30px',
                    right: 10,
                    color: '#3dc3ec',
                  }}
                >
                  <ImQuotesLeft fontSize={60} />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                  }}
                >
                  "Sản phẩm vừa chất lượng lại vừa có giá rẻ. Giao hàng nhanh
                  chóng 👍 "
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                  }}
                >
                  <Avatar
                    alt="Avata"
                    src="https://phuonglinhjsc.vn/file/img/1727250583908_thuyhangjpg"
                    imgProps={{ loading: 'lazy' }}
                  />
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                      }}
                    >
                      Thuý Hằng
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: 'inherit',
                      }}
                    >
                      Khách hàng
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  justifyContent: 'space-between',
                  p: 2,
                  bgcolor: 'white',
                  borderRadius: 3,
                  position: 'relative',
                }}
              >
                <Typography
                  sx={{
                    position: 'absolute',
                    top: '-30px',
                    right: 10,
                    color: '#3dc3ec',
                  }}
                >
                  <ImQuotesLeft fontSize={60} />
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'inherit',
                  }}
                >
                  "Lần đầu tiên mua sp bên Phương Linh JSC, và thật sự ấn tượng
                  với sự hỗ trợ nhanh chóng và tận tâm. Sẽ quay lại chắc chắn!"
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                  }}
                >
                  <Avatar
                    alt="Avata"
                    src="https://phuonglinhjsc.vn/file/img/1727250576581_minhhieujpg"
                    imgProps={{ loading: 'lazy' }}
                  />
                  <Box>
                    <Typography
                      sx={{
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                      }}
                    >
                      Minh Hiếu
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: 'inherit',
                      }}
                    >
                      Khách hàng
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Feedback;
