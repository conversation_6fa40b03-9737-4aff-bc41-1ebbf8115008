import React, { useEffect } from 'react';
import { Box, Button, Typography } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';

const bannerMobile1 =
  'https://phuonglinhjsc.vn/file/img/1727327741533_bannerchamsocphuonglinhjsc300x300.jpg';
const bannerMobile2 =
  'https://phuonglinhjsc.vn/file/img/1727327760754_bannercongnghephuonglinhjsc300x300.jpg';
const bannerMobile3 =
  'https://phuonglinhjsc.vn/file/img/1727327776760_bannerphukienphuonglinhjsc300x300.jpg';

const Industry: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 10,
        pt: 5,
        [theme.breakpoints.down('sm')]: {
          mt: -10,
        },
        // clipPath: 'polygon(50% 4%, 100% 0%, 100% 100%, 0 100%, 0 0)',
        // px: 3,
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          // borderBottom: 1,
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            color: '#006cb1',
            mx: 'auto',
            px: 1,
            pb: 2,
            [theme.breakpoints.down('md')]: {
              fontSize: '26px',
            },
          }}
        >
          NGÀNH HÀNG KINH DOANH
        </Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 3,
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            maxWidth: '100vw',
            px: 1,
          },
        }}
      >
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
            fontSize: '30px',
            [theme.breakpoints.down('md')]: {
              fontSize: '25px',
            },
            [theme.breakpoints.down('sm')]: {
              flexDirection: 'row',
              justifyContent: 'center',
              flex: 0,
              width: '100%',
              fontSize: '25px',
            },
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.down('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727328451346_bannerchamsocphuonglinhjsc300x300.jpg"
              alt="banner-cham-soc"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.up('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src={bannerMobile1}
              alt="banner-cham-soc"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Typography
              sx={{
                width: '250px',
                height: '150px',
                fontSize: 'inherit',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                color: '',
                textAlign: 'center',
                mx: 'auto',
                p: 5,
                [theme.breakpoints.down('md')]: {
                  width: 200,
                  height: 'auto',
                  p: 2,
                },
              }}
            >
              Chăm sóc sức khoẻ
            </Typography>
            <ArrowForwardIcon
              sx={{
                fontSize: 'inherit',
                bgcolor: '#21a1f1',
                color: 'white',
                borderRadius: '100%',
                cursor: 'pointer',
              }}
              onClick={() => {
                navigate('/san-pham');
              }}
            />
          </Box>
        </Box>
        <Box
          sx={{
            flex: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
            fontSize: '30px',
            [theme.breakpoints.down('md')]: {
              flex: 1.1,
              fontSize: '25px',
            },
            [theme.breakpoints.down('sm')]: {
              flexDirection: 'row',
              justifyContent: 'center',
              flex: 0,
              width: '100%',
              fontSize: '25px',
            },
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.down('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727328461574_bannernhanhcongnghephuonglinhjsc300x600.jpg"
              alt="banner-nhanh-cong-nghe"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.up('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src={bannerMobile2}
              alt="banner-nhanh-cong-nghe"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Typography
              sx={{
                width: '250px',
                height: '150px',
                fontSize: 'inherit',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                color: '',
                textAlign: 'center',
                mx: 'auto',
                p: 5,
                [theme.breakpoints.down('md')]: {
                  width: 200,
                  height: 'auto',
                  p: 2,
                },
              }}
            >
              Thiết bị công nghệ
            </Typography>
            <ArrowForwardIcon
              sx={{
                fontSize: 'inherit',
                bgcolor: '#21a1f1',
                color: 'white',
                borderRadius: '100%',
                cursor: 'pointer',
              }}
              onClick={() => {
                navigate('/san-pham');
              }}
            />
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
            fontSize: '30px',
            [theme.breakpoints.down('md')]: {
              fontSize: '25px',
              height: '100%',
            },
            [theme.breakpoints.down('sm')]: {
              flexDirection: 'row',
              justifyContent: 'center',
              flex: 0,
              width: '100%',
              fontSize: '25px',
            },
          }}
        >
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.down('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727328465972_bannerphukienphuonglinhjsc300x300.jpg"
              alt="banner-phu-kien-phuong"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              [theme.breakpoints.up('md')]: {
                display: 'none',
              },
            }}
          >
            <img
              src={bannerMobile3}
              alt="banner-phu-kien-phuong"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Typography
              sx={{
                width: '250px',
                height: '150px',
                fontSize: 'inherit',
                fontWeight: 'bold',
                fontFamily: 'inherit',
                color: '',
                textAlign: 'center',
                mx: 'auto',
                p: 5,
                pb: 0,
                [theme.breakpoints.down('md')]: {
                  width: 200,
                  height: 'auto',
                  p: 2,
                },
              }}
            >
              Phụ kiện công nghệ
            </Typography>
            <ArrowForwardIcon
              sx={{
                fontSize: 'inherit',
                bgcolor: '#21a1f1',
                color: 'white',
                borderRadius: '100%',
                cursor: 'pointer',
              }}
              onClick={() => {
                navigate('/san-pham');
              }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Industry;
