import React, { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { Box, Button, Typography } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';
import { theme } from 'app/components/AppWrapper/theme';

const bannerMobile =
  'https://phuonglinhjsc.vn/file/img/1727319467885_sectionthongsophuonglinhjscmobile900x1600.jpg';

const WhyUs: React.FC = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [number1, setNumber1] = React.useState<any>(0);
  const [number2, setNumber2] = React.useState<any>(0);
  const [number3, setNumber3] = React.useState<any>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [isAction, setIsAction] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );
  const [intervalId, setIntervalId] = React.useState<any>();
  const [isVisible, setIsVisible] = React.useState(false);

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  useEffect(() => {
    // fetchHomePage();
  }, []);

  const useIntersectionObserver = (): React.RefObject<HTMLDivElement> => {
    const ref = useRef<HTMLDivElement>(null);
    const onIntersect = () => setIsAction(true);
    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            onIntersect(); // Trigger the callback when the component is visible
          }
        },
        { threshold: 0.1 },
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      };
    }, []);

    return ref;
  };

  useEffect(() => {
    if (isAction) {
      setIntervalId(
        setInterval(() => {
          setNumber1(pre => pre + 1);
          setNumber3(pre => pre + 1);
          setNumber2(pre => pre + 100000);
        }, 100),
      );
    }
  }, [isAction]);

  useEffect(() => {
    if (number1 >= 10) {
      clearInterval(intervalId);
      setNumber1('100+');
      setNumber2('1 Tr');
      setNumber3('10+');
      return;
    }
  }, [number1]);

  useEffect(() => {
    if (isAction) {
      // Kích hoạt hiệu ứng sau khi component được mount
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 100); // Delay 100ms trước khi hiệu ứng bắt đầu
      return () => clearTimeout(timer);
    }
  }, [isAction]);

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={useIntersectionObserver()}
      sx={{
        width: '100%',
        height: '600px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: -1,
        px: 2,
        py: 10,
        borderRadius: '3px',
        background: `url("https://phuonglinhjsc.vn/file/img/1727319378989_sectionthongsophuonglinhjsc1920x860.jpg")`,
        clipPath: 'polygon(0 0, 100% 0%, 100% 95%, 50% 100%, 0 95%)',
        opacity: isVisible ? 1 : 0.2,
        transition: 'opacity 2s ease-in',
        [theme.breakpoints.down('md')]: {
          height: 550,
        },
        [theme.breakpoints.down('sm')]: {
          background: `url(${bannerMobile})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          height: 1300,
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: 1200,
          textAlign: 'center',
          borderBottom: 1,
          borderColor: 'white',
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            color: 'white',
            mx: 'auto',
            p: 1,
            [theme.breakpoints.down('md')]: {
              fontSize: '40px',
            },
          }}
        >
          LỰA CHỌN CHÚNG TÔI
        </Typography>
        <Typography
          sx={{
            width: 200,
            py: '3px',
            bgcolor: '#006cb1',
            position: 'absolute',
            bottom: 0,
            borderRadius: 5,
            mb: '-3px',
          }}
        ></Typography>
      </Box>
      <Box
        sx={{
          maxWidth: 1200,
          display: 'flex',
          justifyContent: 'space-between',
          gap: 3,
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            height: 'fit-content',
            flex: 1,
            gap: 1,
            justifyContent: 'center',
            mt: -30,
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 3,
            [theme.breakpoints.down('sm')]: {
              gap: 1,
              height: 'fit-content',
            },
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            {number1}
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Đối tác trên toàn quốc
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
            [theme.breakpoints.down('sm')]: {
              gap: 1,
            },
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            {number2}
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Sản phẩm bán ra
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 3,
            [theme.breakpoints.down('sm')]: {
              gap: 1,
            },
          }}
        >
          <Typography
            sx={{
              fontSize: '70px',
              fontWeight: 900,
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            {number3}
          </Typography>
          <Typography
            sx={{
              fontSize: '25px',
              fontWeight: 'bold',
              fontFamily: 'inherit',
              color: 'white',
            }}
          >
            Năm thành lập
          </Typography>
          <Typography
            sx={{
              // fontSize: '40px',
              fontFamily: 'inherit',
              color: 'white',
              textAlign: 'center',
            }}
          >
            PhuongLinhJSC được thành lập với tầm nhìn mang đến cho khách hàng
            của mình những
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default WhyUs;
