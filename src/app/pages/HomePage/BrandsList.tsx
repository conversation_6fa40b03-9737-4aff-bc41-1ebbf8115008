import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useCallback, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Swiper as SwiperType } from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, IconButton, Typography } from '@mui/material';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { request } from 'utils/request';
import get from 'lodash/get';
import { theme } from 'app/components/AppWrapper/theme';

interface Props {
  list?: any;
}

const BrandsList: React.FC<Props> = () => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [brands, setBrands] = React.useState<any[]>([]);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/brand`,
        );

        setBrands(result.slideBrand);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };
  useEffect(() => {
    fetchHomePage();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  const swiperRef = useRef<SwiperType>();

  return (
    <Box
      onMouseEnter={() => {
        setIsNavigate(true);
      }}
      onMouseLeave={() => {
        setIsNavigate(false);
      }}
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        display: 'flex',
        gap: 2,
        mx: 'auto',
        my: 1,
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'column',
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          justifyContent: 'center',
          my: 2,
          px: 1,
        }}
      >
        <Typography
          sx={{
            fontSize: '25px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            textTransform: 'uppercase',
            textWrap: 'nowrap',
          }}
        >
          các thương hiệu
        </Typography>
        <Typography
          sx={{
            fontFamily: 'inherit',
            fontSize: '16px',
          }}
        >
          Mỗi thương hiệu thể hiện của sự đa dạng và tầm nhìn chiến lược của
          công ty
        </Typography>
      </Box>
      <Box
        sx={{
          width: containerWidth - 300,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'end',
          [theme.breakpoints.down('sm')]: {
            width: '100%',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: 3,
            alignItems: 'center',
            [theme.breakpoints.down('sm')]: {
              display: 'none',
            },
          }}
        >
          <IconButton
            sx={{
              display: 'flex',
              justifyContent: 'center',
              color: 'black',
            }}
            onClick={() => swiperRef.current?.slidePrev()}
          >
            <ArrowBackIosNewIcon />
          </IconButton>
          <IconButton
            sx={{
              display: 'flex',
              justifyContent: 'center',
              color: 'black',
            }}
            onClick={() => swiperRef.current?.slideNext()}
          >
            <ArrowForwardIosIcon />
          </IconButton>
        </Box>
        <Swiper
          style={{
            width: '100%',
            zIndex: 0,
            borderRadius: 5,
            padding: '10px',
          }}
          spaceBetween={12}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          // navigation={isNavigate}
          // navigation={{
          //   prevEl: navigationPrevRef.current,
          //   nextEl: navigationNextRef.current,
          // }}
          onBeforeInit={swiper => {
            swiperRef.current = swiper;
          }}
          // onBeforeInit={(swiper) => {
          //   swiper.navigation.nextEl = navigationNextRef.current;
          //   swiper.navigation.prevEl = navigationPrevRef.current;
          // }}
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {brands.map(({ imageUrl, content, href }, index: any) => (
            <SwiperSlide
              key={index}
              onClick={() => {
                window.open(href);
              }}
              onMouseEnter={() => {
                setBestIndex(index);
              }}
              onMouseLeave={() => {
                setBestIndex(-1);
              }}
              style={{
                width: 'auto',
                height: '100px',
                // minWidth: '200px',
                background: '',
                borderRadius: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'start',
                position: 'relative',
              }}
            >
              <Box
                sx={{
                  width: '200px',
                  height: 'auto',
                  background: ``,
                  borderRadius: '',
                  display: 'flex',
                  alignItems: 'center',
                  transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                  transition:
                    'opacity .2s ease-in-out,transform .5s ease-in-out',
                  '&:hover': {},
                  [theme.breakpoints.down('sm')]: {
                    width: 120,
                  },
                }}
              >
                {' '}
                <img
                  src={imageUrl}
                  alt={content || ''}
                  width={'100%'}
                  height={'100%'}
                  loading="lazy"
                />
              </Box>
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    </Box>
  );
};

export default BrandsList;
