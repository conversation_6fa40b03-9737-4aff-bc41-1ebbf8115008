import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography, Card } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Grid from '@mui/material/Unstable_Grid2';
import { theme } from 'app/components/AppWrapper/theme';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { keyframes } from '@emotion/react';
import { useNavigate } from 'react-router-dom';

import bestSale1 from 'assets/images/product/Lens_Protector.webp';
import bestSale2 from 'assets/images/product/screen_protector_01.webp';

const InitListBestSales = [
  {
    background: bestSale1,
    href: 'bestSale-1',
    title: 'Miếng dán bảo vệ Camera',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
  },
  {
    background: bestSale2,
    href: 'bestSale-2',
    title: 'Miếng dán bảo vệ màn hình',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
  },
];

const animation1 = keyframes`
  0% {
    transform: translate(-30px, -30px)
  }
  50% {
    transform: translate(-10px, -20px)
  }
  100% {
    transform: translate(0px, 0px)
  }
`;
const animation2 = keyframes`
  0% {
    transform: translate(0px, 0px) rotate(0deg)
  }
  50% {
    transform: translate(-20px, -20px) rotate(25deg)
  }
  100% {
    transform: translate(-30px, -30px) rotate(45deg)
  }
`;

interface Props {
  title?: string;
  buttonTitle?: string;
  banner?: string;
  content?: string;
  href?: string;
  list?: any[];
}

const AboutUs: React.FC<Props> = ({ banner, content, href, list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [whyUsIndex, setWhyUsIndex] = React.useState<number>(1);
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [bestSales, setBestSales] = React.useState<any[]>(InitListBestSales);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (product: string) => {
    if (product) {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Product not exist',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'start',
        gap: 5,
        width: '100%',
        height: 800,
        position: 'relative',
        my: 0,
        borderRadius: 1,
        background:
          'url("https://phuonglinhjsc.vn/file/img/1727259697684_bannervechungtoiphuonglinhjsc1920x870.jpg")',
        [theme.breakpoints.down('md')]: {
          background:
            'url("https://phuonglinhjsc.vn/file/img/1727259748108_bannervechungtoiphuonglinhjsc900x1600.jpg")',
          flexDirection: 'column-reverse',
          height: 900,
        },
        [theme.breakpoints.down('sm')]: {
          background:
            'url("https://phuonglinhjsc.vn/file/img/1727259748108_bannervechungtoiphuonglinhjsc900x1600.jpg")',
          flexDirection: 'column-reverse',
          height: 1150,
        },
        // bgcolor: '#b00202',
      }}
    >
      <Box
        sx={{
          width: '150px',
          height: '150px',
          position: 'absolute',
          top: 50,
          left: 170,
          [theme.breakpoints.down('md')]: {
            display: 'none',
          },
        }}
      >
        <img
          src="https://phuonglinhjsc.vn/file/img/1727250437160_2png"
          alt=""
          width={'100%'}
          height={'auto'}
          loading="lazy"
        />
      </Box>
      <Box
        sx={{
          width: '900px',
          pt: 10,
          pl: '350px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
          [theme.breakpoints.down('md')]: {
            pt: 20,
            width: 'auto',
            px: 2,
          },
        }}
      >
        <Typography
          sx={{
            fontWeight: 900,
            fontSize: '60px',
            fontFamily: 'inherit',
            color: '#2d8bde',
            [theme.breakpoints.down('md')]: {
              fontSize: '40px',
            },
          }}
        >
          Về chúng tôi
        </Typography>
        <Typography
          sx={{
            whiteSpace: 'pre-wrap',
            fontFamily: 'inherit',
            color: 'gray',
          }}
        >
          {content}
        </Typography>
        <Button
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            mt: 10,
            textTransform: 'none',
            color: 'white',
            background: 'linear-gradient(to right, #19d7e4, #42b4f6)',
            fontSize: '20px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            px: 5,
            py: 2,
            borderRadius: 10,
            cursor: 'pointer',
            '&:hover': {
              color: 'black',
            },
            [theme.breakpoints.down('md')]: {
              // display: 'none',
              fontSize: '16px',
              mt: 2,
              px: 2,
              py: 1.5,
            },
          }}
        >
          Liên hệ với chúng tôi
          <ArrowForwardIcon
            sx={{
              fontSize: '20px',
              bgcolor: 'white',
              color: 'black',
              borderRadius: '100%',
            }}
          />
        </Button>
      </Box>
      <Box
        sx={{
          pt: 10,
          [theme.breakpoints.down('md')]: {
            width: '300px',
            height: '300px',
            mx: 'auto',
          },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          <img
            src="https://phuonglinhjsc.vn/file/img/1727250443102_anhvechungtoiphuonglinhjsc600x700png"
            alt="anh-ve-chung-toi-phuong-linh-jsc"
            height={'auto'}
            width={'100%'}
            loading="lazy"
          />
          <Box
            sx={{
              position: 'absolute',
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727250458025_khunganhvechungtoiphuonglinhjsc600x700png"
              alt="khung-anh-ve-chung-toi-phuong-linh-jsc"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              position: 'absolute',
              top: 100,
              left: 1,
              animation: `${animation1} 2s linear infinite alternate`,
              [theme.breakpoints.down('md')]: {
                width: '40px',
                height: '40px',
                mx: 'auto',
              },
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727250452506_xanhpng"
              alt="xanh"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
          <Box
            sx={{
              position: 'absolute',
              bottom: 50,
              right: 1,
              rotate: '-45deg',
              animation: `${animation2} 3s linear infinite alternate`,
              [theme.breakpoints.down('md')]: {
                width: '80px',
                // height: '50px',
                mx: 'auto',
              },
            }}
          >
            <img
              src="https://phuonglinhjsc.vn/file/img/1727250447756_dopng"
              alt="do"
              height={'100%'}
              width={'100%'}
              loading="lazy"
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AboutUs;
