import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import AddToCart from 'app/components/AppWrapper/AddToCart';
import { theme } from 'app/components/AppWrapper/theme';
import { request } from 'utils/request';
import get from 'lodash/get';
interface Props {
  collection?: any;
  id?: string;
}

const SlideProducts: React.FC<Props> = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [products, setProducts] = React.useState<any[]>([]);

  useEffect(() => {
    // console.log('collection: ', collection)
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);
  useEffect(() => {
    const fetchNewProduct = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v2/products`,
          { categoryId: 'noi-bat' },
        );

        // console.log({ result });
        setProducts(result.products);
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };
    fetchNewProduct();
  }, []);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        my: 3,
        pt: 5,
        // clipPath: 'polygon(50% 4%, 100% 0%, 100% 100%, 0 100%, 0 0)',
        // px: 3,
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          borderBottom: 1,
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: 'bold',
            fontFamily: 'inherit',
            color: '#006cb1',
            mx: 'auto',
            p: 1,
            [theme.breakpoints.down('sm')]: {
              fontSize: 30,
            },
          }}
        >
          SẢN PHẨM NỔI BẬT
        </Typography>
        <Typography
          sx={{
            width: 200,
            py: '3px',
            bgcolor: '#006cb1',
            position: 'absolute',
            bottom: 0,
            borderRadius: 5,
            mb: '-3px',
          }}
        ></Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: 2,
          my: 2,
          py: 5,
          fontSize: '20px',
          [theme.breakpoints.down('sm')]: {
            fontSize: 14,
          },
        }}
      >
        {/* <Typography
          sx={{
            fontWeight: 'bold',
            textTransform: 'uppercase',
          }}
        >
          {collection?.name}
        </Typography>
        <Button onClick={() => navigate(`/bo-suu-tap/${id}`)}>
          Xem tất cả
        </Button> */}
        <Typography
          sx={{
            py: 0.5,
            px: 2,
            color: 'white',
            bgcolor: '#42b4f6',
            fontSize: 'inherit',
            fontWeight: 'bold',
            borderRadius: 10,
            cursor: 'pointer',
          }}
          onClick={() => navigate('/bo-suu-tap')}
        >
          Tất cả
        </Typography>
        <Typography
          sx={{
            py: 0.5,
            px: 2,
            fontSize: 'inherit',
            fontWeight: 'bold',
            cursor: 'pointer',
          }}
          onClick={() => {
            navigate('/bo-suu-tap?keyword=tai nghe');
          }}
        >
          Tai nghe
        </Typography>
        <Typography
          sx={{
            py: 0.5,
            px: 2,
            fontSize: 'inherit',
            fontWeight: 'bold',
            cursor: 'pointer',
          }}
          onClick={() => {
            navigate('/san-pham');
          }}
        >
          Phụ kiện
        </Typography>
      </Box>
      <Box
        sx={{
          [theme.breakpoints.down('sm')]: {
            display: 'none',
          },
        }}
      >
        <Swiper
          style={{
            width: containerWidth,
            zIndex: 0,
            borderRadius: 5,
            padding: 2,
            // boxShadow:
            //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
            // padding: '30px',
          }}
          spaceBetween={20}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          // navigation
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {products.map(
            (
              {
                _id,
                images,
                name,
                price,
                priceOrigin,
                id,
                collectionInfos,
                brands,
                categories,
              },
              index: any,
            ) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: 'auto',
                  height: 'auto',
                  // minWidth: '300px',
                  background: '',
                  borderRadius: 20,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  // overflow: 'hidden',
                  position: 'relative',
                  // transform: `${bestIndex === index ? 'scale(1.05)' : ''}`,
                  // transition:
                  //   'opacity .2s ease-in-out, transform .5s ease-in-out',
                  boxShadow:
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 0.5px rgba(0,0,0,0.05), 0px 0px 1px 0.5px rgba(0,0,0,0.05)',
                }}
              >
                <FavoriteBorderIcon
                  sx={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}
                />
                <Typography
                  sx={{
                    mr: 'auto',
                    p: 2,
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: 'gray',
                    fontFamily: 'inherit',
                    textTransform: 'capitalize',
                    [theme.breakpoints.down('sm')]: {
                      fontSize: 14,
                    },
                  }}
                >
                  {categories[0]?.name || ''}
                </Typography>
                <Box
                  sx={{
                    width: '300px',
                    height: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    borderRadius: 3,
                    '&:hover': {},
                    [theme.breakpoints.down('sm')]: {
                      width: 130,
                    },
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'80%'}
                    height={'80%'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    width: '300px',
                    height: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    [theme.breakpoints.down('sm')]: {
                      width: 130,
                      fontSize: 14,
                    },
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography
                      sx={{
                        display: categories.some(
                          item => item.id === 'san-pham-moi',
                        )
                          ? ''
                          : 'none',
                        width: 'fit-content',
                        ml: 2,
                        py: 0.5,
                        px: 1,
                        fontFamily: 'inherit',
                        fontSize: '12px',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        borderRadius: 2,
                        color: 'white',
                        [theme.breakpoints.down('sm')]: {
                          fontSize: 10,
                        },
                      }}
                    >
                      Sản phẩm mới
                    </Typography>
                    <Typography
                      className="two-line-typography"
                      sx={{
                        py: 1,
                        pb: 0.5,
                        px: 2,
                        fontFamily: 'inherit',
                        fontSize: 'inherit',
                        fontWeight: 'bold',
                        color: bestIndex === index ? 'red' : '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'red',
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                        fontSize: 'inherit',
                        py: 1,
                        px: 2,
                        // fontSize: '20px',
                      }}
                    >
                      {priceOrigin === 0
                        ? 'Liên hệ'
                        : formatCurrency(priceOrigin)}
                    </Typography>
                  </Box>
                  {/* <AddToCart productId={_id} quantity={1}>
                  <Button
                    sx={{
                      width: '100%',
                      display: 'flex',
                      gap: 1,
                      mt: 2,
                      px: 2,
                      color: 'white',
                      bgcolor: '#c1bdbd',
                      borderRadius: 1,
                      textTransform: 'none',
                      fontSize: '10px',
                      // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                      '&:hover': {
                        bgcolor: 'red',
                      },
                    }}
                  >
                    Thêm vào giỏ
                    <AddShoppingCartIcon />
                  </Button>
                </AddToCart> */}
                </Box>
                {/* <Typography
                sx={{
                  color: 'white',
                  bgcolor: 'red',
                  borderRadius: '2px 0 0 2px',
                  mr: 'auto',
                  px: 1,
                  fontSize: '',
                  fontWeight: '',
                  position: 'absolute',
                  // translate: '10px 0px',
                }}
              >
                Save {formatCurrency(save)}
              </Typography> */}
              </SwiperSlide>
            ),
          )}
        </Swiper>
      </Box>
      <Box
        sx={{
          [theme.breakpoints.up('sm')]: {
            display: 'none',
          },
        }}
      >
        <Swiper
          style={{
            width: containerWidth,
            zIndex: 0,
            borderRadius: 5,
            padding: 2,
            // boxShadow:
            //   '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
            // padding: '30px',
          }}
          spaceBetween={20}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          // navigation
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {products.map(
            (
              {
                _id,
                images,
                name,
                price,
                priceOrigin,
                id,
                collectionInfos,
                brands,
                categories,
              },
              index: any,
            ) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: '200px',
                  height: '300px',
                  minWidth: '200px',
                  background: '',
                  borderRadius: 20,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  // overflow: 'hidden',
                  position: 'relative',
                  // transform: `${bestIndex === index ? 'scale(1.05)' : ''}`,
                  // transition:
                  //   'opacity .2s ease-in-out, transform .5s ease-in-out',
                  boxShadow:
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 0.5px rgba(0,0,0,0.05), 0px 0px 1px 0.5px rgba(0,0,0,0.05)',
                }}
              >
                <FavoriteBorderIcon
                  sx={{
                    position: 'absolute',
                    right: 10,
                    top: 10,
                  }}
                />
                <Typography
                  sx={{
                    mr: 'auto',
                    p: 2,
                    fontSize: '10px',
                    fontWeight: 'bold',
                    color: 'gray',
                    fontFamily: 'inherit',
                    textTransform: 'capitalize',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {categories[0]?.name || ''}
                </Typography>
                <Box
                  sx={{
                    width: '150px',
                    height: '150px',
                    display: 'flex',
                    justifyContent: 'center',
                    borderRadius: 3,
                    '&:hover': {},
                    [theme.breakpoints.down('sm')]: {
                      width: 130,
                    },
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'100%'}
                    height={'100%'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    fontSize: '14px',
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography
                      sx={{
                        display: categories.some(
                          item => item.id === 'san-pham-moi',
                        )
                          ? ''
                          : 'none',
                        width: 'fit-content',
                        ml: 2,
                        py: 0.5,
                        px: 1,
                        fontFamily: 'inherit',
                        fontSize: '12px',
                        background:
                          'linear-gradient(to right, #19d7e4, #42b4f6)',
                        borderRadius: 2,
                        color: 'white',
                        [theme.breakpoints.down('sm')]: {
                          fontSize: 10,
                        },
                      }}
                    >
                      Sản phẩm mới
                    </Typography>
                    <Typography
                      className="two-line-typography"
                      sx={{
                        py: 1,
                        pb: 0.5,
                        px: 2,
                        fontFamily: 'inherit',
                        fontSize: 'inherit',
                        fontWeight: 'bold',
                        color: bestIndex === index ? 'red' : '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Typography
                      sx={{
                        color: 'red',
                        fontWeight: 'bold',
                        fontFamily: 'inherit',
                        fontSize: 'inherit',
                        py: 1,
                        px: 2,
                        [theme.breakpoints.down('sm')]: {
                          fontSize: 12,
                        },
                        // fontSize: '20px',
                      }}
                    >
                      {priceOrigin === 0
                        ? 'Liên hệ'
                        : formatCurrency(priceOrigin)}
                    </Typography>
                  </Box>
                  {/* <AddToCart productId={_id} quantity={1}>
                  <Button
                    sx={{
                      width: '100%',
                      display: 'flex',
                      gap: 1,
                      mt: 2,
                      px: 2,
                      color: 'white',
                      bgcolor: '#c1bdbd',
                      borderRadius: 1,
                      textTransform: 'none',
                      fontSize: '10px',
                      // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                      '&:hover': {
                        bgcolor: 'red',
                      },
                    }}
                  >
                    Thêm vào giỏ
                    <AddShoppingCartIcon />
                  </Button>
                </AddToCart> */}
                </Box>
                {/* <Typography
                sx={{
                  color: 'white',
                  bgcolor: 'red',
                  borderRadius: '2px 0 0 2px',
                  mr: 'auto',
                  px: 1,
                  fontSize: '',
                  fontWeight: '',
                  position: 'absolute',
                  // translate: '10px 0px',
                }}
              >
                Save {formatCurrency(save)}
              </Typography> */}
              </SwiperSlide>
            ),
          )}
        </Swiper>
      </Box>
    </Box>
  );
};

export default SlideProducts;
