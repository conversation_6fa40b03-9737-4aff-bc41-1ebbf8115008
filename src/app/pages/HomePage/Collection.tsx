import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import AddToCart from 'app/components/AppWrapper/AddToCart';
import { theme } from 'app/components/AppWrapper/theme';
import new1 from 'assets/images/product/new/new_01.webp';
import new2 from 'assets/images/product/new/new_02.webp';
import new3 from 'assets/images/product/new/new_03.webp';
import new4 from 'assets/images/product/new/new_04.webp';
import banner from 'assets/images/banner/banner_6.webp';

const InitListProduct = [
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
];

interface Props {
  title?: string;
  banner?: string;
  id?: string;
  list?: any;
}

const Collection: React.FC<Props> = ({ title, id, banner, list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [isNavigate, setIsNavigate] = React.useState<boolean>(false);
  const [isHover, setIsHover] = React.useState<boolean>(false);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [products, setProducts] = React.useState<any[]>(list);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (product: string) => {
    if (product) {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Product not exist',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
      // console.log('width2: ', containerRef.current.offsetWidth);
    }
  }, [width]);

  useEffect(() => {
    setProducts(list);
    // console.log('list1: ', list);
  }, [list]);

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        height: 'auto',
        display: 'flex',
        background: '#444242',
        borderRadius: 1,
        overflow: 'hidden',
        boxShadow:
          '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
        [theme.breakpoints.down('md')]: {
          flexDirection: 'column',
        },
      }}
    >
      <Box
        onMouseEnter={() => {
          setIsHover(true);
        }}
        onMouseLeave={() => {
          setIsHover(false);
        }}
        sx={{
          minWidth: '450px',
          height: '450px',
          position: 'relative',
          display: 'flex',
          [theme.breakpoints.down('md')]: {
            width: '100%',
            height: 'auto',
          },
        }}
      >
        <img
          src={banner}
          alt=""
          width={'100%'}
          height={'auto'}
          loading="lazy"
        />
        <Typography
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'start',
            p: 1,
            color: isHover ? 'red' : 'white',
            fontWeight: '600',
            fontSize: '16px',
            textTransform: 'capitalize',
            position: 'absolute',
          }}
        >
          {title}
        </Typography>
      </Box>
      {/* <Button
          sx={{
            height: 'fit-content',
            bgcolor: '',
            textTransform: 'capitalize',
            px: 3,
            py: 0,
            mt: '80px',
            mx: 'auto',
            [theme.breakpoints.up('md')]: {
              display: 'none',
            },
          }}
        >
          Xem tất cả
        </Button> */}
      <Box
        onMouseEnter={() => {
          setIsNavigate(true);
        }}
        onMouseLeave={() => {
          setIsNavigate(false);
        }}
        ref={containerRef}
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'start',
          gap: 0,
          borderRadius: 0,
          background: 'white',
          pb: '10px',
          overflow: 'hidden',
          // ml: ' auto',
          [theme.breakpoints.down('md')]: {
            width: '100%',
          },
        }}
      >
        <Swiper
          style={{
            width: containerWidth - 10,
            height: '100%',
            zIndex: 0,
            borderRadius: 5,
            paddingTop: '10px',
            paddingLeft: '10px',
            margin: 0,
          }}
          spaceBetween={10}
          effect={'coverflow'}
          grabCursor={true}
          slidesPerView={'auto'}
          autoplay={{ delay: random, disableOnInteraction: true }}
          freeMode={true}
          modules={[Navigation, Pagination, Autoplay]}
          navigation={isNavigate}
          // pagination={{ clickable: true }}
          // scrollbar={{ draggable: true }}
        >
          {products?.map(
            ({ _id, images, name, price, priceOrigin, id }, index: any) => (
              <SwiperSlide
                key={index}
                onClick={() => {
                  navigate(`/san-pham/${id}`, {
                    state: { _id, images, name, price, priceOrigin },
                  });
                }}
                onMouseEnter={() => {
                  setBestIndex(index);
                }}
                onMouseLeave={() => {
                  setBestIndex(-1);
                }}
                style={{
                  width: '200px',
                  height: '360px',
                  minWidth: '200px',
                  background: 'white',
                  borderRadius: 5,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  overflow: 'hidden',
                  position: 'relative',
                  cursor: 'pointer',
                  // border: '0.5px solid gray',
                  // transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                  transition:
                    'opacity .2s ease-in-out, box-shadow .2s ease-in-out',
                  boxShadow: `${
                    bestIndex === index
                      ? '0px 0px 10px 1px rgba(0,0,0,0.05), 0px 0px 10px 1px rgba(0,0,0,0.05)'
                      : '0px 0px 1px 1px rgba(0,0,0,0.05), 0px 0px 1px 1px rgba(0,0,0,0.05)'
                  }`,
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    height: '50%',
                    // background: `url("${
                    //   bestIndex === index ? background1 : background2
                    // }")`,
                    // backgroundRepeat: 'no-repeat',
                    // backgroundSize: 'cover',
                    // backgroundPosition: 'center',
                    borderRadius: '0 0 5px 5px',
                    display: 'flex',
                    // transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                    transition:
                      'opacity .2s ease-in-out,background .5s ease-in-out',
                    '&:hover': {},
                  }}
                >
                  <img
                    src={images[0]?.url}
                    alt={name}
                    width={'100%'}
                    height={'200px'}
                    loading="lazy"
                  />
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'end',
                    p: 3,
                    // position: 'absolute',
                    // background: 'black'
                  }}
                >
                  <Typography
                    className="two-line-typography"
                    sx={{
                      color: bestIndex === index ? 'red' : '',
                      // fontSize: '12px',
                    }}
                  >
                    {name}
                  </Typography>
                  {/* <Typography
                      sx={{
                        color: '',
                        // fontSize: '20px',
                      }}
                    >
                      {name}
                    </Typography> */}
                  <Typography
                    sx={{
                      color: 'red',
                      fontWeight: 'bold',
                      // fontSize: '20px',
                    }}
                  >
                    {priceOrigin === 0
                      ? 'Liên hệ'
                      : formatCurrency(priceOrigin)}
                  </Typography>
                  <AddToCart productId={_id} quantity={1}>
                    <Button
                      sx={{
                        width: '100%',
                        display: 'flex',
                        gap: 1,
                        mt: 2,
                        px: 2,
                        color: 'black',
                        bgcolor: '#c1bdbd',
                        borderRadius: 1,
                        textTransform: 'none',
                        fontSize: '10px',
                        // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                        '&:hover': {
                          bgcolor: 'red',
                          color: 'white',
                          // fontWeight: 'bold',
                        },
                      }}
                    >
                      Thêm vào giỏ
                      <AddShoppingCartIcon />
                    </Button>
                  </AddToCart>
                </Box>
                {/* <Typography
                    sx={{
                      color: 'white',
                      bgcolor: 'red',
                      borderRadius: '2px 0 0 2px',
                      mr: 'auto',
                      px: 1,
                      fontSize: '',
                      fontWeight: '',
                      position: 'absolute',
                      // translate: '10px 0px',
                    }}
                  >
                    Save {formatCurrency(save)}
                  </Typography> */}
              </SwiperSlide>
            ),
          )}
        </Swiper>
        <Button
          sx={{
            display: products.length > 0 ? '' : 'none',
            height: 'fit-content',
            bgcolor: 'white',
            color: 'red',
            textTransform: 'none',
            fontWeight: 'bold',
            px: 5,
            py: 1,
            mx: 'auto',
            border: '1px solid red',
            '&:hover': {
              bgcolor: 'green',
              color: 'white',
              border: 'none',
              // fontWeight: 'bold',
            },
            // boxShadow:
            //   '0px 0px 20px 1px rgba(143, 2, 2, 0.255), 0px 0px 20px 1px rgba(188, 4, 4, 0.212)',
            [theme.breakpoints.down('md')]: {
              display: 'none',
            },
          }}
          onClick={() => navigate(`/bo-suu-tap/${id}`)}
        >
          Xem tất cả
        </Button>
      </Box>
    </Box>
  );
};

export default Collection;
