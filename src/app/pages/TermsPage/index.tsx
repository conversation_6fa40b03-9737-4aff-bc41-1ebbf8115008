import Box from '@mui/material/Box';
// import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import {
  useParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import QueueIcon from '@mui/icons-material/Queue';
import CreateIcon from '@mui/icons-material/Create';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import SendIcon from '@mui/icons-material/Send';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import AccountBoxIcon from '@mui/icons-material/AccountBox';
import PhoneIphoneIcon from '@mui/icons-material/PhoneIphone';
import EmailIcon from '@mui/icons-material/Email';
import InputAdornment from '@mui/material/InputAdornment';
import MuiDialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import { TextField, useMediaQuery } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { userInfoSelector } from 'app/slice/user/selectors';
import './styles.css';
import { editorConfigJodit, createMarkup } from 'utils/jodit';
import Maps from 'app/components/GooogleMaps/Maps';
import { request } from 'utils/request';
import { useUtilsSlice } from 'app/slice/utils';
import get from 'lodash/get';
import set from 'lodash/set';

import 'jodit';
// import 'jodit/build/jodit.min.css';
import JoditEditor from 'jodit-react';
import { initAbout } from './about';

import banner1 from 'assets/images/about/banner-phuonglinhjsc_32ef92e5a0aa44bb85dd4e0859cfa370_grande.webp';
import { IconButton } from '@mui/material';

export function TermsPage() {
  const userInfo = useSelector(userInfoSelector);
  const [isEditTerms, setIsEditTerms] = useState<boolean>(false);
  const [data, setData] = useState<any>(initAbout);
  const [footer, setFooter] = useState<any>({});
  const [name, setName] = useState<string>('');
  const [policyIndex, setPolicyindex] = useState<number>(-1);

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { path } = params;
  const pathname = useLocation().pathname;

  const findObjects = (arr, key, value) => {
    return arr.find(item => item[key] === value);
  };
  function findIndexForObject(arr, key, value) {
    return arr.findIndex(item => item[key] === value);
  }

  const [homeSchema, setHomeSchema] = React.useState<any>({});
  const fetchHomePage = () => {
    const apiHandler = async () => {
      try {
        const result: any = await request(
          'get',
          `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/single`,
          { type: 'HOME' },
        );

        // console.log({ result });
        setHomeSchema(result);
        setFooter(result?.footer);
        const findPolicy = findObjects(result?.footer?.policy, 'id', path);
        setData(findPolicy?.detail);
        setPolicyindex(findIndexForObject(result?.footer?.policy, 'id', path));
        // console.log('fetch: ', findObjects(result?.footer?.policy, 'id', path))
      } catch (error) {
        return dispatch(
          utilsAction.showSnackbar({
            message: get(error, 'message', 'Error Undefined'),
            variant: 'error',
          }),
        );
      }
    };

    apiHandler();
  };

  React.useEffect(() => {
    fetchHomePage();
    // console.log('path: ', path);
    // console.log('pathname: ', pathname);
  }, [path]);

  const handleEditHome = async () => {
    try {
      const newPolicy = footer.policy;
      newPolicy[policyIndex] = { ...newPolicy[policyIndex], detail: data };
      const newHomeSchema = {
        ...homeSchema,
        footer: { ...footer, policy: newPolicy },
      };
      setHomeSchema(newHomeSchema);
      const result: any = await request(
        'put',
        `${process.env.REACT_APP_BACKEND_URL}/api/v1/pages/upsert`,
        newHomeSchema,
      );

      // console.log({ result });
      setHomeSchema(result);
      setIsEditTerms(false);
    } catch (error) {
      return dispatch(
        utilsAction.showSnackbar({
          message: get(error, 'message', 'Error Undefined'),
          variant: 'error',
        }),
      );
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleEditProduct = () => {};

  const AddDetailDialog = () => {
    return (
      <MuiDialog
        disableEnforceFocus
        fullScreen={!useMediaQuery(theme.breakpoints.up('md'))}
        fullWidth={true}
        maxWidth="md"
        open={isEditTerms}
        onClose={() => setIsEditTerms(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            backgroundColor: '#efeff1',
            margin: 0,
            height: '80vh',
            [theme.breakpoints.down('sm')]: {
              height: '100vh',
            },
            overflow: 'hidden',
          },
        }}
      >
        <DialogContent
          sx={{
            padding: 5,
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignContent: 'center',
              mb: 1,
            }}
          >
            <Typography
              sx={{
                color: 'orangered',
                fontWeight: 'bold',
              }}
            >
              Chỉnh sửa nội dung chính sách
            </Typography>
            <Box>
              <IconButton
                color="warning"
                onClick={() => {
                  handleEditHome();
                  setIsEditTerms(false);
                }}
              >
                <SaveAsIcon />
              </IconButton>
              <IconButton color="warning" onClick={() => setIsEditTerms(false)}>
                <HighlightOffIcon />
              </IconButton>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              pb: 5,
              flexDirection: 'column',
              [theme.breakpoints.down('sm')]: {},
              width: '100%',
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <JoditEditor
              value={data}
              config={editorConfigJodit}
              onChange={value => setData(value)}
            />
          </Box>
        </DialogContent>
      </MuiDialog>
    );
  };

  useEffect(() => {}, [data]);
  return (
    <>
      <Helmet>
        <title>Chính sách</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            position: 'relative',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            width: '90%',
            height: '100%',
            p: 2,
            my: 1,
            borderRadius: 1,
            boxShadow:
              '0px 0px 20px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
          }}
        >
          <Box
            dangerouslySetInnerHTML={createMarkup(data)}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
              height: '100%',
              pb: 2,
              [theme.breakpoints.down('sm')]: {},
            }}
          ></Box>
          <IconButton
            sx={{
              display: userInfo?.role === 'ADMIN' ? '' : 'none',
              position: 'absolute',
              top: 1,
              right: 1,
            }}
            onClick={() => {
              if (isEditTerms) {
                handleEditProduct();
                setIsEditTerms(false);
              } else {
                setIsEditTerms(true);
              }
            }}
          >
            {isEditTerms ? <SaveAsIcon /> : <CreateIcon />}
          </IconButton>
          {/* <Maps /> */}
        </Box>
      </Paper>
      {AddDetailDialog()}
    </>
  );
}
