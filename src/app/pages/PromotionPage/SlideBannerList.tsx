import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button } from '@mui/material';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';

import banner1 from 'assets/images/banner/banner_1.webp';
import banner2 from 'assets/images/banner/banner_2.webp';
import banner3 from 'assets/images/banner/banner_3.webp';
import banner4 from 'assets/images/banner/banner_4.webp';

const InitListBanners = [
  {
    background: banner1,
    href: 'banner-1',
    bannerId: 'SPIN_WHEEL',
  },
  {
    background: banner2,
    href: 'banner-2',
    bannerId: 'OTHER',
  },
  {
    background: banner3,
    href: 'banner-3',
    bannerId: 'OTHER',
  },
  {
    background: banner4,
    href: 'banner-4',
    bannerId: 'OTHER',
  },
];

interface Props {
  title?: string;
  buttonTitle?: string;
}

const SlideBannerList: React.FC<Props> = ({ title, buttonTitle }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  // TODO: get list banners from api
  const [banners, setBanners] = React.useState<any[]>(InitListBanners);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (gameId: string) => {
    if (gameId === 'SPIN_WHEEL') {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Not found',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        background: '',
      }}
    >
      <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
        }}
        spaceBetween={30}
        effect={'coverflow'}
        grabCursor={true}
        slidesPerView={1}
        autoplay={{ delay: random, disableOnInteraction: false }}
        freeMode={true}
        modules={[Navigation, Pagination, Autoplay]}
        navigation
        pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {banners.map(({ bannerId, background }, index: any) => (
          <Box
            key={index}
            sx={{
              width: 'auto',
              display: 'flex',
              '&:hover': {
                opacity: 0.5,
              },
            }}
          >
            <SwiperSlide
              key={index}
              onClick={() => {
                onClickType(bannerId);
              }}
              onMouseEnter={() => {}}
              onMouseLeave={() => {}}
              style={{
                width: '100%',
                display: 'flex',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: '600px',
                  background: `url("${background}")`,
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  // borderRadius: 3,
                  display: 'flex',
                  [theme.breakpoints.down('md')]: {
                    height: '260px',
                  },
                }}
              ></Box>
            </SwiperSlide>
          </Box>
        ))}
      </Swiper>
    </Box>
  );
};

export default SlideBannerList;
