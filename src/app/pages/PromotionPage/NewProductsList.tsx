import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import AddToCart from 'app/components/AppWrapper/AddToCart';

import new1 from 'assets/images/product/new/new_01.webp';
import new2 from 'assets/images/product/new/new_02.webp';
import new3 from 'assets/images/product/new/new_03.webp';
import new4 from 'assets/images/product/new/new_04.webp';

const InitListNewProduct = [
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: '',
    price: 299999,
    save: 259999,
  },
];

interface Props {
  title?: string;
  buttonTitle?: string;
}

const NewProductsList: React.FC<Props> = ({ title, buttonTitle }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api
  const [products, setProducts] = React.useState<any[]>(InitListNewProduct);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '95%',
        my: 3,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          mt: 1,
        }}
      >
        <Typography
          sx={{
            fontWeight: 'bold',
            textTransform: 'uppercase',
          }}
        >
          Sản phẩm mới
        </Typography>
        <Button>Xem tất cả</Button>
      </Box>
      <Swiper
        style={{
          width: containerWidth,
          zIndex: 0,
          borderRadius: 5,
          paddingBottom: 2,
          paddingTop: '5px',
        }}
        spaceBetween={30}
        effect={'coverflow'}
        grabCursor={true}
        slidesPerView={'auto'}
        autoplay={{ delay: 5000, disableOnInteraction: true }}
        freeMode={true}
        modules={[Navigation, Pagination, Autoplay]}
        navigation
        // pagination={{ clickable: true }}
        // scrollbar={{ draggable: true }}
      >
        {products.map(
          (
            {
              bestSaleId,
              background1,
              background2,
              title,
              content,
              price,
              save,
            },
            index: any,
          ) => (
            <SwiperSlide
              key={index}
              onClick={() => {}}
              onMouseEnter={() => {
                setBestIndex(index);
              }}
              onMouseLeave={() => {
                setBestIndex(-1);
              }}
              style={{
                width: '20%',
                height: '400px',
                minWidth: '200px',
                background: '',
                borderRadius: 5,
                display: 'flex',
                alignItems: 'start',
                justifyContent: 'end',
                // overflow: 'hidden',
                position: 'relative',
                boxShadow:
                  '0px 0px 2px 1px rgba(0,0,0,0.05),inset 0px 0px 20px 1px rgba(0,0,0,0.05)',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: '50%',
                  background: `url("${
                    bestIndex === index ? background1 : background2
                  }")`,
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  borderRadius: 3,
                  display: 'flex',
                  // transform: `${bestIndex === index ? 'scale(1.1)' : ''}`,
                  transition:
                    'opacity .2s ease-in-out,background .5s ease-in-out',
                  '&:hover': {},
                }}
              ></Box>
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'start',
                  justifyContent: 'end',
                  p: 3,
                  position: 'absolute',
                  // background: 'black'
                }}
              >
                <Typography
                  sx={{
                    color: '',
                    fontSize: '18px',
                    fontWeight: 'bold',
                  }}
                >
                  {title}
                </Typography>
                <Typography
                  sx={{
                    color: '',
                    // fontSize: '20px',
                  }}
                >
                  {content}
                </Typography>
                <Typography
                  sx={{
                    color: 'red',
                    // fontSize: '20px',
                  }}
                >
                  {formatCurrency(price)}
                </Typography>
                <Button
                  sx={{
                    mt: 2,
                    color: 'black',
                    bgcolor: 'gray',
                    borderRadius: 5,
                    boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                  }}
                >
                  Thêm vào
                  {/* <AddShoppingCartIcon /> */}
                </Button>
                {/* <AddToCart productId={}>
                </AddToCart> */}
              </Box>
              <Typography
                sx={{
                  color: 'white',
                  bgcolor: 'red',
                  borderRadius: '2px 0 0 2px',
                  mr: 'auto',
                  px: 1,
                  fontSize: '',
                  fontWeight: '',
                  position: 'absolute',
                  // translate: '10px 0px',
                }}
              >
                Save {formatCurrency(save)}
              </Typography>
            </SwiperSlide>
          ),
        )}
      </Swiper>
    </Box>
  );
};

export default NewProductsList;
