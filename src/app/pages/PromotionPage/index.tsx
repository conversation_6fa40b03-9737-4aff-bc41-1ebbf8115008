import Box from '@mui/material/Box';
import * as React from 'react';
import { Helmet } from 'react-helmet-async';
import { theme } from 'app/components/AppWrapper/theme';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { keyframes } from '@emotion/react';
import QueueIcon from '@mui/icons-material/Queue';
import Grid from '@mui/material/Unstable_Grid2';
import Button from '@mui/material/Button';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import ListItemIcon from '@mui/material/ListItemIcon';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useUtilsSlice } from 'app/slice/utils';
import { userInfoSelector } from 'app/slice/user/selectors';
import { cartInfoSelector } from 'app/slice/cart/selectors';
import { useCartSlice } from 'app/slice/cart';

import Collection from './Collection';
import Recently from 'app/components/Recently/Recently';

import banner1 from 'assets/images/promotion/Banner-reward.jpg';
import banner2 from 'assets/images/event/banner_event-1.webp';
import banner3 from 'assets/images/promotion/dienthoaisinhvien-tagline_PK.webp';
import banner4 from 'assets/images/promotion/donghosinhvien-tagline_PK.webp';
import productType1 from 'assets/images/product/type/product_type_1.webp';
import productType2 from 'assets/images/product/type/product_type_2.webp';
import productType3 from 'assets/images/product/type/product_type_3.jpeg';
import productType4 from 'assets/images/product/type/product_type_4.webp';

import new1 from 'assets/images/product/new/new_01.webp';
import new2 from 'assets/images/product/new/new_02.webp';
import new3 from 'assets/images/product/new/new_03.webp';
import new4 from 'assets/images/product/new/new_04.webp';

import Partners from '../HomePage/Partners';

const InitListProduct1 = [
  {
    background: new1,
    background2: new2,
    name: 'Dock Sạc Không Dây',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new3,
    background2: new4,
    name: 'Cáp Sạc đa năng',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new1,
    background2: new2,
    name: 'Dock Sạc Không Dây',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new3,
    background2: new4,
    name: 'Cáp Sạc đa năng',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new1,
    background2: new2,
    name: 'Dock Sạc Không Dây',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new3,
    background2: new4,
    name: 'Cáp Sạc đa năng',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new1,
    background2: new2,
    name: 'Dock Sạc Không Dây',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new3,
    background2: new4,
    name: 'Cáp Sạc đa năng',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new1,
    background2: new2,
    name: 'Dock Sạc Không Dây',
    priceOrigin: 299999,
    price: 259999,
  },
  {
    background: new3,
    background2: new4,
    name: 'Cáp Sạc đa năng',
    priceOrigin: 299999,
    price: 259999,
  },
];

const time1 = (milliseconds: number) => {
  const DD =
    Math.floor(milliseconds / (24 * 60 * 60 * 1000)) < 10
      ? `0${Math.floor(milliseconds / (24 * 60 * 60 * 1000))}`
      : String(Math.floor(milliseconds / (24 * 60 * 60 * 1000)));

  const HH =
    Math.floor(milliseconds / (60 * 60 * 1000)) < 10
      ? `0${Math.floor(
          (milliseconds % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000),
        )}`
      : String(
          Math.floor((milliseconds % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)),
        );

  const mm =
    Math.floor((milliseconds % (60 * 60 * 1000)) / (60 * 1000)) < 10
      ? `0${Math.floor((milliseconds % (60 * 60 * 1000)) / (60 * 1000))}`
      : String(Math.floor((milliseconds % (60 * 60 * 1000)) / (60 * 1000)));

  const ss =
    Math.floor((milliseconds % (60 * 1000)) / 1000) < 10
      ? `0${Math.floor((milliseconds % (60 * 1000)) / 1000)}`
      : String(Math.floor((milliseconds % (60 * 1000)) / 1000));

  return `${DD} ngày ${HH}:${mm}:${ss}`;
};

const event = {
  name: 'Siêu Khuyến mãi',
  banner:
    'https://homicen-dev.s3.ap-southeast-1.amazonaws.com/phuonglinh/promotionpage/Banner-reward.jpg',
  bannerMobile:
    'https://homicen-dev.s3.ap-southeast-1.amazonaws.com/phuonglinh/promotionpage/Banner-reward.jpg',
  color: '#d40c0c',
  timeStart: '2024-03-19T06:46:34.370+00:00',
  timeEnd: '2024-04-31T06:46:34.370+00:00',
  action: [
    {
      name: 'khuyến mại 1',
      ref: '1',
    },
    {
      name: 'khuyến mại 2',
      ref: '2',
    },
    {
      name: 'khuyến mại 3',
      ref: '3',
    },
  ],
};

const translate = keyframes`
  0% {
    transform: translateX(100%)
  }
  100% {
    transform: translateX(-150%)
  }
`;

const handleScrollToBox = id => {
  const boxElement = document.getElementById(id);
  if (boxElement) {
    boxElement.scrollIntoView({ behavior: 'smooth' });
  }
};

export function PromotionPage() {
  const [carts, setCarts] = React.useState<any>([]);
  const [isEventAction, setIsEventAction] = React.useState<boolean>(false);
  const [isEventClose, setIsEventClose] = React.useState<boolean>(false);
  const [isUserAction, setIsUserAction] = React.useState<boolean>(false);
  const [editProductDialog, setEditProductDialog] =
    React.useState<boolean>(false);

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();
  const userInfo = useSelector(userInfoSelector);

  const { actions: cartActions } = useCartSlice();
  const cartInfo = useSelector(cartInfoSelector);
  const navigate = useNavigate();
  const [bannerReward, setBannerReward] = React.useState<any>(banner1);
  const [bannerEvent, setBannerEvent] = React.useState<any>(banner2);
  const [products, setProducts] = React.useState<any[]>(InitListProduct1);
  const [typeIndex, setTypeIndex] = React.useState<any>();
  const [type1, setType1] = React.useState<any>({
    background: productType1,
    title: 'Gia Dụng',
  });
  const [type2, setType2] = React.useState<any>({
    background: productType2,
    title: 'Vật Dụng Cá Nhân',
  });
  const [type3, setType3] = React.useState<any>({
    background: productType3,
    title: 'Phụ Kiện Di Động',
  });
  const [type4, setType4] = React.useState<any>({
    background: productType4,
    title: 'Phụ Kiện Để Bàn',
  });
  const [timingStart, setTimingStart] = React.useState<string>('');
  const [timingEnd, setTimingEnd] = React.useState<string>('');
  const [time, setTime] = React.useState<string>(
    '2024-03-23T06:46:34.370+00:00',
  );
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const checkEventAction = time => {
    if (new Date().getTime() - new Date(time).getTime() >= 0) {
      !isEventAction && setIsEventAction(true);
    } else {
      setIsEventAction(false);
    }
  };

  const countDownStart = (time: any) => {
    setInterval(() => {
      setTimingStart(time1(new Date(time).getTime() - new Date().getTime()));
    }, 1000);
    return time1(new Date(time).getTime() - new Date().getTime());
  };
  const countDownEnd = (time: any) => {
    setInterval(() => {
      setTimingEnd(time1(new Date(time).getTime() - new Date().getTime()));
    }, 1000);
    return time1(new Date(time).getTime() - new Date().getTime());
  };

  const handleCartAdd = (id: any, quantity: number) => {
    const indexExist = carts.findIndex(item => item?._id === id);
    const newCarts = carts.map(item => {
      return { id: item._id, quantity: item.quantity };
    });
    if (indexExist >= 0) {
      newCarts[indexExist] = {
        ...newCarts[indexExist],
        quantity: carts[indexExist].quantity + quantity,
      };
    } else {
      newCarts.push({ id, quantity });
    }
    dispatch(
      cartActions.upsertCart({ products: newCarts, token: userInfo?.token }),
    );
  };

  React.useEffect(() => {
    checkEventAction(event.timeStart);
    !isEventAction && countDownStart(event.timeStart);
    isEventAction && countDownEnd(event.timeEnd);
  }, [isEventAction]);

  React.useEffect(() => {
    if (new Date().getTime() > new Date(event.timeEnd).getTime())
      setIsEventClose(true);
  }, [isEventAction]);

  return (
    <>
      <Helmet>
        <title>Khuyến mãi</title>
        <meta
          name="description"
          content="Nhà phân phối phụ kiện hàng đầu Việt Nam - Phuơng Linh"
        />
      </Helmet>
      <Paper
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          height: 'fit-content',
          overflowX: 'hidden',
          overflowY: 'auto',
          border: 'none',
          boxShadow: 'none',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: 'auto',
            // mt: 2,
            // borderRadius: 1,
            // background: `url("${event.banner}")`,
            backgroundPosition: 'center',
            backgroundSize: 'cover',
            [theme.breakpoints.down('md')]: {
              // height: '300px',
              // background: `url("${event.bannerMobile || event.banner}")`,
            },
          }}
        >
          <img
            src={event.banner}
            alt={event.name}
            width={'100%'}
            height={'100%'}
            loading="lazy"
          />
        </Box>
        <Box
          sx={{
            width: 'fit-content',
            minWidth: '250px',
            height: '100%',
            p: 2,
            display: isEventClose ? 'none' : 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 2,
            transform: 'translateY(-100%)',
            ml: 'auto',
            bgcolor: event?.color,
            borderTopLeftRadius: 5,
            [theme.breakpoints.down('md')]: {
              transform: 'none',
              width: '100%',
              borderRadius: 0,
            },
          }}
        >
          <Typography
            sx={{
              color: 'white',
              [theme.breakpoints.down('md')]: {
                fontSize: '12px',
              },
            }}
          >
            {isEventAction ? 'Kết thúc sau' : 'Bắt đầu sau'}
          </Typography>
          <Typography
            sx={{
              color: 'white',
              // fontSize: '25px',
              px: 2,
              borderRadius: 3,
              boxShadow:
                'inset 10px 10px 15px -3px rgba(0,0,0,0.1),inset -10px -10px 15px -3px rgba(0,0,0,0.1)',
              [theme.breakpoints.down('md')]: {
                fontSize: '12px',
              },
            }}
          >
            {isEventAction ? timingEnd : timingStart}
          </Typography>
          <Typography
            sx={{
              display: isEventAction ? '' : 'none',
              color: 'white',
              fontSize: '12px',
              fontStyle: 'italic',
              textDecoration: 'underline',
              cursor: 'pointer',
              [theme.breakpoints.down('md')]: {
                fontSize: '12px',
              },
            }}
          >
            Xem chi tiết
          </Typography>
        </Box>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            p: 2,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 5,
            mt: -2,
            // bgcolor: event?.color,
            [theme.breakpoints.down('md')]: {
              // justifyContent: 'start',
              // animation: `${translate} 20s linear infinite`,
              // transform: 'none',
              // width: '100%',
              // flexDirection: 'column',
              // m: 0,
              display: 'none',
            },
          }}
        >
          {event.action.map(({ name, ref }) => (
            <Box
              sx={{
                display: isEventClose ? 'none' : 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                cursor: 'pointer',
                py: 3,
                px: 5,
                gap: 3,
                borderRadius: 1,
                background: 'linear-gradient(to bottom, #c09a39, red)',
              }}
              onClick={() => handleScrollToBox(ref)}
            >
              <Typography
                sx={{
                  textAlign: 'center',
                  textWrap: 'nowrap',
                  color: 'white',
                }}
              >
                {name}
              </Typography>
            </Box>
          ))}
        </Box>
        <Box
          sx={{
            width: '90%',
            height: 'auto',
            my: 3,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            gap: 3,
          }}
        >
          <Box
            id="1"
            sx={{
              width: '100%',
            }}
          >
            <Collection
              list={InitListProduct1}
              banner={banner3}
              bgcolor="#56d7d3"
            />
          </Box>
          <Box
            id="2"
            sx={{
              width: '100%',
            }}
          >
            <Collection
              list={InitListProduct1}
              banner={banner4}
              bgcolor="#e5dafe"
            />
          </Box>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography></Typography>
          </Box>
        </Box>
        <Recently />
        <Partners />
      </Paper>
    </>
  );
}
