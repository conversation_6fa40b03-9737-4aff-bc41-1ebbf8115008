import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/swiper-bundle.css';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { Box, Button, Typography } from '@mui/material';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import { useUtilsSlice } from 'app/slice/utils';
import useWindowDimensions from 'app/hook/useWindowDimensions';
import { useNavigate } from 'react-router-dom';
import { theme } from 'app/components/AppWrapper/theme';
import AddToCart from 'app/components/AppWrapper/AddToCart';
import new1 from 'assets/images/product/new/new_01.webp';
import new2 from 'assets/images/product/new/new_02.webp';
import new3 from 'assets/images/product/new/new_03.webp';
import new4 from 'assets/images/product/new/new_04.webp';
import banner from 'assets/images/banner/banner_6.webp';

const InitListProduct = [
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new1,
    background2: new2,
    href: 'bestSale-1',
    title: 'Dock Sạc Không Dây',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
  {
    background1: new3,
    background2: new4,
    href: 'bestSale-1',
    title: 'Cáp Sạc đa năng',
    content: 'Sản phẩm tốt nhất',
    bestSaleId: 'SPIN_WHEEL',
    price: 299999,
    save: 259999,
  },
];

interface Props {
  title?: string;
  banner?: string;
  bgcolor?: string;
  list?: any;
}

const Collection: React.FC<Props> = ({ title, banner, bgcolor, list }) => {
  const navigate = useNavigate();
  const { width } = useWindowDimensions();
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = React.useState<number>(0);
  const [bestIndex, setBestIndex] = React.useState<number>();
  const [random, setRandom] = React.useState<number>(
    Math.random() * 1000 + 3000,
  );

  const formatCurrency = (number: number) => {
    return number?.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
    });
  };

  // TODO: get list banners from api

  const dispatch = useDispatch();
  const { actions: utilsAction } = useUtilsSlice();

  const onClickType = (product: string) => {
    if (product) {
      navigate(``);
    } else {
      dispatch(
        utilsAction.showSnackbar({
          message: 'Product not exist',
          variant: 'error',
        }),
      );
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, [width]);

  return (
    <Box
      sx={{
        width: '95%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        // position: 'relative',
        background: bgcolor,
        borderRadius: 1,
        overflow: 'hidden',
        [theme.breakpoints.down('md')]: {
          height: '800px',
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          [theme.breakpoints.down('md')]: {
            width: '100%',
          },
        }}
      >
        <img
          src={banner}
          alt={title}
          width={'100%'}
          height={'100%'}
          loading="lazy"
        />
      </Box>
      <Box
        sx={{
          width: '100%',
          height: '100%',
          p: '6px',
          // position: 'absolute',
          display: 'flex',
          // justifyContent: 'end',
          [theme.breakpoints.down('md')]: {
            flexDirection: 'column',
            justifyContent: 'space-between',
            gap: 3,
          },
        }}
      >
        <Box
          sx={{
            width: '100%',
            borderRadius: 1,
            background: '',
            [theme.breakpoints.down('md')]: {
              width: '100%',
            },
          }}
        >
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              gap: 4,
              justifyContent: 'center',
              alignItems: 'center',
              flexWrap: 'wrap',
              background: '',
              p: 2,
            }}
          >
            {list?.map(
              (
                {
                  _id,
                  id,
                  background,
                  name,
                  content,
                  priceOrigin,
                  price,
                  save,
                },
                index: any,
              ) => (
                <Box
                  key={index}
                  onClick={() => {
                    onClickType(id);
                  }}
                  onMouseEnter={() => {
                    setBestIndex(index);
                  }}
                  onMouseLeave={() => {
                    setBestIndex(-1);
                  }}
                  sx={{
                    width: '200px',
                    height: '300px',
                    minWidth: '200px',
                    background: '',
                    borderRadius: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'start',
                    bgcolor: 'white',
                    overflow: 'hidden',
                    position: 'relative',
                    transform: `${bestIndex === index ? 'scale(1.07)' : ''}`,
                    transition:
                      'opacity .2s ease-in-out,transform .5s ease-in-out',
                    boxShadow:
                      '0px 0px 2px 1px rgba(0,0,0,0.05), 0px 0px 20px 1px rgba(0,0,0,0.05)',
                  }}
                >
                  <Box
                    sx={{
                      width: '200px',
                      height: '200px',
                      background: `url("${background}")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      borderRadius: '',
                      display: 'flex',

                      '&:hover': {},
                    }}
                  ></Box>
                  <Box
                    sx={{
                      width: '100%',
                      height: 'auto',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'start',
                      justifyContent: 'end',
                      p: 3,
                      position: '',
                      background: '#000000c',
                    }}
                  >
                    <Typography
                      sx={{
                        color: '',
                        fontSize: '',
                        fontWeight: '',
                      }}
                    >
                      {name}
                    </Typography>
                    <Box
                      sx={{
                        width: '100%',
                        display: 'flex',
                        gap: 2,
                        alignItems: 'center',
                      }}
                    >
                      <Typography
                        sx={{
                          color: 'red',
                          fontSize: '',
                          fontWeight: '',
                        }}
                      >
                        {formatCurrency(priceOrigin)}
                      </Typography>
                      <Typography
                        sx={{
                          color: '',
                          fontSize: '12px',
                          fontWeight: '',
                          textDecoration: 'line-through',
                        }}
                      >
                        {formatCurrency(price)}
                      </Typography>
                    </Box>
                    <AddToCart productId={_id} quantity={1}>
                      <Button
                        sx={{
                          width: '100%',
                          display: 'flex',
                          gap: 1,
                          mt: 2,
                          px: 2,
                          color: 'white',
                          bgcolor: '#c1bdbd',
                          borderRadius: 1,
                          textTransform: 'none',
                          fontSize: '10px',
                          // boxShadow: 'rgba(0, 0, 0, 0.35) 0px 5px 15px',
                          '&:hover': {
                            bgcolor: 'red',
                          },
                        }}
                      >
                        Thêm vào giỏ
                        <AddShoppingCartIcon />
                      </Button>
                    </AddToCart>
                  </Box>
                </Box>
              ),
            )}
          </Box>
          <Box
            sx={{
              width: 'fit-content',
              bgcolor: 'white',
              p: 2,
              mx: 'auto',
              borderRadius: 1,
              cursor: 'pointer',
            }}
          >
            Xem thêm
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Collection;
