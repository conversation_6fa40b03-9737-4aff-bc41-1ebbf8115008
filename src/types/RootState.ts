// [IMPORT NEW CONTAINERSTATE ABOVE] < Needed for generating containers seamlessly
import { CartState } from 'app/slice/cart/types';
import { UserState } from 'app/slice/user/types';
import { UtilsState } from 'app/slice/utils/types';

/* 
  Because the redux-injectors injects your reducers asynchronously somewhere in your code
  You have to declare them here manually
*/
export interface RootState {
  // [INSERT NEW REDUCER KEY ABOVE] < Needed for generating containers seamlessly
  user?: UserState;
  utils?: UtilsState;
  cart?: CartState;
}
