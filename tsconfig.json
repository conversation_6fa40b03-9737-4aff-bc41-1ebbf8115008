{"compilerOptions": {"noImplicitAny": false, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noFallthroughCasesInSwitch": true, "jsx": "react", "baseUrl": "./src"}, "include": ["src"], "ts-node": {"compilerOptions": {"esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "noEmit": true, "allowSyntheticDefaultImports": true}}}