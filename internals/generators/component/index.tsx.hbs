/**
*
* {{ properCase componentName }}
*
*/
{{#if wantMemo}}
import React, { memo } from 'react';
{{else}}
import * as React from 'react';
{{/if}}
{{#if wantStyledComponents}}
import styled from 'styled-components/macro';
{{/if}}
{{#if wantTranslations}}
import { useTranslation } from 'react-i18next';
import { messages } from './messages';
{{/if}}

interface Props {}

{{#if wantMemo}}
export const {{ properCase componentName }} = memo((props: Props) => {
{{else}}
export function {{ properCase componentName }}(props: Props) {
{{/if}}
  {{#if wantTranslations}}
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { t, i18n } = useTranslation();
  {{/if}}

  return (
  {{#if wantStyledComponents}}
  <Div>
  {{else}}
  <div>
  {{/if}}
    {{#if wantTranslations}}
    {t('')}
    {/*  {t(...messages.someThing())}  */}
    {{/if}}
  {{#if wantStyledComponents}}
  </Div>
  {{else}}
  </div>
  {{/if}}
  );

{{#if wantMemo}}
});
{{else}}
};
{{/if}}

{{#if wantStyledComponents}}
const Div = styled.div``;
{{/if}}
