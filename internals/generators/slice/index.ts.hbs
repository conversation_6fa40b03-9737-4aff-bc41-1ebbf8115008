import { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from 'utils/@reduxjs/toolkit';
{{#if wantSaga}}
import { useInjectReducer, useInjectSaga } from 'utils/redux-injectors';
import { {{ camelCase sliceName }}Saga } from './saga';
{{else}}
import { useInjectReducer } from 'utils/redux-injectors';
{{/if}}
import { {{ properCase sliceName }}State } from './types';

export const initialState: {{ properCase sliceName }}State = {};

const slice = createSlice({
  name: '{{ camelCase sliceName }}',
  initialState,
  reducers: {
    someAction(state, action: PayloadAction<any>) {},
  },
});

export const { actions: {{ camelCase sliceName }}Actions } = slice;

export const use{{ properCase sliceName }}Slice = () => {
  useInjectReducer({ key: slice.name, reducer: slice.reducer });
  {{#if wantSaga}}
  useInjectSaga({ key: slice.name, saga: {{ camelCase sliceName }}Saga });
  {{/if}}
  return { actions: slice.actions };
};

/**
 * Example Usage:
 *
 * export function MyComponentNeedingThisSlice() {
 *  const { actions } = use{{ properCase sliceName }}Slice();
 *
 *  const onButtonClick = (evt) => {
 *    dispatch(actions.someAction());
 *   };
 * }
 */