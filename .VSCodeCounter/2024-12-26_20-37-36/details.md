# Details

Date : 2024-12-26 20:37:36

Directory /Users/<USER>/dev/e-commerce/frontend/src

Total : 174 files, 35308 codes, 6079 comments, 1901 blanks, all 43288 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files

| filename                                                                                                                | language       |  code | comment | blank | total |
| :---------------------------------------------------------------------------------------------------------------------- | :------------- | ----: | ------: | ----: | ----: |
| [src/app/components/AppWrapper/AddToCart.tsx](/src/app/components/AppWrapper/AddToCart.tsx)                             | TypeScript JSX |   104 |       4 |     9 |   117 |
| [src/app/components/AppWrapper/BrandMenu.tsx](/src/app/components/AppWrapper/BrandMenu.tsx)                             | TypeScript JSX |   366 |     168 |    26 |   560 |
| [src/app/components/AppWrapper/index.tsx](/src/app/components/AppWrapper/index.tsx)                                     | TypeScript JSX | 1,090 |     184 |    69 | 1,343 |
| [src/app/components/AppWrapper/seoImage.tsx](/src/app/components/AppWrapper/seoImage.tsx)                               | TypeScript JSX |    18 |       0 |     4 |    22 |
| [src/app/components/AppWrapper/theme.tsx](/src/app/components/AppWrapper/theme.tsx)                                     | TypeScript JSX |    12 |       0 |     2 |    14 |
| [src/app/components/Dialog/AddProductDialog/index.tsx](/src/app/components/Dialog/AddProductDialog/index.tsx)           | TypeScript JSX |   811 |      68 |    32 |   911 |
| [src/app/components/Dialog/AuthDialog/SignIn.tsx](/src/app/components/Dialog/AuthDialog/SignIn.tsx)                     | TypeScript JSX |   750 |      12 |    49 |   811 |
| [src/app/components/Dialog/AuthDialog/SignUp.tsx](/src/app/components/Dialog/AuthDialog/SignUp.tsx)                     | TypeScript JSX |   501 |      12 |    40 |   553 |
| [src/app/components/Dialog/AuthDialog/index.tsx](/src/app/components/Dialog/AuthDialog/index.tsx)                       | TypeScript JSX |   171 |      10 |     8 |   189 |
| [src/app/components/Footer/index.tsx](/src/app/components/Footer/index.tsx)                                             | TypeScript JSX |   723 |     156 |    14 |   893 |
| [src/app/components/GooogleMaps/Maps.tsx](/src/app/components/GooogleMaps/Maps.tsx)                                     | TypeScript JSX |    56 |       2 |     9 |    67 |
| [src/app/components/InputFileUpload/index.tsx](/src/app/components/InputFileUpload/index.tsx)                           | TypeScript JSX |    93 |       2 |     8 |   103 |
| [src/app/components/LoadingIndicator/index.tsx](/src/app/components/LoadingIndicator/index.tsx)                         | TypeScript JSX |    56 |       0 |    10 |    66 |
| [src/app/components/MoreAction/MoreAction.tsx](/src/app/components/MoreAction/MoreAction.tsx)                           | TypeScript JSX |   141 |       0 |     5 |   146 |
| [src/app/components/MoreAction/ThreeAction.tsx](/src/app/components/MoreAction/ThreeAction.tsx)                         | TypeScript JSX |   117 |       1 |     5 |   123 |
| [src/app/components/Recently/Recently.tsx](/src/app/components/Recently/Recently.tsx)                                   | TypeScript JSX |   262 |      12 |    13 |   287 |
| [src/app/components/SEO/SEO.tsx](/src/app/components/SEO/SEO.tsx)                                                       | TypeScript JSX |     1 |      29 |     1 |    31 |
| [src/app/components/TopBar/Account/index.tsx](/src/app/components/TopBar/Account/index.tsx)                             | TypeScript JSX |   122 |       3 |     5 |   130 |
| [src/app/components/TopBar/OrderForm.tsx](/src/app/components/TopBar/OrderForm.tsx)                                     | TypeScript JSX |    80 |       2 |     6 |    88 |
| [src/app/components/TopBar/SearchMenu/index.tsx](/src/app/components/TopBar/SearchMenu/index.tsx)                       | TypeScript JSX |   188 |       7 |     5 |   200 |
| [src/app/components/TopBar/Top.css](/src/app/components/TopBar/Top.css)                                                 | CSS            |   251 |      11 |    19 |   281 |
| [src/app/components/TopBar/index.tsx](/src/app/components/TopBar/index.tsx)                                             | TypeScript JSX |   748 |     755 |    32 | 1,535 |
| [src/app/components/ZoomImg/ZoomImage.tsx](/src/app/components/ZoomImg/ZoomImage.tsx)                                   | TypeScript JSX |    90 |       3 |    13 |   106 |
| [src/app/constants/API.ts](/src/app/constants/API.ts)                                                                   | TypeScript     |     6 |       0 |     1 |     7 |
| [src/app/constants/app.ts](/src/app/constants/app.ts)                                                                   | TypeScript     |    10 |       0 |     2 |    12 |
| [src/app/hook/useWindowDimensions.tsx](/src/app/hook/useWindowDimensions.tsx)                                           | TypeScript JSX |    21 |       0 |     5 |    26 |
| [src/app/hook/useWindowScrollToTop.tsx](/src/app/hook/useWindowScrollToTop.tsx)                                         | TypeScript JSX |    16 |       0 |     4 |    20 |
| [src/app/index.tsx](/src/app/index.tsx)                                                                                 | TypeScript JSX |    74 |       8 |     8 |    90 |
| [src/app/pages/AboutPage/AboutUs.tsx](/src/app/pages/AboutPage/AboutUs.tsx)                                             | TypeScript JSX |   155 |       2 |    11 |   168 |
| [src/app/pages/AboutPage/BrandsList.tsx](/src/app/pages/AboutPage/BrandsList.tsx)                                       | TypeScript JSX |   189 |       4 |    13 |   206 |
| [src/app/pages/AboutPage/Collection.tsx](/src/app/pages/AboutPage/Collection.tsx)                                       | TypeScript JSX |   376 |      63 |    12 |   451 |
| [src/app/pages/AboutPage/Feedback.tsx](/src/app/pages/AboutPage/Feedback.tsx)                                           | TypeScript JSX |   424 |      10 |    18 |   452 |
| [src/app/pages/AboutPage/HotdealList.tsx](/src/app/pages/AboutPage/HotdealList.tsx)                                     | TypeScript JSX |   194 |      21 |    11 |   226 |
| [src/app/pages/AboutPage/Industry.tsx](/src/app/pages/AboutPage/Industry.tsx)                                           | TypeScript JSX |   208 |       3 |     6 |   217 |
| [src/app/pages/AboutPage/Loadable.tsx](/src/app/pages/AboutPage/Loadable.tsx)                                           | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/AboutPage/NewProductsList.tsx](/src/app/pages/AboutPage/NewProductsList.tsx)                             | TypeScript JSX |   229 |      98 |    14 |   341 |
| [src/app/pages/AboutPage/News.tsx](/src/app/pages/AboutPage/News.tsx)                                                   | TypeScript JSX |   361 |      15 |    19 |   395 |
| [src/app/pages/AboutPage/Partners.tsx](/src/app/pages/AboutPage/Partners.tsx)                                           | TypeScript JSX |   211 |      76 |    14 |   301 |
| [src/app/pages/AboutPage/Services.tsx](/src/app/pages/AboutPage/Services.tsx)                                           | TypeScript JSX |   275 |       6 |     9 |   290 |
| [src/app/pages/AboutPage/SlideBannerList.tsx](/src/app/pages/AboutPage/SlideBannerList.tsx)                             | TypeScript JSX |   170 |       9 |    11 |   190 |
| [src/app/pages/AboutPage/SlideLead.tsx](/src/app/pages/AboutPage/SlideLead.tsx)                                         | TypeScript JSX |   350 |      83 |    17 |   450 |
| [src/app/pages/AboutPage/SlideMiddleBanner.tsx](/src/app/pages/AboutPage/SlideMiddleBanner.tsx)                         | TypeScript JSX |   166 |      14 |    11 |   191 |
| [src/app/pages/AboutPage/SlideProducts.tsx](/src/app/pages/AboutPage/SlideProducts.tsx)                                 | TypeScript JSX |   285 |      65 |     7 |   357 |
| [src/app/pages/AboutPage/WhyUs.tsx](/src/app/pages/AboutPage/WhyUs.tsx)                                                 | TypeScript JSX |   242 |       5 |     9 |   256 |
| [src/app/pages/AboutPage/index.tsx](/src/app/pages/AboutPage/index.tsx)                                                 | TypeScript JSX |   492 |      20 |    36 |   548 |
| [src/app/pages/AdminPage/Loadable.tsx](/src/app/pages/AdminPage/Loadable.tsx)                                           | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/AdminPage/index.tsx](/src/app/pages/AdminPage/index.tsx)                                                 | TypeScript JSX | 2,163 |     599 |    85 | 2,847 |
| [src/app/pages/ContactPage/Loadable.tsx](/src/app/pages/ContactPage/Loadable.tsx)                                       | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/ContactPage/about.ts](/src/app/pages/ContactPage/about.ts)                                               | TypeScript     |     6 |       0 |     6 |    12 |
| [src/app/pages/ContactPage/index.tsx](/src/app/pages/ContactPage/index.tsx)                                             | TypeScript JSX |   310 |      50 |    12 |   372 |
| [src/app/pages/ContactPage/styles.css](/src/app/pages/ContactPage/styles.css)                                           | CSS            |    45 |       1 |    10 |    56 |
| [src/app/pages/ContactPage/toolbar.ts](/src/app/pages/ContactPage/toolbar.ts)                                           | TypeScript     |     1 |     139 |     2 |   142 |
| [src/app/pages/DetailPage/Loadable.tsx](/src/app/pages/DetailPage/Loadable.tsx)                                         | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/DetailPage/NewsLabel.tsx](/src/app/pages/DetailPage/NewsLabel.tsx)                                       | TypeScript JSX |    85 |       4 |    12 |   101 |
| [src/app/pages/DetailPage/SlideProductsList.tsx](/src/app/pages/DetailPage/SlideProductsList.tsx)                       | TypeScript JSX |   266 |      73 |    13 |   352 |
| [src/app/pages/DetailPage/detail.ts](/src/app/pages/DetailPage/detail.ts)                                               | TypeScript     |     7 |       0 |     5 |    12 |
| [src/app/pages/DetailPage/index.tsx](/src/app/pages/DetailPage/index.tsx)                                               | TypeScript JSX | 1,726 |     272 |    38 | 2,036 |
| [src/app/pages/DetailPage/review.tsx](/src/app/pages/DetailPage/review.tsx)                                             | TypeScript JSX |   100 |       0 |     5 |   105 |
| [src/app/pages/FilePage/CardFolder.tsx](/src/app/pages/FilePage/CardFolder.tsx)                                         | TypeScript JSX |   131 |       1 |     8 |   140 |
| [src/app/pages/FilePage/CardImage.tsx](/src/app/pages/FilePage/CardImage.tsx)                                           | TypeScript JSX |   172 |       1 |     9 |   182 |
| [src/app/pages/FilePage/CardPdf.tsx](/src/app/pages/FilePage/CardPdf.tsx)                                               | TypeScript JSX |   197 |       0 |    11 |   208 |
| [src/app/pages/FilePage/FileUpload.tsx](/src/app/pages/FilePage/FileUpload.tsx)                                         | TypeScript JSX |    86 |       4 |    10 |   100 |
| [src/app/pages/FilePage/Loadable.tsx](/src/app/pages/FilePage/Loadable.tsx)                                             | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/FilePage/index.tsx](/src/app/pages/FilePage/index.tsx)                                                   | TypeScript JSX |   488 |      18 |    27 |   533 |
| [src/app/pages/HomePage/AboutUs.tsx](/src/app/pages/HomePage/AboutUs.tsx)                                               | TypeScript JSX |   299 |       4 |    12 |   315 |
| [src/app/pages/HomePage/BrandsList.tsx](/src/app/pages/HomePage/BrandsList.tsx)                                         | TypeScript JSX |   233 |      14 |    12 |   259 |
| [src/app/pages/HomePage/Collection.tsx](/src/app/pages/HomePage/Collection.tsx)                                         | TypeScript JSX |   376 |      63 |    12 |   451 |
| [src/app/pages/HomePage/Feedback.tsx](/src/app/pages/HomePage/Feedback.tsx)                                             | TypeScript JSX |   421 |      41 |    18 |   480 |
| [src/app/pages/HomePage/HotdealList.tsx](/src/app/pages/HomePage/HotdealList.tsx)                                       | TypeScript JSX |   194 |      21 |    11 |   226 |
| [src/app/pages/HomePage/Industry.tsx](/src/app/pages/HomePage/Industry.tsx)                                             | TypeScript JSX |   379 |       3 |     7 |   389 |
| [src/app/pages/HomePage/Loadable.tsx](/src/app/pages/HomePage/Loadable.tsx)                                             | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/HomePage/NewProductsList.tsx](/src/app/pages/HomePage/NewProductsList.tsx)                               | TypeScript JSX |   232 |      98 |    14 |   344 |
| [src/app/pages/HomePage/News.tsx](/src/app/pages/HomePage/News.tsx)                                                     | TypeScript JSX |   361 |      15 |    19 |   395 |
| [src/app/pages/HomePage/Partners.tsx](/src/app/pages/HomePage/Partners.tsx)                                             | TypeScript JSX |   260 |      83 |    17 |   360 |
| [src/app/pages/HomePage/Services.tsx](/src/app/pages/HomePage/Services.tsx)                                             | TypeScript JSX |   959 |      29 |    10 |   998 |
| [src/app/pages/HomePage/SlideBannerList.tsx](/src/app/pages/HomePage/SlideBannerList.tsx)                               | TypeScript JSX |   170 |       9 |    11 |   190 |
| [src/app/pages/HomePage/SlideLead.tsx](/src/app/pages/HomePage/SlideLead.tsx)                                           | TypeScript JSX |   350 |      83 |    17 |   450 |
| [src/app/pages/HomePage/SlideMiddleBanner.tsx](/src/app/pages/HomePage/SlideMiddleBanner.tsx)                           | TypeScript JSX |   166 |      14 |    11 |   191 |
| [src/app/pages/HomePage/SlideProducts.tsx](/src/app/pages/HomePage/SlideProducts.tsx)                                   | TypeScript JSX |   553 |     119 |     8 |   680 |
| [src/app/pages/HomePage/WhyUs.tsx](/src/app/pages/HomePage/WhyUs.tsx)                                                   | TypeScript JSX |   309 |       6 |    16 |   331 |
| [src/app/pages/HomePage/index.tsx](/src/app/pages/HomePage/index.tsx)                                                   | TypeScript JSX |   426 |      56 |    40 |   522 |
| [src/app/pages/ListProduct/Loadable.tsx](/src/app/pages/ListProduct/Loadable.tsx)                                       | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/ListProduct/Menu/detail.ts](/src/app/pages/ListProduct/Menu/detail.ts)                                   | TypeScript     |     7 |       0 |     5 |    12 |
| [src/app/pages/ListProduct/Menu/filter.tsx](/src/app/pages/ListProduct/Menu/filter.tsx)                                 | TypeScript JSX |    93 |       0 |     5 |    98 |
| [src/app/pages/ListProduct/News.tsx](/src/app/pages/ListProduct/News.tsx)                                               | TypeScript JSX |   186 |     191 |    18 |   395 |
| [src/app/pages/ListProduct/index.tsx](/src/app/pages/ListProduct/index.tsx)                                             | TypeScript JSX | 1,181 |      66 |    31 | 1,278 |
| [src/app/pages/NewsInfoPage/Loadable.tsx](/src/app/pages/NewsInfoPage/Loadable.tsx)                                     | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/NewsInfoPage/detail.ts](/src/app/pages/NewsInfoPage/detail.ts)                                           | TypeScript     |     7 |       0 |     5 |    12 |
| [src/app/pages/NewsInfoPage/index.tsx](/src/app/pages/NewsInfoPage/index.tsx)                                           | TypeScript JSX |   686 |      23 |    34 |   743 |
| [src/app/pages/NewsPage/Loadable.tsx](/src/app/pages/NewsPage/Loadable.tsx)                                             | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/NewsPage/detail.ts](/src/app/pages/NewsPage/detail.ts)                                                   | TypeScript     |     7 |       0 |     5 |    12 |
| [src/app/pages/NewsPage/index.tsx](/src/app/pages/NewsPage/index.tsx)                                                   | TypeScript JSX | 2,313 |      86 |    38 | 2,437 |
| [src/app/pages/NotFoundPage/Loadable.tsx](/src/app/pages/NotFoundPage/Loadable.tsx)                                     | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/NotFoundPage/P.ts](/src/app/pages/NotFoundPage/P.ts)                                                     | TypeScript     |     7 |       0 |     2 |     9 |
| [src/app/pages/NotFoundPage/index.tsx](/src/app/pages/NotFoundPage/index.tsx)                                           | TypeScript JSX |    42 |       0 |     4 |    46 |
| [src/app/pages/OrderPage/Loadable.tsx](/src/app/pages/OrderPage/Loadable.tsx)                                           | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/OrderPage/index.tsx](/src/app/pages/OrderPage/index.tsx)                                                 | TypeScript JSX |   664 |     406 |    28 | 1,098 |
| [src/app/pages/OrderPage/review.tsx](/src/app/pages/OrderPage/review.tsx)                                               | TypeScript JSX |    99 |       0 |     5 |   104 |
| [src/app/pages/ProductPage/BrandsList.tsx](/src/app/pages/ProductPage/BrandsList.tsx)                                   | TypeScript JSX |   163 |      36 |    13 |   212 |
| [src/app/pages/ProductPage/BrandsList2.tsx](/src/app/pages/ProductPage/BrandsList2.tsx)                                 | TypeScript JSX |   160 |       5 |    13 |   178 |
| [src/app/pages/ProductPage/Category.tsx](/src/app/pages/ProductPage/Category.tsx)                                       | TypeScript JSX |   169 |       4 |    10 |   183 |
| [src/app/pages/ProductPage/Collection.tsx](/src/app/pages/ProductPage/Collection.tsx)                                   | TypeScript JSX |   376 |      63 |    12 |   451 |
| [src/app/pages/ProductPage/Feedback.tsx](/src/app/pages/ProductPage/Feedback.tsx)                                       | TypeScript JSX |   424 |      10 |    18 |   452 |
| [src/app/pages/ProductPage/HotDeal.tsx](/src/app/pages/ProductPage/HotDeal.tsx)                                         | TypeScript JSX |   136 |       2 |    13 |   151 |
| [src/app/pages/ProductPage/HotdealList.tsx](/src/app/pages/ProductPage/HotdealList.tsx)                                 | TypeScript JSX |   194 |      21 |    11 |   226 |
| [src/app/pages/ProductPage/Industry.tsx](/src/app/pages/ProductPage/Industry.tsx)                                       | TypeScript JSX |   208 |       3 |     6 |   217 |
| [src/app/pages/ProductPage/Loadable.tsx](/src/app/pages/ProductPage/Loadable.tsx)                                       | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/ProductPage/NewProductsList.tsx](/src/app/pages/ProductPage/NewProductsList.tsx)                         | TypeScript JSX |   229 |      98 |    14 |   341 |
| [src/app/pages/ProductPage/News.tsx](/src/app/pages/ProductPage/News.tsx)                                               | TypeScript JSX |   186 |     191 |    18 |   395 |
| [src/app/pages/ProductPage/Partners.tsx](/src/app/pages/ProductPage/Partners.tsx)                                       | TypeScript JSX |   190 |      76 |    12 |   278 |
| [src/app/pages/ProductPage/Services.tsx](/src/app/pages/ProductPage/Services.tsx)                                       | TypeScript JSX |   275 |       6 |     9 |   290 |
| [src/app/pages/ProductPage/SlideBannerList.tsx](/src/app/pages/ProductPage/SlideBannerList.tsx)                         | TypeScript JSX |   170 |       9 |    11 |   190 |
| [src/app/pages/ProductPage/SlideLead.tsx](/src/app/pages/ProductPage/SlideLead.tsx)                                     | TypeScript JSX |   238 |     131 |    17 |   386 |
| [src/app/pages/ProductPage/SlideMiddleBanner.tsx](/src/app/pages/ProductPage/SlideMiddleBanner.tsx)                     | TypeScript JSX |   166 |      14 |    11 |   191 |
| [src/app/pages/ProductPage/SlideProducts.tsx](/src/app/pages/ProductPage/SlideProducts.tsx)                             | TypeScript JSX |   518 |     104 |     7 |   629 |
| [src/app/pages/ProductPage/WhyUs.tsx](/src/app/pages/ProductPage/WhyUs.tsx)                                             | TypeScript JSX |   242 |       5 |     9 |   256 |
| [src/app/pages/ProductPage/index.tsx](/src/app/pages/ProductPage/index.tsx)                                             | TypeScript JSX |   280 |     191 |    25 |   496 |
| [src/app/pages/PromotionPage/Collection.tsx](/src/app/pages/PromotionPage/Collection.tsx)                               | TypeScript JSX |   343 |       5 |    12 |   360 |
| [src/app/pages/PromotionPage/Loadable.tsx](/src/app/pages/PromotionPage/Loadable.tsx)                                   | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/PromotionPage/NewProductsList.tsx](/src/app/pages/PromotionPage/NewProductsList.tsx)                     | TypeScript JSX |   270 |      12 |    11 |   293 |
| [src/app/pages/PromotionPage/SlideBannerList.tsx](/src/app/pages/PromotionPage/SlideBannerList.tsx)                     | TypeScript JSX |   140 |       3 |    11 |   154 |
| [src/app/pages/PromotionPage/index.tsx](/src/app/pages/PromotionPage/index.tsx)                                         | TypeScript JSX |   452 |      13 |    24 |   489 |
| [src/app/pages/TermsPage/Loadable.tsx](/src/app/pages/TermsPage/Loadable.tsx)                                           | TypeScript JSX |     5 |       3 |     3 |    11 |
| [src/app/pages/TermsPage/about.ts](/src/app/pages/TermsPage/about.ts)                                                   | TypeScript     |     6 |       0 |     6 |    12 |
| [src/app/pages/TermsPage/index.tsx](/src/app/pages/TermsPage/index.tsx)                                                 | TypeScript JSX |   276 |       8 |    15 |   299 |
| [src/app/pages/TermsPage/styles.css](/src/app/pages/TermsPage/styles.css)                                               | CSS            |    45 |       1 |    10 |    56 |
| [src/app/pages/TermsPage/toolbar.ts](/src/app/pages/TermsPage/toolbar.ts)                                               | TypeScript     |     1 |     139 |     2 |   142 |
| [src/app/slice/cart/index.ts](/src/app/slice/cart/index.ts)                                                             | TypeScript     |    53 |      10 |     5 |    68 |
| [src/app/slice/cart/saga.ts](/src/app/slice/cart/saga.ts)                                                               | TypeScript     |    39 |      27 |     5 |    71 |
| [src/app/slice/cart/selectors.ts](/src/app/slice/cart/selectors.ts)                                                     | TypeScript     |     5 |       1 |     4 |    10 |
| [src/app/slice/cart/types.ts](/src/app/slice/cart/types.ts)                                                             | TypeScript     |    11 |       1 |     3 |    15 |
| [src/app/slice/user/index.ts](/src/app/slice/user/index.ts)                                                             | TypeScript     |   118 |      36 |     6 |   160 |
| [src/app/slice/user/saga.ts](/src/app/slice/user/saga.ts)                                                               | TypeScript     |   132 |      25 |     8 |   165 |
| [src/app/slice/user/selectors.ts](/src/app/slice/user/selectors.ts)                                                     | TypeScript     |     5 |       1 |     4 |    10 |
| [src/app/slice/user/types.ts](/src/app/slice/user/types.ts)                                                             | TypeScript     |    22 |       2 |     4 |    28 |
| [src/app/slice/utils/index.ts](/src/app/slice/utils/index.ts)                                                           | TypeScript     |    31 |       6 |     5 |    42 |
| [src/app/slice/utils/localStorage.ts](/src/app/slice/utils/localStorage.ts)                                             | TypeScript     |    81 |       1 |    21 |   103 |
| [src/app/slice/utils/selectors.ts](/src/app/slice/utils/selectors.ts)                                                   | TypeScript     |    20 |       1 |     5 |    26 |
| [src/app/slice/utils/types.ts](/src/app/slice/utils/types.ts)                                                           | TypeScript     |    24 |       3 |     6 |    33 |
| [src/assets/images/button/Facebook_Messenger_logo_2020.svg](/src/assets/images/button/Facebook_Messenger_logo_2020.svg) | XML            |     2 |       0 |     1 |     3 |
| [src/assets/images/card-icon.svg](/src/assets/images/card-icon.svg)                                                     | XML            |    19 |       0 |     1 |    20 |
| [src/index.tsx](/src/index.tsx)                                                                                         | TypeScript JSX |    29 |      16 |    14 |    59 |
| [src/locales/**tests**/i18n.test.ts](/src/locales/__tests__/i18n.test.ts)                                               | TypeScript     |     7 |       0 |     2 |     9 |
| [src/locales/en/translation.json](/src/locales/en/translation.json)                                                     | JSON           |    15 |       0 |     1 |    16 |
| [src/locales/i18n.ts](/src/locales/i18n.ts)                                                                             | TypeScript     |    31 |       6 |     7 |    44 |
| [src/locales/translations.ts](/src/locales/translations.ts)                                                             | TypeScript     |    22 |      10 |     3 |    35 |
| [src/locales/types.ts](/src/locales/types.ts)                                                                           | TypeScript     |     4 |      12 |     3 |    19 |
| [src/locales/vi/translation.json](/src/locales/vi/translation.json)                                                     | JSON           |    15 |       0 |     1 |    16 |
| [src/react-app-env.d.ts](/src/react-app-env.d.ts)                                                                       | TypeScript     |     0 |       3 |     2 |     5 |
| [src/reportWebVitals.ts](/src/reportWebVitals.ts)                                                                       | TypeScript     |    13 |       0 |     3 |    16 |
| [src/setupTests.ts](/src/setupTests.ts)                                                                                 | TypeScript     |     4 |       4 |     3 |    11 |
| [src/store/**tests**/configureStore.test.ts](/src/store/__tests__/configureStore.test.ts)                               | TypeScript     |    17 |       0 |     3 |    20 |
| [src/store/**tests**/reducer.test.ts](/src/store/__tests__/reducer.test.ts)                                             | TypeScript     |    19 |       0 |     3 |    22 |
| [src/store/configureStore.ts](/src/store/configureStore.ts)                                                             | TypeScript     |    27 |       4 |     8 |    39 |
| [src/store/reducers.ts](/src/store/reducers.ts)                                                                         | TypeScript     |    11 |       7 |     4 |    22 |
| [src/styles/**tests**/media.test.ts](/src/styles/__tests__/media.test.ts)                                               | TypeScript     |    13 |       0 |     2 |    15 |
| [src/styles/global-styles.ts](/src/styles/global-styles.ts)                                                             | TypeScript     |    48 |      51 |    13 |   112 |
| [src/styles/media.ts](/src/styles/media.ts)                                                                             | TypeScript     |    13 |      17 |     5 |    35 |
| [src/types/RootState.ts](/src/types/RootState.ts)                                                                       | TypeScript     |     8 |       6 |     2 |    16 |
| [src/types/index.ts](/src/types/index.ts)                                                                               | TypeScript     |     5 |       0 |     3 |     8 |
| [src/utils/@reduxjs/toolkit.tsx](/src/utils/@reduxjs/toolkit.tsx)                                                       | TypeScript JSX |    15 |       2 |     3 |    20 |
| [src/utils/LocalStorage.ts](/src/utils/LocalStorage.ts)                                                                 | TypeScript     |    35 |       6 |     2 |    43 |
| [src/utils/app.ts](/src/utils/app.ts)                                                                                   | TypeScript     |    25 |       1 |     5 |    31 |
| [src/utils/convert.ts](/src/utils/convert.ts)                                                                           | TypeScript     |     8 |       0 |     3 |    11 |
| [src/utils/jodit.ts](/src/utils/jodit.ts)                                                                               | TypeScript     |   183 |      62 |    19 |   264 |
| [src/utils/loadable.tsx](/src/utils/loadable.tsx)                                                                       | TypeScript JSX |    25 |       0 |     6 |    31 |
| [src/utils/messages.ts](/src/utils/messages.ts)                                                                         | TypeScript     |     6 |       7 |     1 |    14 |
| [src/utils/moment.ts](/src/utils/moment.ts)                                                                             | TypeScript     |    67 |       0 |    11 |    78 |
| [src/utils/redux-injectors.ts](/src/utils/redux-injectors.ts)                                                           | TypeScript     |    17 |       1 |     4 |    22 |
| [src/utils/regexp.ts](/src/utils/regexp.ts)                                                                             | TypeScript     |     5 |       0 |     2 |     7 |
| [src/utils/request.ts](/src/utils/request.ts)                                                                           | TypeScript     |    42 |       7 |     6 |    55 |
| [src/utils/s3.ts](/src/utils/s3.ts)                                                                                     | TypeScript     |    85 |      14 |    18 |   117 |
| [src/utils/types/injector-typings.ts](/src/utils/types/injector-typings.ts)                                             | TypeScript     |    18 |       0 |     5 |    23 |
| [src/utils/uploadImg.ts](/src/utils/uploadImg.ts)                                                                       | TypeScript     |    19 |       2 |     4 |    25 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)
