# Summary

Date : 2024-12-26 20:37:36

Directory /Users/<USER>/dev/e-commerce/frontend/src

Total : 174 files, 35308 codes, 6079 comments, 1901 blanks, all 43288 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages

| language       | files |   code | comment | blank |  total |
| :------------- | ----: | -----: | ------: | ----: | -----: |
| TypeScript JSX |   116 | 33,558 |   5,454 | 1,595 | 40,607 |
| TypeScript     |    51 |  1,358 |     612 |   263 |  2,233 |
| CSS            |     3 |    341 |      13 |    39 |    393 |
| JSON           |     2 |     30 |       0 |     2 |     32 |
| XML            |     2 |     21 |       0 |     2 |     23 |

## Directories

| path                                   | files |   code | comment | blank |  total |
| :------------------------------------- | ----: | -----: | ------: | ----: | -----: |
| .                                      |   174 | 35,308 |   6,079 | 1,901 | 43,288 |
| . (Files)                              |     4 |     46 |      23 |    22 |     91 |
| app                                    |   139 | 34,436 |   5,841 | 1,728 | 42,005 |
| app (Files)                            |     1 |     74 |       8 |     8 |     90 |
| app/components                         |    23 |  6,751 |   1,441 |   384 |  8,576 |
| app/components/AppWrapper              |     5 |  1,590 |     356 |   110 |  2,056 |
| app/components/Dialog                  |     4 |  2,233 |     102 |   129 |  2,464 |
| app/components/Dialog/AddProductDialog |     1 |    811 |      68 |    32 |    911 |
| app/components/Dialog/AuthDialog       |     3 |  1,422 |      34 |    97 |  1,553 |
| app/components/Footer                  |     1 |    723 |     156 |    14 |    893 |
| app/components/GooogleMaps             |     1 |     56 |       2 |     9 |     67 |
| app/components/InputFileUpload         |     1 |     93 |       2 |     8 |    103 |
| app/components/LoadingIndicator        |     1 |     56 |       0 |    10 |     66 |
| app/components/MoreAction              |     2 |    258 |       1 |    10 |    269 |
| app/components/Recently                |     1 |    262 |      12 |    13 |    287 |
| app/components/SEO                     |     1 |      1 |      29 |     1 |     31 |
| app/components/TopBar                  |     5 |  1,389 |     778 |    67 |  2,234 |
| app/components/TopBar (Files)          |     3 |  1,079 |     768 |    57 |  1,904 |
| app/components/TopBar/Account          |     1 |    122 |       3 |     5 |    130 |
| app/components/TopBar/SearchMenu       |     1 |    188 |       7 |     5 |    200 |
| app/components/ZoomImg                 |     1 |     90 |       3 |    13 |    106 |
| app/constants                          |     2 |     16 |       0 |     3 |     19 |
| app/hook                               |     2 |     37 |       0 |     9 |     46 |
| app/pages                              |    99 | 27,017 |   4,278 | 1,248 | 32,543 |
| app/pages/AboutPage                    |    17 |  4,332 |     497 |   221 |  5,050 |
| app/pages/AdminPage                    |     2 |  2,168 |     602 |    88 |  2,858 |
| app/pages/ContactPage                  |     5 |    367 |     193 |    33 |    593 |
| app/pages/DetailPage                   |     6 |  2,189 |     352 |    76 |  2,617 |
| app/pages/FilePage                     |     6 |  1,079 |      27 |    68 |  1,174 |
| app/pages/HomePage                     |    17 |  5,693 |     661 |   238 |  6,592 |
| app/pages/ListProduct                  |     5 |  1,472 |     260 |    62 |  1,794 |
| app/pages/ListProduct (Files)          |     3 |  1,372 |     260 |    52 |  1,684 |
| app/pages/ListProduct/Menu             |     2 |    100 |       0 |    10 |    110 |
| app/pages/NewsInfoPage                 |     3 |    698 |      26 |    42 |    766 |
| app/pages/NewsPage                     |     3 |  2,325 |      89 |    46 |  2,460 |
| app/pages/NotFoundPage                 |     3 |     54 |       3 |     9 |     66 |
| app/pages/OrderPage                    |     3 |    768 |     409 |    36 |  1,213 |
| app/pages/ProductPage                  |    19 |  4,329 |     972 |   232 |  5,533 |
| app/pages/PromotionPage                |     5 |  1,210 |      36 |    61 |  1,307 |
| app/pages/TermsPage                    |     5 |    333 |     151 |    36 |    520 |
| app/slice                              |    12 |    541 |     114 |    76 |    731 |
| app/slice/cart                         |     4 |    108 |      39 |    17 |    164 |
| app/slice/user                         |     4 |    277 |      64 |    22 |    363 |
| app/slice/utils                        |     4 |    156 |      11 |    37 |    204 |
| assets                                 |     2 |     21 |       0 |     2 |     23 |
| assets/images                          |     2 |     21 |       0 |     2 |     23 |
| assets/images (Files)                  |     1 |     19 |       0 |     1 |     20 |
| assets/images/button                   |     1 |      2 |       0 |     1 |      3 |
| locales                                |     6 |     94 |      28 |    17 |    139 |
| locales (Files)                        |     3 |     57 |      28 |    13 |     98 |
| locales/**tests**                      |     1 |      7 |       0 |     2 |      9 |
| locales/en                             |     1 |     15 |       0 |     1 |     16 |
| locales/vi                             |     1 |     15 |       0 |     1 |     16 |
| store                                  |     4 |     74 |      11 |    18 |    103 |
| store (Files)                          |     2 |     38 |      11 |    12 |     61 |
| store/**tests**                        |     2 |     36 |       0 |     6 |     42 |
| styles                                 |     3 |     74 |      68 |    20 |    162 |
| styles (Files)                         |     2 |     61 |      68 |    18 |    147 |
| styles/**tests**                       |     1 |     13 |       0 |     2 |     15 |
| types                                  |     2 |     13 |       6 |     5 |     24 |
| utils                                  |    14 |    550 |     102 |    89 |    741 |
| utils (Files)                          |    12 |    517 |     100 |    81 |    698 |
| utils/@reduxjs                         |     1 |     15 |       2 |     3 |     20 |
| utils/types                            |     1 |     18 |       0 |     5 |     23 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)
