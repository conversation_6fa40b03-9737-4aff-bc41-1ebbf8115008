{"file:///Users/<USER>/dev/e-commerce/frontend/src/reportWebVitals.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/index.tsx": {"language": "TypeScript JSX", "code": 29, "comment": 16, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/react-app-env.d.ts": {"language": "TypeScript", "code": 0, "comment": 3, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/store/__tests__/configureStore.test.ts": {"language": "TypeScript", "code": 17, "comment": 0, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/setupTests.ts": {"language": "TypeScript", "code": 4, "comment": 4, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/store/reducers.ts": {"language": "TypeScript", "code": 11, "comment": 7, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/store/__tests__/reducer.test.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/store/configureStore.ts": {"language": "TypeScript", "code": 27, "comment": 4, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/styles/__tests__/media.test.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/styles/global-styles.ts": {"language": "TypeScript", "code": 48, "comment": 51, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/%40reduxjs/toolkit.tsx": {"language": "TypeScript JSX", "code": 15, "comment": 2, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/types/index.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/moment.ts": {"language": "TypeScript", "code": 67, "comment": 0, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/styles/media.ts": {"language": "TypeScript", "code": 13, "comment": 17, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/types/RootState.ts": {"language": "TypeScript", "code": 8, "comment": 6, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/loadable.tsx": {"language": "TypeScript JSX", "code": 25, "comment": 0, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/convert.ts": {"language": "TypeScript", "code": 8, "comment": 0, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/app.ts": {"language": "TypeScript", "code": 25, "comment": 1, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/messages.ts": {"language": "TypeScript", "code": 6, "comment": 7, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/redux-injectors.ts": {"language": "TypeScript", "code": 17, "comment": 1, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/uploadImg.ts": {"language": "TypeScript", "code": 19, "comment": 2, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/regexp.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/request.ts": {"language": "TypeScript", "code": 42, "comment": 7, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/LocalStorage.ts": {"language": "TypeScript", "code": 35, "comment": 6, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/types/injector-typings.ts": {"language": "TypeScript", "code": 18, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/translations.ts": {"language": "TypeScript", "code": 22, "comment": 10, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/en/translation.json": {"language": "JSON", "code": 15, "comment": 0, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/s3.ts": {"language": "TypeScript", "code": 85, "comment": 14, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/utils/jodit.ts": {"language": "TypeScript", "code": 183, "comment": 62, "blank": 19}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/saga.ts": {"language": "TypeScript", "code": 132, "comment": 25, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/hook/useWindowDimensions.tsx": {"language": "TypeScript JSX", "code": 21, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/assets/images/card-icon.svg": {"language": "XML", "code": 19, "comment": 0, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/hook/useWindowScrollToTop.tsx": {"language": "TypeScript JSX", "code": 16, "comment": 0, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/index.ts": {"language": "TypeScript", "code": 118, "comment": 36, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/selectors.ts": {"language": "TypeScript", "code": 5, "comment": 1, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/types.ts": {"language": "TypeScript", "code": 22, "comment": 2, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/index.ts": {"language": "TypeScript", "code": 53, "comment": 10, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/selectors.ts": {"language": "TypeScript", "code": 5, "comment": 1, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/types.ts": {"language": "TypeScript", "code": 11, "comment": 1, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/constants/app.ts": {"language": "TypeScript", "code": 10, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/constants/API.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/SEO/SEO.tsx": {"language": "TypeScript JSX", "code": 1, "comment": 29, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/saga.ts": {"language": "TypeScript", "code": 39, "comment": 27, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/InputFileUpload/index.tsx": {"language": "TypeScript JSX", "code": 93, "comment": 2, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/GooogleMaps/Maps.tsx": {"language": "TypeScript JSX", "code": 56, "comment": 2, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/ZoomImg/ZoomImage.tsx": {"language": "TypeScript JSX", "code": 90, "comment": 3, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Recently/Recently.tsx": {"language": "TypeScript JSX", "code": 262, "comment": 12, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/about.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/toolbar.ts": {"language": "TypeScript", "code": 1, "comment": 139, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/styles.css": {"language": "CSS", "code": 45, "comment": 1, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/index.tsx": {"language": "TypeScript JSX", "code": 686, "comment": 23, "blank": 34}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/index.tsx": {"language": "TypeScript JSX", "code": 2313, "comment": 86, "blank": 38}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/theme.tsx": {"language": "TypeScript JSX", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/index.tsx": {"language": "TypeScript JSX", "code": 1090, "comment": 184, "blank": 69}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/seoImage.tsx": {"language": "TypeScript JSX", "code": 18, "comment": 0, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/P.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AdminPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/AddToCart.tsx": {"language": "TypeScript JSX", "code": 104, "comment": 4, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/BrandMenu.tsx": {"language": "TypeScript JSX", "code": 366, "comment": 168, "blank": 26}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/index.tsx": {"language": "TypeScript JSX", "code": 42, "comment": 0, "blank": 4}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/index.tsx": {"language": "TypeScript JSX", "code": 310, "comment": 50, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/index.tsx": {"language": "TypeScript JSX", "code": 664, "comment": 406, "blank": 28}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AdminPage/index.tsx": {"language": "TypeScript JSX", "code": 2163, "comment": 599, "blank": 85}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/OrderForm.tsx": {"language": "TypeScript JSX", "code": 80, "comment": 2, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/review.tsx": {"language": "TypeScript JSX", "code": 100, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/index.tsx": {"language": "TypeScript JSX", "code": 280, "comment": 191, "blank": 25}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/BrandsList.tsx": {"language": "TypeScript JSX", "code": 189, "comment": 4, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/index.tsx": {"language": "TypeScript JSX", "code": 492, "comment": 20, "blank": 36}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/NewsLabel.tsx": {"language": "TypeScript JSX", "code": 85, "comment": 4, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/Top.css": {"language": "CSS", "code": 251, "comment": 11, "blank": 19}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/review.tsx": {"language": "TypeScript JSX", "code": 99, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/SlideProductsList.tsx": {"language": "TypeScript JSX", "code": 266, "comment": 73, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/index.tsx": {"language": "TypeScript JSX", "code": 1181, "comment": 66, "blank": 31}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/SearchMenu/index.tsx": {"language": "TypeScript JSX", "code": 188, "comment": 7, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideMiddleBanner.tsx": {"language": "TypeScript JSX", "code": 166, "comment": 14, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Services.tsx": {"language": "TypeScript JSX", "code": 275, "comment": 6, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/Account/index.tsx": {"language": "TypeScript JSX", "code": 122, "comment": 3, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Menu/filter.tsx": {"language": "TypeScript JSX", "code": 93, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideMiddleBanner.tsx": {"language": "TypeScript JSX", "code": 166, "comment": 14, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/AboutUs.tsx": {"language": "TypeScript JSX", "code": 155, "comment": 2, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideBannerList.tsx": {"language": "TypeScript JSX", "code": 170, "comment": 9, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideBannerList.tsx": {"language": "TypeScript JSX", "code": 170, "comment": 9, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/News.tsx": {"language": "TypeScript JSX", "code": 186, "comment": 191, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/News.tsx": {"language": "TypeScript JSX", "code": 361, "comment": 15, "blank": 19}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideLead.tsx": {"language": "TypeScript JSX", "code": 350, "comment": 83, "blank": 17}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideLead.tsx": {"language": "TypeScript JSX", "code": 238, "comment": 131, "blank": 17}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/BrandsList.tsx": {"language": "TypeScript JSX", "code": 163, "comment": 36, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/News.tsx": {"language": "TypeScript JSX", "code": 186, "comment": 191, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Category.tsx": {"language": "TypeScript JSX", "code": 169, "comment": 4, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/index.tsx": {"language": "TypeScript JSX", "code": 748, "comment": 755, "blank": 32}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Partners.tsx": {"language": "TypeScript JSX", "code": 211, "comment": 76, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/index.tsx": {"language": "TypeScript JSX", "code": 1726, "comment": 272, "blank": 38}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Partners.tsx": {"language": "TypeScript JSX", "code": 190, "comment": 76, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/WhyUs.tsx": {"language": "TypeScript JSX", "code": 242, "comment": 5, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Services.tsx": {"language": "TypeScript JSX", "code": 275, "comment": 6, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/toolbar.ts": {"language": "TypeScript", "code": 1, "comment": 139, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/styles.css": {"language": "CSS", "code": 45, "comment": 1, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideProducts.tsx": {"language": "TypeScript JSX", "code": 285, "comment": 65, "blank": 7}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/about.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/HotDeal.tsx": {"language": "TypeScript JSX", "code": 136, "comment": 2, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/HotdealList.tsx": {"language": "TypeScript JSX", "code": 194, "comment": 21, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideProducts.tsx": {"language": "TypeScript JSX", "code": 518, "comment": 104, "blank": 7}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/index.tsx": {"language": "TypeScript JSX", "code": 276, "comment": 8, "blank": 15}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/LoadingIndicator/index.tsx": {"language": "TypeScript JSX", "code": 56, "comment": 0, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/HotdealList.tsx": {"language": "TypeScript JSX", "code": 194, "comment": 21, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/WhyUs.tsx": {"language": "TypeScript JSX", "code": 242, "comment": 5, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/BrandsList2.tsx": {"language": "TypeScript JSX", "code": 160, "comment": 5, "blank": 13}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Footer/index.tsx": {"language": "TypeScript JSX", "code": 723, "comment": 156, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Collection.tsx": {"language": "TypeScript JSX", "code": 376, "comment": 63, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/NewProductsList.tsx": {"language": "TypeScript JSX", "code": 229, "comment": 98, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/NewProductsList.tsx": {"language": "TypeScript JSX", "code": 229, "comment": 98, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Industry.tsx": {"language": "TypeScript JSX", "code": 208, "comment": 3, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Feedback.tsx": {"language": "TypeScript JSX", "code": 424, "comment": 10, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Feedback.tsx": {"language": "TypeScript JSX", "code": 424, "comment": 10, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/selectors.ts": {"language": "TypeScript", "code": 20, "comment": 1, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/index.ts": {"language": "TypeScript", "code": 31, "comment": 6, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/localStorage.ts": {"language": "TypeScript", "code": 81, "comment": 1, "blank": 21}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/NewProductsList.tsx": {"language": "TypeScript JSX", "code": 270, "comment": 12, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/types.ts": {"language": "TypeScript", "code": 24, "comment": 3, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/SlideBannerList.tsx": {"language": "TypeScript JSX", "code": 140, "comment": 3, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/AboutUs.tsx": {"language": "TypeScript JSX", "code": 299, "comment": 4, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/Collection.tsx": {"language": "TypeScript JSX", "code": 343, "comment": 5, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideBannerList.tsx": {"language": "TypeScript JSX", "code": 170, "comment": 9, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/index.tsx": {"language": "TypeScript JSX", "code": 452, "comment": 13, "blank": 24}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideMiddleBanner.tsx": {"language": "TypeScript JSX", "code": 166, "comment": 14, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideLead.tsx": {"language": "TypeScript JSX", "code": 350, "comment": 83, "blank": 17}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/BrandsList.tsx": {"language": "TypeScript JSX", "code": 233, "comment": 14, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/News.tsx": {"language": "TypeScript JSX", "code": 361, "comment": 15, "blank": 19}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Industry.tsx": {"language": "TypeScript JSX", "code": 208, "comment": 3, "blank": 6}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/index.tsx": {"language": "TypeScript JSX", "code": 426, "comment": 56, "blank": 40}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Collection.tsx": {"language": "TypeScript JSX", "code": 376, "comment": 63, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Services.tsx": {"language": "TypeScript JSX", "code": 959, "comment": 29, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AddProductDialog/index.tsx": {"language": "TypeScript JSX", "code": 811, "comment": 68, "blank": 32}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/HotdealList.tsx": {"language": "TypeScript JSX", "code": 194, "comment": 21, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideProducts.tsx": {"language": "TypeScript JSX", "code": 553, "comment": 119, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/WhyUs.tsx": {"language": "TypeScript JSX", "code": 309, "comment": 6, "blank": 16}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/MoreAction/MoreAction.tsx": {"language": "TypeScript JSX", "code": 141, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/index.tsx": {"language": "TypeScript JSX", "code": 171, "comment": 10, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/NewProductsList.tsx": {"language": "TypeScript JSX", "code": 232, "comment": 98, "blank": 14}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Feedback.tsx": {"language": "TypeScript JSX", "code": 421, "comment": 41, "blank": 18}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/SignUp.tsx": {"language": "TypeScript JSX", "code": 501, "comment": 12, "blank": 40}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/FileUpload.tsx": {"language": "TypeScript JSX", "code": 86, "comment": 4, "blank": 10}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardPdf.tsx": {"language": "TypeScript JSX", "code": 197, "comment": 0, "blank": 11}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Partners.tsx": {"language": "TypeScript JSX", "code": 260, "comment": 83, "blank": 17}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Industry.tsx": {"language": "TypeScript JSX", "code": 379, "comment": 3, "blank": 7}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/SignIn.tsx": {"language": "TypeScript JSX", "code": 750, "comment": 12, "blank": 49}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardFolder.tsx": {"language": "TypeScript JSX", "code": 131, "comment": 1, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/components/MoreAction/ThreeAction.tsx": {"language": "TypeScript JSX", "code": 117, "comment": 1, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/index.tsx": {"language": "TypeScript JSX", "code": 488, "comment": 18, "blank": 27}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Loadable.tsx": {"language": "TypeScript JSX", "code": 5, "comment": 3, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/index.tsx": {"language": "TypeScript JSX", "code": 74, "comment": 8, "blank": 8}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Collection.tsx": {"language": "TypeScript JSX", "code": 376, "comment": 63, "blank": 12}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardImage.tsx": {"language": "TypeScript JSX", "code": 172, "comment": 1, "blank": 9}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/__tests__/i18n.test.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/dev/e-commerce/frontend/src/assets/images/button/Facebook_Messenger_logo_2020.svg": {"language": "XML", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/types.ts": {"language": "TypeScript", "code": 4, "comment": 12, "blank": 3}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/vi/translation.json": {"language": "JSON", "code": 15, "comment": 0, "blank": 1}, "file:///Users/<USER>/dev/e-commerce/frontend/src/locales/i18n.ts": {"language": "TypeScript", "code": 31, "comment": 6, "blank": 7}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Menu/detail.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/detail.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/detail.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 5}, "file:///Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/detail.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 5}}