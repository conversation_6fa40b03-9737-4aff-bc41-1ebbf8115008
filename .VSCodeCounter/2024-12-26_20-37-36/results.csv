"filename", "language", "TypeScript", "TypeScript JSX", "JSON", "XML", "CSS", "comment", "blank", "total"
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/AddToCart.tsx", "TypeScript JSX", 0, 104, 0, 0, 0, 4, 9, 117
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/BrandMenu.tsx", "TypeScript JSX", 0, 366, 0, 0, 0, 168, 26, 560
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/index.tsx", "TypeScript JSX", 0, 1090, 0, 0, 0, 184, 69, 1343
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/seoImage.tsx", "TypeScript JSX", 0, 18, 0, 0, 0, 0, 4, 22
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/AppWrapper/theme.tsx", "TypeScript JSX", 0, 12, 0, 0, 0, 0, 2, 14
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AddProductDialog/index.tsx", "TypeScript JSX", 0, 811, 0, 0, 0, 68, 32, 911
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/SignIn.tsx", "TypeScript JSX", 0, 750, 0, 0, 0, 12, 49, 811
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/SignUp.tsx", "TypeScript JSX", 0, 501, 0, 0, 0, 12, 40, 553
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Dialog/AuthDialog/index.tsx", "TypeScript JSX", 0, 171, 0, 0, 0, 10, 8, 189
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Footer/index.tsx", "TypeScript JSX", 0, 723, 0, 0, 0, 156, 14, 893
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/GooogleMaps/Maps.tsx", "TypeScript JSX", 0, 56, 0, 0, 0, 2, 9, 67
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/InputFileUpload/index.tsx", "TypeScript JSX", 0, 93, 0, 0, 0, 2, 8, 103
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/LoadingIndicator/index.tsx", "TypeScript JSX", 0, 56, 0, 0, 0, 0, 10, 66
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/MoreAction/MoreAction.tsx", "TypeScript JSX", 0, 141, 0, 0, 0, 0, 5, 146
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/MoreAction/ThreeAction.tsx", "TypeScript JSX", 0, 117, 0, 0, 0, 1, 5, 123
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/Recently/Recently.tsx", "TypeScript JSX", 0, 262, 0, 0, 0, 12, 13, 287
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/SEO/SEO.tsx", "TypeScript JSX", 0, 1, 0, 0, 0, 29, 1, 31
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/Account/index.tsx", "TypeScript JSX", 0, 122, 0, 0, 0, 3, 5, 130
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/OrderForm.tsx", "TypeScript JSX", 0, 80, 0, 0, 0, 2, 6, 88
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/SearchMenu/index.tsx", "TypeScript JSX", 0, 188, 0, 0, 0, 7, 5, 200
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/Top.css", "CSS", 0, 0, 0, 0, 251, 11, 19, 281
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/TopBar/index.tsx", "TypeScript JSX", 0, 748, 0, 0, 0, 755, 32, 1535
"/Users/<USER>/dev/e-commerce/frontend/src/app/components/ZoomImg/ZoomImage.tsx", "TypeScript JSX", 0, 90, 0, 0, 0, 3, 13, 106
"/Users/<USER>/dev/e-commerce/frontend/src/app/constants/API.ts", "TypeScript", 6, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/dev/e-commerce/frontend/src/app/constants/app.ts", "TypeScript", 10, 0, 0, 0, 0, 0, 2, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/hook/useWindowDimensions.tsx", "TypeScript JSX", 0, 21, 0, 0, 0, 0, 5, 26
"/Users/<USER>/dev/e-commerce/frontend/src/app/hook/useWindowScrollToTop.tsx", "TypeScript JSX", 0, 16, 0, 0, 0, 0, 4, 20
"/Users/<USER>/dev/e-commerce/frontend/src/app/index.tsx", "TypeScript JSX", 0, 74, 0, 0, 0, 8, 8, 90
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/AboutUs.tsx", "TypeScript JSX", 0, 155, 0, 0, 0, 2, 11, 168
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/BrandsList.tsx", "TypeScript JSX", 0, 189, 0, 0, 0, 4, 13, 206
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Collection.tsx", "TypeScript JSX", 0, 376, 0, 0, 0, 63, 12, 451
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Feedback.tsx", "TypeScript JSX", 0, 424, 0, 0, 0, 10, 18, 452
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/HotdealList.tsx", "TypeScript JSX", 0, 194, 0, 0, 0, 21, 11, 226
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Industry.tsx", "TypeScript JSX", 0, 208, 0, 0, 0, 3, 6, 217
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/NewProductsList.tsx", "TypeScript JSX", 0, 229, 0, 0, 0, 98, 14, 341
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/News.tsx", "TypeScript JSX", 0, 361, 0, 0, 0, 15, 19, 395
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Partners.tsx", "TypeScript JSX", 0, 211, 0, 0, 0, 76, 14, 301
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/Services.tsx", "TypeScript JSX", 0, 275, 0, 0, 0, 6, 9, 290
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideBannerList.tsx", "TypeScript JSX", 0, 170, 0, 0, 0, 9, 11, 190
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideLead.tsx", "TypeScript JSX", 0, 350, 0, 0, 0, 83, 17, 450
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideMiddleBanner.tsx", "TypeScript JSX", 0, 166, 0, 0, 0, 14, 11, 191
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/SlideProducts.tsx", "TypeScript JSX", 0, 285, 0, 0, 0, 65, 7, 357
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/WhyUs.tsx", "TypeScript JSX", 0, 242, 0, 0, 0, 5, 9, 256
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AboutPage/index.tsx", "TypeScript JSX", 0, 492, 0, 0, 0, 20, 36, 548
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AdminPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/AdminPage/index.tsx", "TypeScript JSX", 0, 2163, 0, 0, 0, 599, 85, 2847
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/about.ts", "TypeScript", 6, 0, 0, 0, 0, 0, 6, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/index.tsx", "TypeScript JSX", 0, 310, 0, 0, 0, 50, 12, 372
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/styles.css", "CSS", 0, 0, 0, 0, 45, 1, 10, 56
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ContactPage/toolbar.ts", "TypeScript", 1, 0, 0, 0, 0, 139, 2, 142
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/NewsLabel.tsx", "TypeScript JSX", 0, 85, 0, 0, 0, 4, 12, 101
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/SlideProductsList.tsx", "TypeScript JSX", 0, 266, 0, 0, 0, 73, 13, 352
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/detail.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 5, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/index.tsx", "TypeScript JSX", 0, 1726, 0, 0, 0, 272, 38, 2036
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/DetailPage/review.tsx", "TypeScript JSX", 0, 100, 0, 0, 0, 0, 5, 105
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardFolder.tsx", "TypeScript JSX", 0, 131, 0, 0, 0, 1, 8, 140
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardImage.tsx", "TypeScript JSX", 0, 172, 0, 0, 0, 1, 9, 182
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/CardPdf.tsx", "TypeScript JSX", 0, 197, 0, 0, 0, 0, 11, 208
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/FileUpload.tsx", "TypeScript JSX", 0, 86, 0, 0, 0, 4, 10, 100
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/FilePage/index.tsx", "TypeScript JSX", 0, 488, 0, 0, 0, 18, 27, 533
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/AboutUs.tsx", "TypeScript JSX", 0, 299, 0, 0, 0, 4, 12, 315
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/BrandsList.tsx", "TypeScript JSX", 0, 233, 0, 0, 0, 14, 12, 259
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Collection.tsx", "TypeScript JSX", 0, 376, 0, 0, 0, 63, 12, 451
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Feedback.tsx", "TypeScript JSX", 0, 421, 0, 0, 0, 41, 18, 480
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/HotdealList.tsx", "TypeScript JSX", 0, 194, 0, 0, 0, 21, 11, 226
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Industry.tsx", "TypeScript JSX", 0, 379, 0, 0, 0, 3, 7, 389
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/NewProductsList.tsx", "TypeScript JSX", 0, 232, 0, 0, 0, 98, 14, 344
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/News.tsx", "TypeScript JSX", 0, 361, 0, 0, 0, 15, 19, 395
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Partners.tsx", "TypeScript JSX", 0, 260, 0, 0, 0, 83, 17, 360
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/Services.tsx", "TypeScript JSX", 0, 959, 0, 0, 0, 29, 10, 998
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideBannerList.tsx", "TypeScript JSX", 0, 170, 0, 0, 0, 9, 11, 190
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideLead.tsx", "TypeScript JSX", 0, 350, 0, 0, 0, 83, 17, 450
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideMiddleBanner.tsx", "TypeScript JSX", 0, 166, 0, 0, 0, 14, 11, 191
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/SlideProducts.tsx", "TypeScript JSX", 0, 553, 0, 0, 0, 119, 8, 680
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/WhyUs.tsx", "TypeScript JSX", 0, 309, 0, 0, 0, 6, 16, 331
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/HomePage/index.tsx", "TypeScript JSX", 0, 426, 0, 0, 0, 56, 40, 522
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Menu/detail.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 5, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/Menu/filter.tsx", "TypeScript JSX", 0, 93, 0, 0, 0, 0, 5, 98
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/News.tsx", "TypeScript JSX", 0, 186, 0, 0, 0, 191, 18, 395
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ListProduct/index.tsx", "TypeScript JSX", 0, 1181, 0, 0, 0, 66, 31, 1278
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/detail.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 5, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsInfoPage/index.tsx", "TypeScript JSX", 0, 686, 0, 0, 0, 23, 34, 743
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/detail.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 5, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NewsPage/index.tsx", "TypeScript JSX", 0, 2313, 0, 0, 0, 86, 38, 2437
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/P.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 2, 9
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/NotFoundPage/index.tsx", "TypeScript JSX", 0, 42, 0, 0, 0, 0, 4, 46
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/index.tsx", "TypeScript JSX", 0, 664, 0, 0, 0, 406, 28, 1098
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/OrderPage/review.tsx", "TypeScript JSX", 0, 99, 0, 0, 0, 0, 5, 104
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/BrandsList.tsx", "TypeScript JSX", 0, 163, 0, 0, 0, 36, 13, 212
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/BrandsList2.tsx", "TypeScript JSX", 0, 160, 0, 0, 0, 5, 13, 178
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Category.tsx", "TypeScript JSX", 0, 169, 0, 0, 0, 4, 10, 183
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Collection.tsx", "TypeScript JSX", 0, 376, 0, 0, 0, 63, 12, 451
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Feedback.tsx", "TypeScript JSX", 0, 424, 0, 0, 0, 10, 18, 452
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/HotDeal.tsx", "TypeScript JSX", 0, 136, 0, 0, 0, 2, 13, 151
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/HotdealList.tsx", "TypeScript JSX", 0, 194, 0, 0, 0, 21, 11, 226
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Industry.tsx", "TypeScript JSX", 0, 208, 0, 0, 0, 3, 6, 217
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/NewProductsList.tsx", "TypeScript JSX", 0, 229, 0, 0, 0, 98, 14, 341
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/News.tsx", "TypeScript JSX", 0, 186, 0, 0, 0, 191, 18, 395
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Partners.tsx", "TypeScript JSX", 0, 190, 0, 0, 0, 76, 12, 278
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/Services.tsx", "TypeScript JSX", 0, 275, 0, 0, 0, 6, 9, 290
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideBannerList.tsx", "TypeScript JSX", 0, 170, 0, 0, 0, 9, 11, 190
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideLead.tsx", "TypeScript JSX", 0, 238, 0, 0, 0, 131, 17, 386
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideMiddleBanner.tsx", "TypeScript JSX", 0, 166, 0, 0, 0, 14, 11, 191
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/SlideProducts.tsx", "TypeScript JSX", 0, 518, 0, 0, 0, 104, 7, 629
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/WhyUs.tsx", "TypeScript JSX", 0, 242, 0, 0, 0, 5, 9, 256
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/ProductPage/index.tsx", "TypeScript JSX", 0, 280, 0, 0, 0, 191, 25, 496
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/Collection.tsx", "TypeScript JSX", 0, 343, 0, 0, 0, 5, 12, 360
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/NewProductsList.tsx", "TypeScript JSX", 0, 270, 0, 0, 0, 12, 11, 293
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/SlideBannerList.tsx", "TypeScript JSX", 0, 140, 0, 0, 0, 3, 11, 154
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/PromotionPage/index.tsx", "TypeScript JSX", 0, 452, 0, 0, 0, 13, 24, 489
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/Loadable.tsx", "TypeScript JSX", 0, 5, 0, 0, 0, 3, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/about.ts", "TypeScript", 6, 0, 0, 0, 0, 0, 6, 12
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/index.tsx", "TypeScript JSX", 0, 276, 0, 0, 0, 8, 15, 299
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/styles.css", "CSS", 0, 0, 0, 0, 45, 1, 10, 56
"/Users/<USER>/dev/e-commerce/frontend/src/app/pages/TermsPage/toolbar.ts", "TypeScript", 1, 0, 0, 0, 0, 139, 2, 142
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/index.ts", "TypeScript", 53, 0, 0, 0, 0, 10, 5, 68
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/saga.ts", "TypeScript", 39, 0, 0, 0, 0, 27, 5, 71
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/selectors.ts", "TypeScript", 5, 0, 0, 0, 0, 1, 4, 10
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/cart/types.ts", "TypeScript", 11, 0, 0, 0, 0, 1, 3, 15
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/index.ts", "TypeScript", 118, 0, 0, 0, 0, 36, 6, 160
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/saga.ts", "TypeScript", 132, 0, 0, 0, 0, 25, 8, 165
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/selectors.ts", "TypeScript", 5, 0, 0, 0, 0, 1, 4, 10
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/user/types.ts", "TypeScript", 22, 0, 0, 0, 0, 2, 4, 28
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/index.ts", "TypeScript", 31, 0, 0, 0, 0, 6, 5, 42
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/localStorage.ts", "TypeScript", 81, 0, 0, 0, 0, 1, 21, 103
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/selectors.ts", "TypeScript", 20, 0, 0, 0, 0, 1, 5, 26
"/Users/<USER>/dev/e-commerce/frontend/src/app/slice/utils/types.ts", "TypeScript", 24, 0, 0, 0, 0, 3, 6, 33
"/Users/<USER>/dev/e-commerce/frontend/src/assets/images/button/Facebook_Messenger_logo_2020.svg", "XML", 0, 0, 0, 2, 0, 0, 1, 3
"/Users/<USER>/dev/e-commerce/frontend/src/assets/images/card-icon.svg", "XML", 0, 0, 0, 19, 0, 0, 1, 20
"/Users/<USER>/dev/e-commerce/frontend/src/index.tsx", "TypeScript JSX", 0, 29, 0, 0, 0, 16, 14, 59
"/Users/<USER>/dev/e-commerce/frontend/src/locales/__tests__/i18n.test.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 2, 9
"/Users/<USER>/dev/e-commerce/frontend/src/locales/en/translation.json", "JSON", 0, 0, 15, 0, 0, 0, 1, 16
"/Users/<USER>/dev/e-commerce/frontend/src/locales/i18n.ts", "TypeScript", 31, 0, 0, 0, 0, 6, 7, 44
"/Users/<USER>/dev/e-commerce/frontend/src/locales/translations.ts", "TypeScript", 22, 0, 0, 0, 0, 10, 3, 35
"/Users/<USER>/dev/e-commerce/frontend/src/locales/types.ts", "TypeScript", 4, 0, 0, 0, 0, 12, 3, 19
"/Users/<USER>/dev/e-commerce/frontend/src/locales/vi/translation.json", "JSON", 0, 0, 15, 0, 0, 0, 1, 16
"/Users/<USER>/dev/e-commerce/frontend/src/react-app-env.d.ts", "TypeScript", 0, 0, 0, 0, 0, 3, 2, 5
"/Users/<USER>/dev/e-commerce/frontend/src/reportWebVitals.ts", "TypeScript", 13, 0, 0, 0, 0, 0, 3, 16
"/Users/<USER>/dev/e-commerce/frontend/src/setupTests.ts", "TypeScript", 4, 0, 0, 0, 0, 4, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/store/__tests__/configureStore.test.ts", "TypeScript", 17, 0, 0, 0, 0, 0, 3, 20
"/Users/<USER>/dev/e-commerce/frontend/src/store/__tests__/reducer.test.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 3, 22
"/Users/<USER>/dev/e-commerce/frontend/src/store/configureStore.ts", "TypeScript", 27, 0, 0, 0, 0, 4, 8, 39
"/Users/<USER>/dev/e-commerce/frontend/src/store/reducers.ts", "TypeScript", 11, 0, 0, 0, 0, 7, 4, 22
"/Users/<USER>/dev/e-commerce/frontend/src/styles/__tests__/media.test.ts", "TypeScript", 13, 0, 0, 0, 0, 0, 2, 15
"/Users/<USER>/dev/e-commerce/frontend/src/styles/global-styles.ts", "TypeScript", 48, 0, 0, 0, 0, 51, 13, 112
"/Users/<USER>/dev/e-commerce/frontend/src/styles/media.ts", "TypeScript", 13, 0, 0, 0, 0, 17, 5, 35
"/Users/<USER>/dev/e-commerce/frontend/src/types/RootState.ts", "TypeScript", 8, 0, 0, 0, 0, 6, 2, 16
"/Users/<USER>/dev/e-commerce/frontend/src/types/index.ts", "TypeScript", 5, 0, 0, 0, 0, 0, 3, 8
"/Users/<USER>/dev/e-commerce/frontend/src/utils/@reduxjs/toolkit.tsx", "TypeScript JSX", 0, 15, 0, 0, 0, 2, 3, 20
"/Users/<USER>/dev/e-commerce/frontend/src/utils/LocalStorage.ts", "TypeScript", 35, 0, 0, 0, 0, 6, 2, 43
"/Users/<USER>/dev/e-commerce/frontend/src/utils/app.ts", "TypeScript", 25, 0, 0, 0, 0, 1, 5, 31
"/Users/<USER>/dev/e-commerce/frontend/src/utils/convert.ts", "TypeScript", 8, 0, 0, 0, 0, 0, 3, 11
"/Users/<USER>/dev/e-commerce/frontend/src/utils/jodit.ts", "TypeScript", 183, 0, 0, 0, 0, 62, 19, 264
"/Users/<USER>/dev/e-commerce/frontend/src/utils/loadable.tsx", "TypeScript JSX", 0, 25, 0, 0, 0, 0, 6, 31
"/Users/<USER>/dev/e-commerce/frontend/src/utils/messages.ts", "TypeScript", 6, 0, 0, 0, 0, 7, 1, 14
"/Users/<USER>/dev/e-commerce/frontend/src/utils/moment.ts", "TypeScript", 67, 0, 0, 0, 0, 0, 11, 78
"/Users/<USER>/dev/e-commerce/frontend/src/utils/redux-injectors.ts", "TypeScript", 17, 0, 0, 0, 0, 1, 4, 22
"/Users/<USER>/dev/e-commerce/frontend/src/utils/regexp.ts", "TypeScript", 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/dev/e-commerce/frontend/src/utils/request.ts", "TypeScript", 42, 0, 0, 0, 0, 7, 6, 55
"/Users/<USER>/dev/e-commerce/frontend/src/utils/s3.ts", "TypeScript", 85, 0, 0, 0, 0, 14, 18, 117
"/Users/<USER>/dev/e-commerce/frontend/src/utils/types/injector-typings.ts", "TypeScript", 18, 0, 0, 0, 0, 0, 5, 23
"/Users/<USER>/dev/e-commerce/frontend/src/utils/uploadImg.ts", "TypeScript", 19, 0, 0, 0, 0, 2, 4, 25
"Total", "-", 1358, 33558, 30, 21, 341, 6079, 1901, 43288