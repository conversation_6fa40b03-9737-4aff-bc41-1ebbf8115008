{"typescript.tsdk": "node_modules/typescript/lib", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "javascript.format.enable": false}