{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "11.11.3", "@emotion/styled": "11.11.0", "@mui/icons-material": "5.15.6", "@mui/lab": "5.0.0-alpha.169", "@mui/material": "5.15.14", "@mui/x-tree-view": "7.0.0", "@radix-ui/react-dialog": "1.1.6", "@react-google-maps/api": "2.19.3", "@reduxjs/toolkit": "1.8.5", "@tailwindcss/cli": "4.0.9", "@tanstack/react-table": "8.17.0", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@types/dompurify": "3.0.5", "@types/draft-convert": "2.1.8", "@types/draft-js": "0.11.18", "@types/fontfaceobserver": "^2.1.0", "@types/jest": "^27.5.2", "@types/node": "^14.18.27", "@types/react": "^18.0.20", "@types/react-dom": "^18.0.6", "@types/react-draft-wysiwyg": "1.13.8", "@types/react-redux": "^7.1.24", "@types/react-test-renderer": "^18.0.0", "@types/rimraf": "^3.0.2", "@types/shelljs": "^0.8.11", "@types/styled-components": "^5.1.26", "@types/testing-library__jest-dom": "^5.14.5", "@types/webpack": "^5.28.0", "@types/webpack-env": "^1.18.0", "aws-sdk": "2.1565.0", "axios": "1.6.5", "chalk": "4.1.2", "cross-env": "7.0.3", "dompurify": "3.0.9", "draft-convert": "2.1.13", "eslint-config-prettier": "8.5.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react-hooks": "4.6.0", "fontfaceobserver": "2.3.0", "gatsby-plugin-react-helmet-async": "1.2.3", "husky": "8.0.1", "i18next": "21.9.2", "i18next-browser-languagedetector": "6.1.5", "i18next-scanner": "4.0.0", "inquirer": "7.3.3", "inquirer-directory": "2.2.0", "jest-styled-components": "7.1.1", "jodit-react": "4.0.15", "lint-staged": "13.0.3", "lucide-react": "0.477.0", "moment-timezone": "0.5.45", "node-plop": "0.26.3", "notistack": "3.0.1", "plop": "2.7.6", "prettier": "2.7.1", "react": "18.2.0", "react-app-polyfill": "3.0.0", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-facebook": "9.0.12", "react-helmet": "6.1.0", "react-helmet-async": "1.3.0", "react-i18next": "11.18.6", "react-icons": "5.3.0", "react-is": "18.2.0", "react-redux": "7.2.8", "react-router-dom": "6.3.0", "react-schemaorg": "2.0.0", "react-scripts": "5.0.1", "react-test-renderer": "18.2.0", "react-youtube": "10.1.0", "redux-injectors": "1.3.0", "redux-saga": "1.2.1", "rimraf": "3.0.2", "sanitize.css": "13.0.0", "schema-dts": "1.1.2", "serve": "14.0.1", "shelljs": "0.8.5", "styled-components": "5.3.5", "stylelint": "14.12.0", "stylelint-config-recommended": "9.0.0", "stylelint-config-styled-components": "0.1.1", "stylelint-processor-styled-components": "1.10.0", "swiper": "10.3.1", "tailwind-merge": "3.0.2", "tailwindcss": "4.0.9", "ts-node": "10.9.1", "typescript": "4.6.4", "web-vitals": "2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start:prod": "yarn run build && serve -s build", "test:generators": "ts-node ./internals/testing/generators/test-generators.ts", "checkTs": "tsc --noEmit", "eslint": "eslint --ext js,ts,tsx", "lint": "yarn run eslint src", "lint:fix": "yarn run eslint --fix src", "lint:css": "stylelint src/**/*.css", "generate": "plop --plopfile internals/generators/plopfile.ts", "prettify": "prettier --write", "extract-messages": "i18next-scanner --config=internals/extractMessages/i18next-scanner.config.js", "prepare": "husky install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=14.x"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["yarn run eslint --fix"], "*.{md,json}": ["prettier --write"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*/*.d.ts", "!src/**/*/Loadable.{js,jsx,ts,tsx}", "!src/**/*/messages.ts", "!src/**/*/types.ts", "!src/index.tsx"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}, "devDependencies": {"@types/lodash": "4.14.202", "@types/react-helmet": "6.1.11", "react-draggable": "4.4.6", "validator": "13.12.0"}}